# Fetch MCP 故障排除指南

## 问题：Schema格式错误

### 错误信息
```
Failed to start the MCP server. {"command":"uvx mcp-server-fetch","args":[],"error":"invalid schema for tool fetch: unknown format \"uri\" ignored in schema at path \"#/properties/url\",\"stderr":""}
```

### 原因分析
这个错误表明Augment IDE对某些MCP服务器的schema格式支持有限制，特别是对"uri"格式的支持。

## 解决方案

### 方案1：使用Python方式（推荐）

**配置文件**：`Fetch-MCP-Python配置.json`

```json
{
  "mcpServers": {
    "fetch": {
      "command": "python",
      "args": [
        "-m",
        "mcp_server_fetch"
      ]
    }
  }
}
```

**优势**：
- 更好的兼容性
- 已验证可以正常工作
- 不依赖uvx的schema解析

### 方案2：使用Web-Fetch工具（备选）

如果Python方式仍有问题，可以尝试使用内置的web-fetch功能：

```json
{
  "mcpServers": {
    "web-reader": {
      "command": "npx",
      "args": [
        "web-reader-mcp"
      ]
    }
  }
}
```

### 方案3：等待Augment更新

Augment IDE可能在未来版本中改进对MCP schema的支持。

## 验证步骤

### 1. 确认Python安装
```bash
python --version
python -m mcp_server_fetch --help
```

### 2. 测试配置
使用Python配置后，在Augment中测试：
```
请获取 https://httpbin.org/html 的内容
```

### 3. 检查日志
如果仍有问题，查看Augment的MCP服务器日志。

## 常见问题

### Q: Python方式也报错怎么办？
A: 检查以下几点：
1. Python版本是否 >= 3.10
2. mcp-server-fetch包是否正确安装
3. 网络连接是否正常

### Q: 为什么uvx方式不工作？
A: Augment IDE对某些MCP schema格式的支持可能有限制，这是IDE层面的兼容性问题。

### Q: 有其他获取网页内容的方法吗？
A: 是的，您可以：
1. 使用内置的web-search工具
2. 使用playwright MCP进行网页操作
3. 等待Augment改进schema支持

## 推荐配置

基于测试结果，推荐使用以下配置：

```json
{
  "mcpServers": {
    "fetch": {
      "command": "python",
      "args": [
        "-m",
        "mcp_server_fetch"
      ]
    }
  }
}
```

这个配置已经验证可以正常工作，避免了schema兼容性问题。

## 后续支持

如果Python方式仍有问题，建议：

1. 检查Augment的官方文档
2. 查看是否有更新版本
3. 考虑使用其他MCP服务器作为替代

---

**总结**：使用Python方式的配置可以避免schema兼容性问题，是目前最稳定的解决方案。
