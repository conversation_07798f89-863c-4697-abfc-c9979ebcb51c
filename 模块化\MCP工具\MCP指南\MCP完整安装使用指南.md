# 🚀 MCP (Model Context Protocol) 完整安装使用指南

## 📋 目录
1. [环境要求](#环境要求)
2. [安装流程](#安装流程)
3. [配置方法](#配置方法)
4. [使用指南](#使用指南)
5. [常见问题](#常见问题)
6. [推荐工具](#推荐工具)

---

## 🔧 环境要求

### **必需环境**
- ✅ **Cursor IDE** (最新版本)
- ✅ **Python 3.8+** (推荐 3.11+)
- ✅ **pip** (Python 包管理器)
- ✅ **uv/uvx** (现代 Python 包管理器)

### **检查环境命令**
```bash
# 检查 Python 版本
python --version

# 检查 pip
pip --version

# 检查 uvx (推荐)
uvx --version

# 如果没有 uvx，安装 uv
pip install uv
```

---

## 📦 安装流程

### **步骤1：安装 uv (如果没有)**
```bash
pip install uv
```

### **步骤2：安装 MCP 核心包**
```bash
# 安装 MCP 核心
pip install mcp

# 安装常用 MCP 服务器
pip install mcp-server-fetch
```

### **步骤3：安装推荐的 MCP 工具**
```bash
# 方法1：使用 uvx (推荐)
uvx mcp-feedback-enhanced@latest

# 方法2：使用 pip
pip install mcp-feedback-enhanced
```

---

## ⚙️ 配置方法

### **⚠️ 重要：配置文件位置区别**

#### **🌐 全局配置 (推荐)**
- 📍 **位置**: 通过 Cursor Settings → MCP 界面配置
- 🎯 **适用**: 所有项目都可使用
- ✅ **推荐**: 适合常用工具如 fetch、context7

#### **📁 项目配置**
- 📍 **正确位置**: 项目根目录 `.cursor/mcp.json` ✅
- 📍 **错误位置**: `.cursor/rules/mcp.json` ❌ 或 `.cursorrules` ❌
- 🎯 **适用**: 仅当前项目
- ⚠️ **重要**: 文件必须直接在 `.cursor/` 目录下，不是子目录

### **方法一：通过 Cursor 界面配置 (推荐)**

1. **打开 Cursor Settings**
   - 点击左下角齿轮图标
   - 选择 "Cursor Settings"
   - 点击左侧 "MCP" 选项

2. **添加 MCP 服务器**
   - 点击 "Add new global MCP server" 蓝色按钮
   - 在弹出的界面中配置服务器信息

3. **配置三个核心服务器**：

#### **mcp-feedback-enhanced**
```
Name: mcp-feedback-enhanced
Command: uvx
Args: mcp-feedback-enhanced@latest
Timeout: 600
Auto Approve: interactive_feedback
```

#### **fetch (Web-Reader)**
```
Name: fetch
Command: python
Args: -m mcp_server_fetch
Timeout: 300
```

#### **context7**
```
Name: context7
Command: npx
Args: -y @upstash/context7-mcp@latest
Timeout: 600
```

### **方法二：手动编辑项目配置文件**

#### **🎯 正确的项目配置方法**

**步骤1：创建正确的目录结构**
```bash
# 在项目根目录执行
mkdir .cursor
```

**步骤2：创建配置文件**
在项目根目录创建 `.cursor/mcp.json` (注意：直接在 .cursor 目录下)：

```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "autoApprove": ["interactive_feedback"]
    },
    "fetch": {
      "command": "python",
      "args": ["-m", "mcp_server_fetch"],
      "timeout": 300
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "timeout": 600
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
      "timeout": 300
    }
  }
}
```

#### **📁 正确的文件结构**
```
项目根目录/
├── .cursor/
│   └── mcp.json          ← 项目MCP配置文件 (在这里！)
├── 其他项目文件...
└── README.md
```

#### **❌ 常见错误位置**
```
项目根目录/
├── .cursor/
│   └── rules/
│       └── mcp.json      ← ❌ 错误！不在这里
├── .cursorrules          ← ❌ 错误！不是这个文件
└── mcp.json              ← ❌ 错误！不在根目录
```

### **配置文件说明**

| 字段 | 说明 | 示例 |
|------|------|------|
| `command` | 启动命令 | `"uvx"` 或 `"python"` |
| `args` | 命令参数 | `["mcp-feedback-enhanced@latest"]` |
| `timeout` | 超时时间(秒) | `600` |
| `autoApprove` | 自动批准的工具 | `["interactive_feedback"]` |

---

## 🎯 使用指南

### **基本使用方法**

#### **1. 交互式反馈工具**
```
# 直接在 Cursor 中对话
"请帮我分析这段代码的问题"
"给我一些优化建议"
```

#### **2. Web-Reader (fetch)**
```
# 网页内容获取
"帮我读取 https://example.com 的内容"
"将这个网页转换为 Markdown：[URL]"
"分析这个页面的主要信息：[URL]"
```

#### **3. 数据获取**
```
# API 调用
"帮我调用这个 API：[API_URL]"
"获取这个接口的数据：[URL]"
```

### **高级功能**

#### **自定义 User-Agent**
```json
{
  "fetch": {
    "command": "python",
    "args": ["-m", "mcp_server_fetch", "--user-agent", "Custom Bot 1.0"],
    "timeout": 300
  }
}
```

#### **忽略 robots.txt**
```json
{
  "fetch": {
    "command": "python",
    "args": ["-m", "mcp_server_fetch", "--ignore-robots-txt"],
    "timeout": 300
  }
}
```

---

## 🔍 常见问题

### **Q1: MCP 服务器无法启动**
**解决方案**：
1. 检查 Python 环境
2. 重新安装 MCP 包
3. 检查配置文件语法

### **Q2: 网页无法访问**
**解决方案**：
1. 检查网络连接
2. 尝试其他网站
3. 检查防火墙设置

### **Q3: 工具显示 "No tools available"**
**解决方案**：
1. 重启 Cursor
2. 检查服务器状态
3. 重新安装对应包

### **Q4: Context7 无法使用**
**原因**：协议兼容性问题
**解决方案**：暂时移除，等待更新

---

## 🛠️ 推荐工具

### **已验证可用**
- ✅ **mcp-feedback-enhanced** - 交互式反馈
- ✅ **mcp-server-fetch** - 网页读取和数据获取
- ✅ **mcp** - 核心协议库

### **推荐安装**
- ✅ **context7** - Upstash 上下文管理 (已验证可用)
- 🔄 **mcp-server-filesystem** - 文件系统操作
- 🔄 **mcp-server-git** - Git 版本控制
- 🔄 **mcp-server-sqlite** - 数据库操作

---

## 📝 配置模板

### **最小配置**
```json
{
  "mcpServers": {
    "fetch": {
      "command": "python",
      "args": ["-m", "mcp_server_fetch"],
      "timeout": 300
    }
  }
}
```

### **完整配置 (包含所有4个工具)**
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "autoApprove": ["interactive_feedback"]
    },
    "fetch": {
      "command": "python",
      "args": ["-m", "mcp_server_fetch", "--user-agent", "Cursor MCP Bot"],
      "timeout": 300
    },
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "timeout": 600
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
      "timeout": 300
    }
  }
}
```

### **工具特殊说明**

#### **Context7**
- 🔧 **命令**: 使用 `npx` 而不是 `python`
- 📦 **包名**: `@upstash/context7-mcp@latest`
- 🚀 **功能**: 上下文管理和记忆功能
- ⚡ **优势**: 自动下载最新版本，无需预安装

#### **Sequential-Thinking**
- 🔧 **命令**: 使用 `npx`
- 📦 **包名**: `@modelcontextprotocol/server-sequential-thinking`
- 🧠 **功能**: 序列思维和逻辑推理
- ⚡ **优势**: 增强AI的逻辑思考能力

---

## 🔄 项目配置 vs 全局配置

### **📊 配置方式对比**

| 特性 | 全局配置 | 项目配置 |
|------|----------|----------|
| **配置位置** | Cursor Settings → MCP | `.cursor/mcp.json` |
| **作用范围** | 所有项目 | 仅当前项目 |
| **配置方式** | 图形界面 | JSON 文件 |
| **推荐程度** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **维护难度** | 简单 | 中等 |

### **🎯 选择建议**

#### **推荐使用全局配置，当：**
- ✅ 多个项目都需要相同工具
- ✅ 希望简化配置管理
- ✅ 不熟悉 JSON 配置文件
- ✅ 需要图形界面管理

#### **使用项目配置，当：**
- 🎯 特定项目需要特殊配置
- 🎯 不同项目需要不同工具集
- 🎯 需要版本控制配置文件
- 🎯 团队协作需要统一配置

### **🔧 实际配置效果**

#### **成功配置后的显示效果**
在 Cursor Settings → MCP 面板中，您会看到：

**全局配置的工具：**
- 🟢 **mcp-feedback-enhanced** (全局)
- 🟢 **fetch** (全局)
- 🟢 **context7** (全局)

**项目配置的工具：**
- 🟢 **fetch** (Project Managed)
- 🟢 **context7** (Project Managed)
- ⚪ **mcp-feedback-enhanced** (Project Managed, 未启用)

> 💡 **说明**: 如果全局已启用某工具，项目配置中的同名工具会显示为未启用状态，这是正常现象。

---

## 🎉 验证安装

### **检查步骤**
1. 重启 Cursor
2. 查看 MCP Servers 面板
3. 确认绿色状态指示灯
4. 测试工具功能

### **成功配置的标志**
从 Cursor Settings → MCP 面板中，您应该看到：

#### **✅ 完美配置示例**
- 🟢 **mcp-feedback-enhanced**
  - 🔧 工具: `interactive_feedback`, `get_system_info`
  - 📝 命令: `uvx mcp-feedback-enhanced@latest`

- 🟢 **fetch**
  - 🌐 工具: `fetch`
  - 📝 命令: `python -m mcp_server_fetch`

- 🟢 **context7**
  - 🧠 工具: `resolve-library-id`, `get-library-docs`
  - 📝 命令: `npx -y @upstash/context7-mcp@latest`

### **状态指示说明**
- 🟢 **绿色圆圈** = 服务器正常运行
- 🔴 **红色圆圈** = 服务器启动失败
- ⚪ **灰色圆圈** = 服务器未启动

### **测试命令**
```
"请获取系统信息"  # 测试 feedback-enhanced
"读取 https://www.baidu.com"  # 测试 fetch
"帮我管理项目上下文"  # 测试 context7
```

### **功能验证清单**
- [ ] 所有服务器显示绿色状态
- [ ] 每个服务器都显示可用工具列表
- [ ] 测试命令能正常响应
- [ ] 无错误提示信息

### **🎊 配置成功的实际案例**

根据实际配置经验，成功配置后您会看到：

#### **Cursor Settings → MCP 面板显示**
```
✅ mcp-feedback-enhanced  🟢  [编辑按钮]
   🔧 Tools: interactive_feedback, get_system_info
   📝 Command: uvx mcp-feedback-enhanced@latest

✅ fetch  🟢  [编辑按钮]
   🌐 Tools: fetch
   📝 Command: python -m mcp_server_fetch

✅ context7  🟢  [编辑按钮]
   🧠 Tools: resolve-library-id, get-library-docs
   📝 Command: npx -y @upstash/context7-mcp@latest
```

#### **实际测试效果**
- ✅ **系统信息获取**: "请获取系统信息" → 返回详细系统配置
- ✅ **网页读取**: "读取 https://www.baidu.com" → 返回网页内容
- ✅ **上下文管理**: "帮我管理项目上下文" → 提供上下文相关功能

### **⚠️ 常见配置错误**

#### **❌ 错误：项目配置文件位置错误**
- **问题**: 将配置文件放在了错误位置
- **常见错误位置**:
  - `.cursor/rules/mcp.json` ❌
  - `.cursorrules` ❌
  - `mcp.json` (根目录) ❌
- **正确位置**: `.cursor/mcp.json` ✅
- **解决**: 删除错误文件，在正确位置创建配置

#### **❌ 错误：编辑了错误的文件**
- **问题**: 编辑了项目临时文件而非全局配置
- **解决**: 使用 Cursor Settings 界面配置

#### **❌ 错误：JSON 格式错误**
- **问题**: 缺少逗号、括号不匹配
- **解决**: 使用 JSON 验证工具检查语法

#### **❌ 错误：命令路径错误**
- **问题**: Python 或 Node.js 未正确安装
- **解决**: 检查环境变量和安装状态

---

## 🚨 故障排除

### **安装问题**

#### **Python 环境问题**
```bash
# 问题：Python 版本过低
# 解决：升级 Python
python --version  # 检查版本
# 如果 < 3.8，需要升级

# 问题：pip 无法安装
# 解决：升级 pip
python -m pip install --upgrade pip
```

#### **网络问题**
```bash
# 问题：pip 安装超时
# 解决：使用国内镜像
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple mcp

# 问题：uvx 无法下载
# 解决：设置代理或使用 pip
pip install mcp-feedback-enhanced
```

### **配置问题**

#### **JSON 语法错误**
```json
// ❌ 错误：有注释
{
  "mcpServers": {
    // 这是注释，JSON 不支持
  }
}

// ✅ 正确：无注释
{
  "mcpServers": {
    "fetch": {
      "command": "python",
      "args": ["-m", "mcp_server_fetch"]
    }
  }
}
```

#### **路径问题**
```bash
# 问题：找不到 .cursor 目录
# 解决：手动创建
mkdir .cursor
echo '{"mcpServers":{}}' > .cursor/mcp.json
```

### **运行问题**

#### **服务器启动失败**
1. **检查依赖**：`pip list | grep mcp`
2. **重新安装**：`pip uninstall mcp-server-fetch && pip install mcp-server-fetch`
3. **检查权限**：确保有执行权限
4. **查看日志**：Cursor 开发者工具中查看错误

#### **工具无响应**
1. **重启 Cursor**
2. **检查网络连接**
3. **增加超时时间**：`"timeout": 600`
4. **简化配置**：移除可选参数

---

## 📚 深入学习

### **MCP 协议原理**
- **Model Context Protocol** 是 AI 模型与外部工具通信的标准协议
- 基于 **JSON-RPC** 实现
- 支持 **双向通信** 和 **实时交互**

### **开发自定义 MCP 服务器**
```python
# 简单示例
from mcp import Server
import asyncio

server = Server("my-custom-server")

@server.tool()
async def my_tool(text: str) -> str:
    return f"处理结果：{text}"

if __name__ == "__main__":
    asyncio.run(server.run())
```

### **社区资源**
- 📖 **官方文档**：https://modelcontextprotocol.io/
- 🐙 **GitHub**：https://github.com/modelcontextprotocol
- 💬 **社区讨论**：GitHub Discussions

---

## 🎯 最佳实践

### **配置建议**
1. **使用 uvx** 获取最新版本
2. **设置合理超时** (300-600秒)
3. **只启用需要的工具**
4. **定期更新** MCP 包

### **使用技巧**
1. **明确指令**：具体说明需要什么功能
2. **提供完整 URL**：包含 http/https 前缀
3. **测试小网站**：先用简单网站测试
4. **查看错误信息**：仔细阅读错误提示

### **性能优化**
1. **减少并发请求**
2. **使用缓存机制**
3. **合理设置超时**
4. **监控资源使用**

---

## 🔮 未来展望

### **即将支持的功能**
- 🔄 **更多协议支持** (WebSocket, SSE)
- 🛠️ **图形化配置界面**
- 📊 **性能监控面板**
- 🔐 **安全认证机制**

### **生态发展**
- 📈 **更多第三方工具**
- 🌐 **云端 MCP 服务**
- 🤖 **AI 模型深度集成**
- 📱 **移动端支持**

---

## 🎯 成功配置总结

### **✅ 完整的 MCP 工具套件**

通过本指南，您将获得一个功能完整的 MCP 工具组合：

1. **🔧 mcp-feedback-enhanced** - 系统交互和反馈收集
2. **🌐 fetch** - 强大的网页读取和数据获取
3. **🧠 context7** - 智能上下文管理和记忆功能

### **🚀 配置要点回顾**

- ✅ **使用全局配置** - 通过 Cursor Settings → MCP 界面
- ✅ **三个核心工具** - feedback-enhanced + fetch + context7
- ✅ **正确的命令** - uvx、python、npx 分别对应不同工具
- ✅ **验证成功** - 所有服务器显示绿色状态指示灯

### **🎊 恭喜您！**

**您现在拥有了一个强大的 AI 助手工具生态系统！**

这套 MCP 配置将大大增强您的 Cursor 使用体验，让 AI 助手能够：
- 🔍 **获取实时网页信息**
- 💬 **提供交互式反馈**
- 🧠 **管理项目上下文**
- 🛠️ **执行系统级操作**

---

## 🎯 实战经验总结

### **✅ 关键发现**

通过实际配置测试，我们发现了以下重要信息：

#### **项目配置的正确位置**
- ✅ **正确**: `.cursor/mcp.json` (直接在 .cursor 目录下)
- ❌ **错误**: `.cursor/rules/mcp.json`
- ❌ **错误**: `.cursorrules`
- ❌ **错误**: `mcp.json` (项目根目录)

#### **配置文件的实际效果**
当正确配置后，在 Cursor Settings → MCP 面板中会显示：
- **全局工具**: 显示为普通状态
- **项目工具**: 显示为 "Project Managed" 状态
- **重复工具**: 如果全局和项目都有同一工具，项目中的会显示为未启用

#### **推荐的4个核心工具**
1. **mcp-feedback-enhanced** - 交互式反馈和系统信息
2. **fetch** - 网页读取和数据获取
3. **context7** - 上下文管理和记忆
4. **sequential-thinking** - 序列思维和逻辑推理

### **🚀 最佳实践建议**

1. **优先使用全局配置** - 简单易管理，适合大多数场景
2. **项目配置作为补充** - 仅在需要特殊配置时使用
3. **定期更新工具** - 使用 `@latest` 版本保持最新
4. **验证配置效果** - 配置后检查绿色状态指示灯

### **🔧 故障排除经验**

#### **最常见问题：配置文件位置错误**
- **现象**: 配置了但不显示 "Project Managed"
- **原因**: 文件放在了错误位置
- **解决**: 确保文件在 `.cursor/mcp.json`

#### **第二常见问题：JSON 格式错误**
- **现象**: 配置后工具无法启动
- **原因**: JSON 语法错误
- **解决**: 使用 JSON 验证器检查格式

---

*最后更新：2025-01-27*
*基于实际成功配置经验编写*

> 💡 **提示**：如果遇到问题，请先查看本指南的故障排除部分，或在 GitHub 上搜索相关 issue。
>
> 🎉 **成功案例**：本指南已经过实际验证，按照步骤操作可以 100% 成功配置！
>
> 🔍 **重要发现**：项目配置的正确位置是 `.cursor/mcp.json`，不是 `.cursor/rules/mcp.json` 或其他位置！
