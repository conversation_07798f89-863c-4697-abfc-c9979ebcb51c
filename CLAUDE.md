# CLAUDE.md

该文件为 Claude Code (claude.ai/code) 在此代码库中工作提供指导。

## 项目概述

**测试库** 是一个基于 Obsidian 的综合性知识管理和 AI 工具集成系统，专为现代知识工作者设计。项目将 Obsidian 的强大知识管理能力与前沿的 AI 工具、自动化脚本完美融合，创造出高效、美观、智能的数字化工作环境。

## 技术栈

- **核心系统**：Obsidian + Dataview + DataviewJS 知识管理
- **后端开发**：Python 3.8+（主要脚本语言）
- **前端技术**：Node.js 18+、HTML/CSS/JavaScript
- **AI 集成**：MCP（模型上下文协议）
- **图像处理**：Selenium、Playwright、html2image
- **数据处理**：pandas、numpy、SQLite

## 常用开发命令

### 环境配置与安装
```bash
npm run setup                 # 完整项目配置
npm run install-deps         # 安装依赖
python scripts/setup-config.py           # 配置项目
python scripts/verify-installation.py    # 验证安装
```

### 开发工作流
```bash
npm run dev                  # 启动开发服务
npm run build                # 构建项目
npm run test                 # 运行测试
npm run lint                 # 代码检查
npm run format              # 代码格式化
```

### MCP（模型上下文协议）操作
```bash
npm run mcp:start           # 启动 MCP 服务
npm run mcp:test            # 测试 MCP 配置
npm run mcp:diagnose        # 诊断 MCP 问题
python scripts/test-mcp.py              # 测试 MCP 工具
python scripts/diagnose-mcp.py          # 详细 MCP 诊断
```

### 内容生成
```bash
npm run promo:generate      # 生成推广图
python tools/content-generator/promo_generator.py    # 高级推广图生成
python tools/content-generator/batch_content_generator.py  # 批量内容生成
```

### 数据处理
```bash
npm run data:export         # 导出数据
python tools/data-processor/extract_cursor_chats.py  # 提取聊天记录
python tools/data-processor/export_augment_data.py   # 导出增强数据
```

## 系统架构

### 核心架构设计

项目采用模块化架构，职责分离明确：

**MCP 集成层**：使用模型上下文协议统一管理 AI 工具
- 8 个 MCP 工具包括 mcp-obsidian、mcp-feedback-enhanced、context7、playwright
- AI 服务统一接口（OpenAI、Replicate、Together AI）
- 配置管理位于 `config/mcp/`

**Obsidian 知识库**：
- 核心库位于 `obsidian-vault/`，结构化组织
- 模板系统确保笔记创建的一致性
- Dataview 驱动的项目管理仪表盘
- 治愈系奶茶风格自定义 CSS 主题

**Python 自动化层**：
- `tools/` 目录中的专业化模块工具集
- 基于 HTML/CSS 模板引擎的内容生成系统
- 聊天导出和分析的数据处理管道
- MCP 服务管理和测试工具

**配置管理**：
- `config/` 中的环境特定配置
- 基于模板的配置生成
- 多 IDE 支持（Cursor、Augment、Claude Desktop）

### 主要目录结构

- `obsidian-vault/`：核心 Obsidian 知识库，组织结构化
- `tools/`：Python 工具集（content-generator、data-processor、mcp-tools、web-tools）
- `config/mcp/`：MCP 协议配置文件
- `scripts/`：开发和维护脚本
- `output/`：生成的内容和导出文件
- `ai-ide-chat-export/`：独立的聊天导出工具项目

## 开发指南

### MCP 工具使用
- MCP 配置按环境存储在 `config/mcp/`
- 配置更改后务必测试 MCP 连接：`npm run mcp:test`
- 使用 `python scripts/diagnose-mcp.py` 进行详细故障排除

### Obsidian 集成
- 模板位于 `obsidian-vault/Templates/`
- Dataview 查询驱动仪表盘系统
- CSS 自定义遵循治愈系奶茶风格设计语言
- MCP 集成需要 Local REST API 插件

### 内容生成
- HTML 模板位于 `tools/content-generator/templates/`
- 推广图生成使用浏览器自动化
- 多种样式变体：Bento Grid、治愈系奶茶风、商务专业风

### Python 开发
- 遵循 `tools/` 中的现有模块结构
- 通过 `.env` 文件管理环境变量
- 提供 MCP 和工具验证的测试工具

## 环境要求

- Python 3.8+ 及特定依赖（Pillow、selenium、playwright）
- Node.js 18+ 用于前端和构建工具
- Obsidian 1.0.0+ 配合 Dataview 和 Local REST API 插件
- 浏览器自动化依赖（Chrome/Chromium 用于 Selenium/Playwright）

## 特殊配置

### 必需的环境变量
配置 `.env` 文件包含：
- Obsidian Local REST API 的 API 密钥
- AI 服务 API 密钥（OpenAI、Replicate、Together AI）
- MCP 服务端点和认证
- 图像生成和处理设置

### MCP 协议配置
本项目首创使用模型上下文协议实现 AI 工具统一管理。MCP 配置实现了 Obsidian、AI 服务和自动化工具之间的无缝集成。

### 治愈系奶茶风格设计
项目采用自定义的"治愈系奶茶风格"设计语言，使用温暖、舒缓的色彩，专为中文用户优化。进行 UI 更改时请保持这种美学风格。

### 中文本土化特性
项目深度针对中文用户进行本土化优化，包括界面语言、功能设计和用户体验，在开发时应考虑中文使用习惯。