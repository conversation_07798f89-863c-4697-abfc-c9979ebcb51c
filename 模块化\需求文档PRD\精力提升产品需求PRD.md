# 精力提升产品需求PRD

## 1. 产品背景与市场机会
**背景**：在信息爆炸与高强度工作环境中，都市白领与创意从业者普遍面临 **精力衰退**、**注意力分散** 与 **作息紊乱** 等问题。根据《2023 数字健康市场报告》，全球数字健康市场规模已达 **US$ 1750 亿**，其中 **能量与专注度管理** 细分赛道年复合增长率超过 **12%**。

**市场机会**：
1. **移动端普及**：智能手机渗透率 > 90%，为数据采集与干预提供天然入口。
2. **健康意识升级**：后疫情时代，用户对睡眠、运动、心理健康投入时间与金钱均显著提升。
3. **同质化竞争**：现有应用侧重计步或冥想，缺乏 **跨场景整合** 与 **个性化算法**，为差异化切入提供空间。

---

## 2. 目标用户画像
| 维度 | 描述 |
|---|---|
| 年龄 | 25 – 40 岁 |
| 职业 | 白领、自由职业者、初创团队成员 |
| 痛点 | 下午犯困、工作时分心、熬夜后恢复慢 |
| 目标 | 提升白天精力与专注度、建立健康作息 |
| 动机 | 期望以最小成本持续优化状态，提升工作/学习产出 |

---

## 3. 关键痛点 & 价值主张
1. **缺乏可量化的精力指标** → 提供 **能量评估分数**，让用户直观感知状态变化。
2. **无法识别精力波动原因** → **行为追踪**（睡眠/运动/饮食）+ 关联分析，定位影响因子。
3. **缺少个性化建议** → 基于算法与同类人群数据，推送 **行动建议**。
4. **自律难以长期坚持** → **番茄钟+社群激励** 结合，提升持续性与趣味性。

---

## 4. 产品愿景与成功指标
**愿景**：成为用户每日必用的 **“个人能量管家”**，帮助其以更高效、更健康的方式达成目标。

**成功指标 (North Star & KPI)**：
- **North Star**：每周用户完成的高能时段 (High-Energy Sessions) 次数。
- **DAU/MAU** ≥ 25%，**7日留存** ≥ 40%，**NPS** ≥ 45。

---

## 5. 核心功能
| 功能 | 描述 | 价值 |
|---|---|---|
| 能量评估 | 综合睡眠、HRV、活动量，输出 0–100 分能量值 | 量化反馈，形成使用动机 |
| 行为追踪 | 接入 Apple Health / Google Fit，同步睡眠、步数、心率等 | 获取多维数据，为算法提供基础 |
| AI 建议 | 基于回归模型+规则引擎，生成睡眠、运动、饮食建议 | 个性化干预，提升转化率 |
| 番茄钟 | 番茄工作法 + 白噪音，计入高能时段 | 提升专注与完成感 |
| 社群激励 | 排行榜、打卡、徽章体系 | 建立社交杠杆，提升留存 |

---

## 6. 用户旅程 & 业务流程
1. **新用户**：下载 → 注册 → 数据授权 → 初始问卷 → 首次能量评估。
2. **日常使用**：
   - 早上：打开 App 查看今日能量预测与建议。
   - 工作中：启动番茄钟进入专注模式。
   - 晚上：睡前浏览日总结与次日建议。
3. **反馈闭环**：用户完成建议 → 数据更新 → 能量分数迭代。

---

## 7. MVP 范围
- 基础注册/登录（手机号 + 第三方登录）。
- Apple Health / Google Fit 数据同步（睡眠、步数、心率）。
- 能量评分算法 V1（简单加权模型）。
- 能量仪表板、番茄钟功能。
- AI 文本建议（基于模板+变量）。

---

## 8. 非功能需求
| 分类 | 需求 | 指标 |
|---|---|---|
| 性能 | App 首屏加载 | ≤ 2 秒|
| 稳定性 | Crash 率 | ≤ 0.5%|
| 安全 | 传输 & 存储加密 | AES-256 + HTTPS |
| 隐私 | 合规 | GDPR / 中国《个人信息保护法》 |

---

## 9. 里程碑 & KPI
| 阶段 | 时间 | 产出 | KPI |
|---|---|---|---|
| PRD 完成 | T+0 周 | 文档评审通过 | — |
| 设计稿 & 原型 | T+2 周 | 交互 + 视觉稿 | 主要流程通过可用性测试 |
| Alpha | T+6 周 | 核心功能实现 | Crash ≤ 2% |
| Beta (50% 灰度) | T+8 周 | 功能完善 + 埋点 | 7日留存 ≥ 35% |
| GA | T+10 周 | 全量发布 | DAU ≥ 10k，评分 ≥ 4.5 |

---

## 10. 风险与假设
| 风险 | 影响 | 缓解措施 |
|---|---|---|
| 数据接口不稳定 | 评分失准，体验下降 | 本地缓存 + 重试 & 监控 |
| 算法准确率不足 | 用户质疑价值 | 引入专家校正 + A/B 测试迭代 |
| 同质化竞争激烈 | 用户流失 | 强化内容差异化 & 社群运营 |

---

## 11. TDD 测试点（MVP）
1. **API 接口**：
   - /auth/login 返回 200 & token。
   - /energy/score 输入完整数据返回有效分数 (0–100)。
2. **核心算法**：
   - 输入同一套指标，多次计算输出稳定。
   - 缺失值场景下返回可解释默认值。
3. **数据汇聚**：
   - 同步睡眠数据时，日边界跨时区仍准确。

---

## 12. Playwright 端到端测试（后续迭代）
- 注册流程自动化验证。
- 能量仪表板关键指标渲染。
- 番茄钟启动 → 倒计时 → 完成提示。

> 本 PRD 将随版本演进持续更新。
