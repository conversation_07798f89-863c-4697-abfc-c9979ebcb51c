@echo off
chcp 65001 >nul
echo 🔧 Augment Guidelines 快速恢复工具
echo =====================================

cd /d "%~dp0\.."

echo 📋 检查当前状态...
if not exist ".augment-guidelines" (
    echo ❌ .augment-guidelines 文件不存在
    goto :restore
)

for %%A in (.augment-guidelines) do set size=%%~zA
if %size%==0 (
    echo ⚠️ .augment-guidelines 文件为空 ^(%size% 字节^)
    goto :restore
) else (
    echo ✅ .augment-guidelines 文件存在 ^(%size% 字节^)
    echo ✅ 配置文件状态正常
    goto :end
)

:restore
echo.
echo 🔄 开始恢复配置...
if exist "docs\Augment Agent工作偏好设置.md" (
    copy "docs\Augment Agent工作偏好设置.md" ".augment-guidelines" >nul
    echo ✅ 已从工作偏好设置恢复配置
) else (
    echo ❌ 源文件不存在: docs\Augment Agent工作偏好设置.md
    goto :error
)

echo.
echo 📊 恢复结果:
for %%A in (.augment-guidelines) do echo 📁 文件大小: %%~zA 字节
echo 🎉 配置恢复完成！

echo.
echo 💡 使用建议:
echo 1. 每次启动 Augment Agent 前运行此脚本
echo 2. 可以将此脚本添加到桌面快捷方式
echo 3. 如果问题持续，可能需要联系 Augment 技术支持

goto :end

:error
echo ❌ 恢复失败，请检查文件路径和权限
pause
exit /b 1

:end
echo.
echo 按任意键退出...
pause >nul
