
我将查找关于MCP Obsidian的使用指南信息。先看一下是否有专门的文档：

Ran tool
让我查看README.md文件以获取更多关于MCP Obsidian的使用信息：

Ran tool
让我检查一下start_obsidian_mcp.bat脚本的内容，以了解MCP服务的启动过程：

Ran tool
让我查看一下Obsidian中有哪些与MCP相关的文件：

Ran tool

## MCP Obsidian使用指南

MCP Obsidian是一个连接Cursor IDE与Obsidian的接口，让您可以在编码时直接操作Obsidian笔记库。以下是主要功能和使用方法：

### 基本功能

1. **浏览笔记库**
   - 列出笔记库根目录: `mcp_mcp-obsidian_obsidian_list_files_in_vault`
   - 列出特定目录: `mcp_mcp-obsidian_obsidian_list_files_in_dir`（需指定路径）

2. **读取笔记内容**
   - 获取单个文件内容: `mcp_mcp-obsidian_obsidian_get_file_contents`
   - 批量获取多个文件: `mcp_mcp-obsidian_obsidian_batch_get_file_contents`

3. **搜索功能**
   - 简单文本搜索: `mcp_mcp-obsidian_obsidian_simple_search`
   - 复杂条件搜索: `mcp_mcp-obsidian_obsidian_complex_search`

4. **编辑笔记**
   - 添加内容到文件: `mcp_mcp-obsidian_obsidian_append_content`
   - 修改特定部分内容: `mcp_mcp-obsidian_obsidian_patch_content`
   - 删除文件: `mcp_mcp-obsidian_obsidian_delete_file`

5. **周期性笔记**
   - 获取当前周期笔记: `mcp_mcp-obsidian_obsidian_get_periodic_note`
   - 获取最近周期笔记: `mcp_mcp-obsidian_obsidian_get_recent_periodic_notes`

### 使用步骤

6. 确保Obsidian已启动并运行
7. 确保已执行`start_obsidian_mcp.bat`脚本启动MCP服务
8. 在Cursor IDE中使用上述功能与您的笔记库交互

### 示例应用场景

- 编程时查询相关笔记
- 自动创建项目文档
- 查看和更新任务笔记
- 在编码过程中记录想法和问题
- 将代码示例保存到Obsidian

需要我演示某个具体功能吗？
