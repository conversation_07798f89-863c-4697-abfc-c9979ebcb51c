# MCP工具使用规则

## 1. 基本原则
- MCP工具是增强AI能力的重要工具，能够提供实时数据获取、反馈收集、上下文管理等功能
- 正确使用这些工具能够显著提升工作效率和输出质量
- 需要根据具体任务选择合适的工具

## 2. 核心工具

### 2.1 mcp-feedback-enhanced（反馈增强工具）
- **功能**：收集和管理用户反馈，提供反馈分析
- **使用场景**：
  - 收集用户对AI输出的评价
  - 分析反馈趋势和模式
  - 改进AI响应质量

### 2.2 fetch/Web-Reader（网页获取工具）
- **功能**：获取网页内容，解析网页信息
- **使用场景**：
  - 获取最新的网页信息
  - 解析网页结构和内容
  - 提取特定网页数据

### 2.3 context7（上下文管理工具）
- **功能**：管理和维护对话上下文
- **使用场景**：
  - 保持长对话的连贯性
  - 管理复杂项目的上下文信息
  - 提供智能上下文检索

### 2.4 sequential-thinking（顺序思维工具）
- **功能**：支持结构化思维和逻辑推理
- **使用场景**：
  - 复杂问题的分步解决
  - 逻辑链条的构建
  - 系统性思考过程

## 3. 工具调用规则

### 3.1 调用时机
- 需要实时数据时使用fetch工具
- 需要收集反馈时使用mcp-feedback-enhanced
- 处理复杂逻辑时使用sequential-thinking
- 需要维护上下文时使用context7

### 3.2 调用方式
- 通过自然语言描述触发工具调用
- 明确指定需要的功能和参数
- 确保调用目的清晰明确

### 3.3 参数设置
- 根据具体需求设置合适的参数
- 注意参数的格式和类型要求
- 确保必需参数完整提供

## 4. 最佳实践

### 4.1 工具组合使用
- 可以组合多个工具解决复杂问题
- 注意工具间的协调和配合
- 避免重复调用相同功能

### 4.2 错误处理
- 工具调用失败时要有备选方案
- 及时检查工具返回结果
- 对异常情况进行适当处理

### 4.3 性能优化
- 避免不必要的工具调用
- 合理控制调用频率
- 优化参数设置提高效率

## 5. 注意事项

### 5.1 安全考虑
- 注意保护敏感信息
- 遵守相关使用协议
- 避免滥用工具功能

### 5.2 兼容性
- 确保工具版本兼容
- 注意不同环境下的差异
- 及时更新工具配置

### 5.3 监控和维护
- 定期检查工具运行状态
- 监控工具使用效果
- 及时处理异常情况

## 6. 常见问题

### 6.1 工具无法调用
- 检查MCP配置是否正确
- 确认工具是否正常安装
- 验证网络连接状态

### 6.2 返回结果异常
- 检查输入参数是否正确
- 确认目标资源是否可访问
- 分析错误信息并调整策略

### 6.3 性能问题
- 优化调用参数
- 减少不必要的调用
- 考虑使用缓存机制
