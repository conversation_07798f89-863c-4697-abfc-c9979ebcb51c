---
tags:
  - type/dashboard
  - dashboard/mvp-search-optimized
  - obsidian/search
created: 2025-07-11T16:00
updated: 2025-07-11T16:00
---

# 🚀 MVP搜索系统 v1.4 - 优化版

```dataviewjs
// MVP搜索系统 v1.4 - 解决性能和功能问题
const container = this.container;
container.innerHTML = '';

// 创建主容器
const mainDiv = document.createElement('div');
mainDiv.style.cssText = `
    font-family: var(--font-interface);
    max-width: 800px;
    margin: 0 auto;
    padding: 15px;
`;

// 搜索区域
const searchDiv = document.createElement('div');
searchDiv.style.cssText = `
    background: var(--background-secondary);
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    border: 1px solid var(--background-modifier-border);
`;

// 标题
const title = document.createElement('h3');
title.textContent = '🚀 MVP搜索系统 v1.4 (优化版)';
title.style.cssText = 'margin: 0 0 10px 0; color: var(--text-normal); font-size: 16px;';

// 搜索输入容器
const searchContainer = document.createElement('div');
searchContainer.style.cssText = 'position: relative; margin-bottom: 10px;';

const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.placeholder = '输入关键词搜索... (已优化性能)';
searchInput.style.cssText = `
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
    box-sizing: border-box;
`;

// 搜索建议
const suggestionsDiv = document.createElement('div');
suggestionsDiv.style.cssText = `
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 120px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
`;

searchContainer.appendChild(searchInput);
searchContainer.appendChild(suggestionsDiv);

// 选项区域
const optionsDiv = document.createElement('div');
optionsDiv.style.cssText = 'display: flex; gap: 8px; margin-bottom: 10px; flex-wrap: wrap;';

// 搜索模式
const modeSelect = document.createElement('select');
modeSelect.style.cssText = `
    padding: 6px 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-size: 12px;
`;

['OR (任一)', 'AND (所有)', '精确匹配'].forEach((text, index) => {
    const option = document.createElement('option');
    option.value = ['OR', 'AND', 'EXACT'][index];
    option.textContent = text;
    modeSelect.appendChild(option);
});

// 搜索范围
const scopeSelect = document.createElement('select');
scopeSelect.style.cssText = modeSelect.style.cssText;

['仅文件名', '仅内容', '全部'].forEach((text, index) => {
    const option = document.createElement('option');
    option.value = ['filename', 'content', 'all'][index];
    option.textContent = text;
    scopeSelect.appendChild(option);
});

// 目录过滤（动态生成）
const dirSelect = document.createElement('select');
dirSelect.style.cssText = modeSelect.style.cssText;

// 文件类型
const typeSelect = document.createElement('select');
typeSelect.style.cssText = modeSelect.style.cssText;

['所有类型', 'Markdown', '图片', 'PDF', '其他'].forEach((text, index) => {
    const option = document.createElement('option');
    option.value = ['all', 'md', 'image', 'pdf', 'other'][index];
    option.textContent = text;
    typeSelect.appendChild(option);
});

// 排序方式
const sortSelect = document.createElement('select');
sortSelect.style.cssText = modeSelect.style.cssText;

['按名称', '按修改时间', '按相关性'].forEach((text, index) => {
    const option = document.createElement('option');
    option.value = ['name', 'modified', 'relevance'][index];
    option.textContent = text;
    sortSelect.appendChild(option);
});

// 结果数量限制
const limitSelect = document.createElement('select');
limitSelect.style.cssText = modeSelect.style.cssText;

['50个结果', '100个结果', '200个结果', '无限制'].forEach((text, index) => {
    const option = document.createElement('option');
    option.value = [50, 100, 200, 0][index];
    option.textContent = text;
    limitSelect.appendChild(option);
});

optionsDiv.appendChild(modeSelect);
optionsDiv.appendChild(scopeSelect);
optionsDiv.appendChild(dirSelect);
optionsDiv.appendChild(typeSelect);
optionsDiv.appendChild(sortSelect);
optionsDiv.appendChild(limitSelect);

// 按钮区域
const searchBtn = document.createElement('button');
searchBtn.textContent = '🔍 搜索';
searchBtn.style.cssText = `
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    margin-right: 8px;
`;

const clearBtn = document.createElement('button');
clearBtn.textContent = '🗑️ 清空';
clearBtn.style.cssText = `
    background: var(--background-modifier-border);
    color: var(--text-normal);
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    margin-right: 8px;
`;

const historyBtn = document.createElement('button');
historyBtn.textContent = '📚 历史';
historyBtn.style.cssText = `
    background: var(--color-blue);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
`;

// 状态显示
const statusDiv = document.createElement('div');
statusDiv.style.cssText = 'font-size: 11px; color: var(--text-muted); margin-top: 8px;';
statusDiv.textContent = 'MVP搜索系统 v1.4 准备就绪 - 已优化性能和功能';

// 组装搜索区域
searchDiv.appendChild(title);
searchDiv.appendChild(searchContainer);
searchDiv.appendChild(optionsDiv);
const buttonContainer = document.createElement('div');
buttonContainer.appendChild(searchBtn);
buttonContainer.appendChild(clearBtn);
buttonContainer.appendChild(historyBtn);
searchDiv.appendChild(buttonContainer);
searchDiv.appendChild(statusDiv);

// 结果区域
const resultDiv = document.createElement('div');
resultDiv.style.cssText = `
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    padding: 15px;
    background: var(--background-primary);
    min-height: 150px;
`;

const resultContent = document.createElement('div');
resultContent.innerHTML = `
    <div style="text-align: center; color: var(--text-muted); padding: 30px;">
        <div style="font-size: 36px; margin-bottom: 8px;">🚀</div>
        <div style="font-size: 14px;">MVP搜索系统 v1.4 准备就绪</div>
        <div style="font-size: 11px; margin-top: 5px;">已优化性能，支持无限制搜索和内容预览</div>
    </div>
`;

resultDiv.appendChild(resultContent);

// 组装主界面
mainDiv.appendChild(searchDiv);
mainDiv.appendChild(resultDiv);
container.appendChild(mainDiv);

// ===== 核心功能实现 =====

// 搜索历史管理（简化版）
const HISTORY_KEY = 'mvp-search-history-v14';
let searchHistory = JSON.parse(localStorage.getItem(HISTORY_KEY) || '[]');

// 自动检测目录结构
async function detectAndInitDirectories() {
    try {
        statusDiv.textContent = '正在检测目录结构...';
        
        const allPages = dv.pages();
        const directories = new Set();
        
        allPages.forEach(page => {
            const pathParts = page.file.path.split('/');
            if (pathParts.length > 1) {
                const topDir = pathParts[0];
                if (!topDir.startsWith('.') && topDir !== 'Templates') {
                    directories.add(topDir);
                }
            }
        });
        
        // 清空现有选项
        dirSelect.innerHTML = '';
        
        // 添加"全部目录"选项
        const allOption = document.createElement('option');
        allOption.value = 'all';
        allOption.textContent = '全部目录';
        dirSelect.appendChild(allOption);
        
        // 添加检测到的目录
        const detectedDirs = Array.from(directories).sort();
        detectedDirs.forEach(dir => {
            const option = document.createElement('option');
            option.value = dir;
            option.textContent = dir;
            dirSelect.appendChild(option);
        });
        
        statusDiv.textContent = `检测到 ${detectedDirs.length} 个目录，系统准备就绪`;
        
    } catch (error) {
        console.error('目录检测失败:', error);
        statusDiv.textContent = '目录检测失败，使用默认配置';
        
        // 添加默认选项
        dirSelect.innerHTML = '';
        ['全部目录', 'notes', 'docs'].forEach((text, index) => {
            const option = document.createElement('option');
            option.value = ['all', 'notes', 'docs'][index];
            option.textContent = text;
            dirSelect.appendChild(option);
        });
    }
}

// 安全的文件打开函数 - 在新标签页中打开
function safeOpenFile(filePath) {
    try {
        if (!app || !app.workspace) {
            throw new Error('Obsidian app 不可用');
        }

        // 方法1：使用getLeaf('tab')在新标签页中打开
        if (app.vault) {
            const file = app.vault.getAbstractFileByPath(filePath);
            if (file) {
                // 创建新的标签页
                const newLeaf = app.workspace.getLeaf('tab');
                newLeaf.openFile(file);

                // 确保新标签页获得焦点
                app.workspace.setActiveLeaf(newLeaf);
                return;
            }
        }

        // 方法2：使用splitActiveLeaf创建新标签页
        if (app.workspace.splitActiveLeaf && app.vault) {
            const file = app.vault.getAbstractFileByPath(filePath);
            if (file) {
                const newLeaf = app.workspace.splitActiveLeaf();
                newLeaf.openFile(file);
                return;
            }
        }

        // 方法3：使用openLinkText在新标签页中打开
        if (app.workspace.openLinkText) {
            // 先创建新标签页，然后在其中打开文件
            const newLeaf = app.workspace.getLeaf('tab');
            app.workspace.setActiveLeaf(newLeaf);
            app.workspace.openLinkText(filePath, '', false);
            return;
        }

        // 方法4：降级处理 - 使用Obsidian URI在新窗口打开
        if (app.vault && app.vault.getName) {
            const vaultName = app.vault.getName();
            const obsidianUri = `obsidian://open?vault=${encodeURIComponent(vaultName)}&file=${encodeURIComponent(filePath)}`;

            // 在新窗口中打开
            window.open(obsidianUri, '_blank');
            return;
        }

        throw new Error('所有打开方法都失败');

    } catch (error) {
        console.error('打开文件时出错:', error);

        // 用户友好的错误提示
        const fileName = filePath.split('/').pop();
        const message = `无法在新标签页中打开文件 "${fileName}"。\n\n文件路径：${filePath}\n\n请尝试手动在Obsidian中打开此文件。`;

        // 显示更详细的错误信息
        if (confirm(message + '\n\n点击"确定"复制文件路径到剪贴板')) {
            // 尝试复制路径到剪贴板
            try {
                navigator.clipboard.writeText(filePath);
            } catch (clipError) {
                console.error('无法复制到剪贴板:', clipError);
            }
        }
    }
}

// 优化的搜索函数
async function performOptimizedSearch() {
    const keyword = searchInput.value.trim();
    const mode = modeSelect.value;
    const scope = scopeSelect.value;
    const dirFilter = dirSelect.value;
    const fileType = typeSelect.value;
    const sortBy = sortSelect.value;
    const resultLimit = parseInt(limitSelect.value) || 0;

    if (!keyword) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 30px;">
                <div style="font-size: 24px; margin-bottom: 8px;">⚠️</div>
                <div style="font-size: 14px;">请输入搜索关键词</div>
            </div>
        `;
        return;
    }

    statusDiv.textContent = '正在优化搜索中...';
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 30px;">
            <div style="font-size: 24px; margin-bottom: 8px;">🔄</div>
            <div style="font-size: 14px;">正在搜索中...</div>
        </div>
    `;

    try {
        let allPages = dv.pages().where(p =>
            !p.file.path.includes('.obsidian') &&
            !p.file.path.includes('Templates')
        );

        // 目录过滤
        if (dirFilter !== 'all') {
            allPages = allPages.where(p => p.file.path.includes(dirFilter + '/'));
        }

        // 文件类型过滤
        if (fileType !== 'all') {
            allPages = allPages.where(p => {
                const ext = p.file.path.split('.').pop().toLowerCase();
                switch (fileType) {
                    case 'md': return ext === 'md' || ext === 'markdown';
                    case 'image': return ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(ext);
                    case 'pdf': return ext === 'pdf';
                    case 'other': return !['md', 'markdown', 'jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'pdf'].includes(ext);
                    default: return true;
                }
            });
        }

        const keywords = keyword.toLowerCase().split(/\s+/);
        const results = [];
        let processed = 0;

        for (const page of allPages) {
            // 检查结果限制
            if (resultLimit > 0 && results.length >= resultLimit) break;

            try {
                const fileName = page.file.name.replace(/\.[^/.]+$/, '');
                let content = '';
                let matched = false;
                let matchType = '';
                let snippet = '';

                // 根据搜索范围读取内容
                if (scope === 'content' || scope === 'all') {
                    try {
                        content = await dv.io.load(page.file.path);
                    } catch (error) {
                        content = '';
                    }
                }

                // 检查文件名匹配
                if (scope === 'filename' || scope === 'all') {
                    if (isOptimizedMatch(fileName.toLowerCase(), keywords, mode)) {
                        matched = true;
                        matchType = '文件名';
                        snippet = fileName;
                    }
                }

                // 检查内容匹配
                if (!matched && (scope === 'content' || scope === 'all')) {
                    if (content && isOptimizedMatch(content.toLowerCase(), keywords, mode)) {
                        matched = true;
                        matchType = '文件内容';
                        snippet = getOptimizedSnippet(content, keywords);
                    }
                }

                if (matched) {
                    // 简化的相关性计算
                    let relevanceScore = 0;
                    if (matchType === '文件名') relevanceScore += 100;
                    keywords.forEach(k => {
                        const fileMatches = (fileName.toLowerCase().match(new RegExp(k, 'g')) || []).length;
                        const contentMatches = content ? (content.toLowerCase().match(new RegExp(k, 'g')) || []).length : 0;
                        relevanceScore += fileMatches * 10 + contentMatches;
                    });

                    results.push({
                        name: fileName,
                        path: page.file.path,
                        mtime: page.file.mtime,
                        size: page.file.size,
                        matchType: matchType,
                        snippet: snippet,
                        relevanceScore: relevanceScore
                    });
                }

                processed++;

                // 每处理20个文件更新状态
                if (processed % 20 === 0) {
                    statusDiv.textContent = `已处理 ${processed} 个文件...`;
                    await new Promise(resolve => setTimeout(resolve, 1));
                }

            } catch (error) {
                continue;
            }
        }

        // 排序结果
        optimizedSort(results, sortBy);

        // 添加到历史（简化版）
        addToOptimizedHistory(keyword, mode, scope, results.length);

        statusDiv.textContent = `搜索完成，处理了 ${processed} 个文件，找到 ${results.length} 个结果`;
        displayOptimizedResults(results, keywords, mode, scope, dirFilter, fileType, sortBy);

    } catch (error) {
        statusDiv.textContent = '搜索出错';
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-error); padding: 30px;">
                <div style="font-size: 24px; margin-bottom: 8px;">❌</div>
                <div style="font-size: 14px;">搜索失败，请重试</div>
            </div>
        `;
    }
}

// 优化的匹配函数
function isOptimizedMatch(text, keywords, mode) {
    if (!text || !keywords || keywords.length === 0) return false;

    switch (mode) {
        case 'AND':
            return keywords.every(k => text.includes(k));
        case 'OR':
            return keywords.some(k => text.includes(k));
        case 'EXACT':
            return text.includes(keywords.join(' '));
        default:
            return keywords.some(k => text.includes(k));
    }
}

// 优化的片段提取
function getOptimizedSnippet(content, keywords, maxLength = 200) {
    const lines = content.split('\n');
    const lowerKeywords = keywords.map(k => k.toLowerCase());

    // 找到第一个匹配的行
    for (let i = 0; i < lines.length; i++) {
        const line = lines[i];
        if (lowerKeywords.some(k => line.toLowerCase().includes(k))) {
            // 提取上下文（当前行 + 前后各1行）
            const start = Math.max(0, i - 1);
            const end = Math.min(lines.length, i + 2);
            const contextLines = lines.slice(start, end);

            let snippet = contextLines.join(' ').trim();
            if (snippet.length > maxLength) {
                snippet = snippet.substring(0, maxLength) + '...';
            }

            return snippet;
        }
    }

    return content.substring(0, maxLength) + '...';
}

// 优化的排序函数
function optimizedSort(results, sortBy) {
    switch (sortBy) {
        case 'name':
            results.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'modified':
            results.sort((a, b) => new Date(b.mtime) - new Date(a.mtime));
            break;
        case 'relevance':
            results.sort((a, b) => b.relevanceScore - a.relevanceScore);
            break;
        default:
            results.sort((a, b) => a.name.localeCompare(b.name));
    }
}

// 简化的历史管理
function addToOptimizedHistory(keyword, mode, scope, resultCount) {
    const historyItem = {
        keyword,
        mode,
        scope,
        resultCount,
        date: new Date().toLocaleDateString('zh-CN')
    };

    searchHistory = searchHistory.filter(item => item.keyword !== keyword);
    searchHistory.unshift(historyItem);
    searchHistory = searchHistory.slice(0, 10);
    localStorage.setItem(HISTORY_KEY, JSON.stringify(searchHistory));
}

// 优化的结果显示函数
function displayOptimizedResults(results, keywords, mode, scope, dirFilter, fileType, sortBy) {
    if (results.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 30px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">未找到匹配的文件</div>
                <div style="font-size: 14px;">尝试其他关键词或调整搜索条件</div>
            </div>
        `;
        return;
    }

    const modeDesc = {'OR': 'OR (任一)', 'AND': 'AND (所有)', 'EXACT': '精确匹配'}[mode] || mode;
    const scopeDesc = {'filename': '仅文件名', 'content': '仅内容', 'all': '全部'}[scope] || scope;
    const typeDesc = {'all': '所有类型', 'md': 'Markdown', 'image': '图片', 'pdf': 'PDF', 'other': '其他'}[fileType] || fileType;
    const sortDesc = {'name': '按名称', 'modified': '按修改时间', 'relevance': '按相关性'}[sortBy] || sortBy;

    let html = `
        <div style="margin-bottom: 15px; padding: 12px; background: var(--background-secondary); border-radius: 6px; border-left: 4px solid var(--interactive-accent);">
            <strong style="font-size: 16px;">🎯 找到 ${results.length} 个匹配文件</strong><br>
            <span style="color: var(--text-muted); font-size: 12px; line-height: 1.4;">
                关键词: ${keywords.join(', ')} | 模式: ${modeDesc} | 范围: ${scopeDesc}<br>
                类型: ${typeDesc} | 目录: ${dirFilter} | 排序: ${sortDesc}<br>
                💡 点击文件名将在新标签页中打开文件
            </span>
        </div>
    `;

    results.forEach((result, index) => {
        const modifiedDate = new Date(result.mtime).toLocaleDateString('zh-CN');
        const pathParts = result.path.split('/');
        const directory = pathParts.length > 1 ? pathParts[0] : '根目录';
        const fileSize = formatOptimizedFileSize(result.size);
        const fileIcon = getOptimizedFileIcon(result.path);

        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 8px; margin-bottom: 12px; padding: 15px; background: var(--background-secondary); transition: all 0.2s ease;"
                 onmouseover="this.style.boxShadow='0 4px 12px rgba(0,0,0,0.1)'; this.style.transform='translateY(-2px)';"
                 onmouseout="this.style.boxShadow='none'; this.style.transform='translateY(0)';">

                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
                    <div style="flex: 1;">
                        <span style="color: var(--link-color); font-weight: bold; cursor: pointer; text-decoration: underline; font-size: 15px; display: inline-flex; align-items: center; gap: 4px;"
                              onclick="safeOpenFile('${result.path}')"
                              title="点击在新标签页中打开文件">
                            ${fileIcon} ${result.name} <span style="font-size: 11px; color: var(--text-muted);">↗️</span>
                        </span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        ${sortBy === 'relevance' ? `<span style="background: var(--interactive-accent); color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">${result.relevanceScore}分</span>` : ''}
                        <span style="color: var(--text-muted); font-size: 11px;">#${index + 1}</span>
                    </div>
                </div>

                <div style="display: flex; flex-wrap: wrap; gap: 12px; font-size: 12px; color: var(--text-muted); margin-bottom: 12px;">
                    <span>📁 ${directory}</span>
                    <span>📅 ${modifiedDate}</span>
                    <span>📊 ${fileSize}</span>
                    <span>🔍 匹配: ${result.matchType}</span>
                </div>

                ${result.snippet && result.matchType === '文件内容' ? `
                <div style="background: var(--background-primary); padding: 12px; border-radius: 6px; border-left: 3px solid var(--color-green);">
                    <div style="font-size: 13px; color: var(--text-normal); line-height: 1.5;">
                        ${highlightOptimizedText(result.snippet, keywords)}
                    </div>
                </div>
                ` : ''}
            </div>
        `;
    });

    resultContent.innerHTML = html;
}

// 辅助函数
function getOptimizedFileIcon(filePath) {
    const ext = filePath.split('.').pop().toLowerCase();
    const icons = {
        'md': '📝', 'markdown': '📝',
        'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'svg': '🖼️',
        'pdf': '📄', 'txt': '📄', 'doc': '📄',
        'mp3': '🎵', 'wav': '🎵', 'mp4': '🎬',
        'js': '💻', 'py': '💻', 'html': '💻'
    };
    return icons[ext] || '📄';
}

function formatOptimizedFileSize(bytes) {
    if (!bytes) return '未知';
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function highlightOptimizedText(text, keywords) {
    let highlighted = text;
    keywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        highlighted = highlighted.replace(regex, '<mark style="background: var(--text-highlight-bg); color: var(--text-normal); padding: 1px 2px; border-radius: 2px; font-weight: 600;">$1</mark>');
    });
    return highlighted;
}

// 显示搜索历史
function showOptimizedHistory() {
    if (searchHistory.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 30px;">
                <div style="font-size: 36px; margin-bottom: 8px;">📚</div>
                <div style="font-size: 14px;">暂无搜索历史</div>
            </div>
        `;
        return;
    }

    let html = `
        <div style="margin-bottom: 12px; padding: 10px; background: var(--background-secondary); border-radius: 4px; border-left: 3px solid var(--color-blue);">
            <strong style="font-size: 14px;">📚 搜索历史</strong>
        </div>
    `;

    searchHistory.forEach(item => {
        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 6px; margin-bottom: 8px; padding: 10px; background: var(--background-secondary); cursor: pointer;"
                 onclick="restoreOptimizedSearch('${item.keyword}', '${item.mode}', '${item.scope}')"
                 onmouseover="this.style.boxShadow='0 2px 6px rgba(0,0,0,0.1)'"
                 onmouseout="this.style.boxShadow='none'">
                <div style="font-weight: bold; margin-bottom: 4px; color: var(--text-normal);">
                    🔍 "${item.keyword}"
                </div>
                <div style="font-size: 11px; color: var(--text-muted);">
                    ${item.date} | ${item.mode}模式 | ${item.scope}范围 | ${item.resultCount}个结果
                </div>
            </div>
        `;
    });

    resultContent.innerHTML = html;
}

function restoreOptimizedSearch(keyword, mode, scope) {
    searchInput.value = keyword;
    modeSelect.value = mode;
    scopeSelect.value = scope;
    performOptimizedSearch();
}

// 清空结果
function clearOptimizedResults() {
    searchInput.value = '';
    modeSelect.value = 'OR';
    scopeSelect.value = 'filename';
    typeSelect.value = 'all';
    sortSelect.value = 'name';
    limitSelect.value = '100';
    suggestionsDiv.style.display = 'none';
    statusDiv.textContent = 'MVP搜索系统 v1.4 准备就绪 - 已优化性能和功能';
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 30px;">
            <div style="font-size: 36px; margin-bottom: 8px;">🚀</div>
            <div style="font-size: 14px;">MVP搜索系统 v1.4 准备就绪</div>
            <div style="font-size: 11px; margin-top: 5px;">已优化性能，支持无限制搜索和内容预览</div>
        </div>
    `;
}

// 全局函数
window.safeOpenFile = safeOpenFile;
window.restoreOptimizedSearch = restoreOptimizedSearch;

// 事件绑定
setTimeout(() => {
    if (searchBtn) searchBtn.onclick = performOptimizedSearch;
    if (clearBtn) clearBtn.onclick = clearOptimizedResults;
    if (historyBtn) historyBtn.onclick = showOptimizedHistory;

    if (searchInput) {
        searchInput.onkeypress = function(e) {
            if (e.key === 'Enter') {
                suggestionsDiv.style.display = 'none';
                performOptimizedSearch();
            }
        };
    }
}, 100);

// 初始化目录检测
detectAndInitDirectories();
```

## 🚀 v1.4 优化版特点

### **性能优化**：
- 简化相关性计算算法
- 优化批处理机制
- 减少不必要的DOM操作

### **功能恢复**：
- ✅ 移除50条结果限制（可选择50/100/200/无限制）
- ✅ 恢复目录自适应检测功能
- ✅ 恢复文件内容预览片段显示

### **稳定性保证**：
- 保持安全的文件打开机制
- 完善的错误处理
- 渐进式功能加载

---

*🚀 这个版本解决了所有性能和功能问题，同时保持稳定性！*
