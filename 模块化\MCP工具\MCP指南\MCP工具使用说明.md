# 🛠️ MCP 4个核心工具使用指南与案例

## 📋 目录
1. [工具概览](#工具概览)
2. [mcp-feedback-enhanced 使用指南](#mcp-feedback-enhanced-使用指南)
3. [fetch 网页读取工具](#fetch-网页读取工具)
4. [context7 上下文管理](#context7-上下文管理)
5. [sequential-thinking 序列思维](#sequential-thinking-序列思维)
6. [组合使用案例](#组合使用案例)
7. [Prompt 模板库](#prompt-模板库)

---

## 🔧 工具概览

### **4个核心MCP工具功能对比**

| 工具 | 主要功能 | 使用场景 | 触发方式 |
|------|----------|----------|----------|
| **mcp-feedback-enhanced** | 系统信息、交互反馈 | 系统诊断、用户反馈 | 直接对话 |
| **fetch** | 网页读取、数据获取 | 信息收集、内容分析 | 提供URL |
| **context7** | 上下文管理、记忆 | 项目管理、知识库 | 上下文相关请求 |
| **sequential-thinking** | 逻辑推理、步骤分析 | 复杂问题解决 | 复杂思考任务 |

---

## 🔧 mcp-feedback-enhanced 使用指南

### **功能说明**
- 🖥️ **系统信息获取** - 获取当前系统配置
- 💬 **交互式反馈** - 收集用户反馈和建议
- 🔍 **环境诊断** - 检查开发环境状态

### **触发Prompt示例**

#### **基础使用**
```
请获取系统信息
```

#### **详细诊断**
```
帮我检查当前开发环境的配置信息，包括：
- 操作系统版本
- Python 环境
- Node.js 版本
- 可用内存和存储空间
```

#### **问题诊断**
```
我的代码运行很慢，请帮我获取系统信息来诊断可能的性能问题
```

### **实际案例**

#### **案例1：环境配置检查**
**用户需求**: 配置新的开发环境
**Prompt**:
```
我刚安装了新的开发环境，请帮我获取系统信息，检查以下配置是否正确：
1. Python 版本是否 >= 3.8
2. Node.js 是否已安装
3. 可用内存是否足够开发使用
4. 磁盘空间是否充足
```

#### **案例2：性能问题排查**
**用户需求**: 解决程序运行缓慢问题
**Prompt**:
```
我的Python程序运行很慢，请获取系统信息帮我分析：
- CPU使用率情况
- 内存使用情况
- 是否有资源瓶颈
- 给出优化建议
```

---

## 🌐 fetch 网页读取工具

### **功能说明**
- 📄 **网页内容获取** - 读取任何网页的文本内容
- 🔄 **API数据获取** - 调用REST API获取数据
- 📊 **内容分析** - 分析网页结构和信息

### **触发Prompt示例**

#### **基础网页读取**
```
请读取 https://www.example.com 的内容
```

#### **内容分析**
```
请读取 https://news.ycombinator.com 并总结今天的热门技术新闻
```

#### **API数据获取**
```
请调用这个API获取数据：https://api.github.com/users/octocat
然后分析返回的用户信息
```

### **实际案例**

#### **案例1：技术文档学习**
**用户需求**: 学习新技术框架
**Prompt**:
```
请读取 https://reactjs.org/docs/getting-started.html 的内容，然后：
1. 总结React的核心概念
2. 提取关键的代码示例
3. 列出学习路径建议
4. 转换为Markdown格式保存
```

#### **案例2：竞品分析**
**用户需求**: 分析竞争对手网站
**Prompt**:
```
请读取以下竞品网站并进行对比分析：
1. https://competitor1.com
2. https://competitor2.com

分析内容包括：
- 产品功能特点
- 价格策略
- 用户体验设计
- 技术架构特点
生成对比报告
```

#### **案例3：新闻监控**
**用户需求**: 监控行业动态
**Prompt**:
```
请读取以下科技新闻网站：
- https://techcrunch.com
- https://www.theverge.com

筛选出与AI、机器学习相关的新闻，总结要点并按重要性排序
```

---

## 🧠 context7 上下文管理

### **功能说明**
- 📚 **项目上下文管理** - 管理项目相关信息
- 🔗 **知识库构建** - 建立知识关联
- 💾 **记忆功能** - 保存重要信息供后续使用

### **触发Prompt示例**

#### **项目管理**
```
请帮我管理这个项目的上下文信息，包括技术栈、进度和关键决策
```

#### **知识库构建**
```
请将这些技术文档整理到知识库中，建立相关性链接
```

#### **信息检索**
```
从项目上下文中查找关于数据库设计的相关信息
```

### **实际案例**

#### **案例1：项目文档管理**
**用户需求**: 管理大型项目文档
**Prompt**:
```
我正在开发一个电商系统，请帮我建立项目上下文：

项目信息：
- 技术栈：React + Node.js + MongoDB
- 团队规模：5人
- 开发周期：3个月
- 主要功能：用户管理、商品管理、订单系统、支付集成

请帮我：
1. 建立项目知识库结构
2. 关联相关技术文档
3. 设置里程碑提醒
4. 建立团队协作上下文
```

#### **案例2：学习进度跟踪**
**用户需求**: 跟踪技术学习进度
**Prompt**:
```
我正在学习机器学习，请帮我管理学习上下文：

学习计划：
- 数学基础（线性代数、概率论）
- Python编程
- 机器学习算法
- 深度学习框架

请帮我：
1. 跟踪学习进度
2. 关联学习资源
3. 记录重要概念
4. 建立知识图谱
```

---

## 🧠 sequential-thinking 序列思维

### **功能说明**
- 🔄 **逻辑推理** - 进行复杂的逻辑分析
- 📝 **步骤分解** - 将复杂问题分解为步骤
- 🎯 **决策支持** - 提供结构化的决策分析

### **触发Prompt示例**

#### **复杂问题分析**
```
请用序列思维分析这个复杂的技术架构问题
```

#### **决策分析**
```
我需要在多个技术方案中做选择，请帮我进行系统性分析
```

#### **问题分解**
```
这个项目很复杂，请帮我分解成可执行的步骤
```

### **实际案例**

#### **案例1：技术选型决策**
**用户需求**: 为新项目选择技术栈
**Prompt**:
```
我需要为一个中型Web应用选择技术栈，请用序列思维帮我分析：

项目需求：
- 用户量：10万+
- 功能：社交、内容管理、实时通信
- 团队：前端2人、后端2人、全栈1人
- 预算：中等
- 时间：6个月

候选方案：
1. React + Node.js + PostgreSQL
2. Vue.js + Django + MySQL
3. Next.js + Prisma + Supabase

请进行系统性分析，包括：
- 技术成熟度评估
- 团队技能匹配度
- 开发效率对比
- 长期维护成本
- 扩展性考虑
最终给出推荐方案和理由
```

#### **案例2：项目重构规划**
**用户需求**: 规划大型项目重构
**Prompt**:
```
我们的遗留系统需要重构，请用序列思维帮我制定重构计划：

现状：
- 单体应用，代码10万行+
- 技术栈老旧（PHP 5.6）
- 性能问题严重
- 维护困难

目标：
- 微服务架构
- 现代技术栈
- 提升性能10倍
- 保证业务连续性

请分析：
1. 风险评估和缓解策略
2. 重构优先级排序
3. 分阶段实施计划
4. 资源需求评估
5. 成功指标定义
```

---

## 🔄 组合使用案例

### **案例1：完整的技术调研流程**

**场景**: 为新项目进行全面技术调研

**步骤1 - 使用 fetch 收集信息**
```
请读取以下技术文档和官网：
1. https://reactjs.org/docs
2. https://vuejs.org/guide
3. https://angular.io/docs

提取各框架的特点、优势和适用场景
```

**步骤2 - 使用 sequential-thinking 分析**
```
基于刚才收集的信息，请用序列思维分析：
- 各框架的技术特点对比
- 适合我们项目的程度评估
- 学习成本和开发效率分析
- 给出选择建议
```

**步骤3 - 使用 context7 管理**
```
请将这次技术调研的结果整理到项目上下文中：
- 保存对比分析结果
- 建立技术选型知识库
- 关联相关文档链接
- 设置后续评估提醒
```

**步骤4 - 使用 mcp-feedback-enhanced 验证**
```
请获取当前系统信息，验证选择的技术栈是否与我们的开发环境兼容
```

### **案例2：问题诊断与解决**

**场景**: 生产环境性能问题排查

**步骤1 - 系统诊断**
```
生产环境出现性能问题，请先获取系统信息进行初步诊断
```

**步骤2 - 信息收集**
```
请读取以下监控页面和日志：
- https://monitoring.example.com/dashboard
- 获取最近的错误日志信息

分析性能瓶颈可能的原因
```

**步骤3 - 问题分析**
```
基于收集的信息，请用序列思维分析：
1. 问题的根本原因
2. 可能的解决方案
3. 解决方案的优先级
4. 实施风险评估
```

**步骤4 - 解决方案管理**
```
请将问题分析和解决方案保存到项目上下文中：
- 记录问题详情和分析过程
- 保存解决方案和实施计划
- 建立类似问题的知识库
- 设置后续监控提醒
```

---

## 📝 Prompt 模板库

### **🔧 mcp-feedback-enhanced 模板**

#### **系统检查模板**
```
请获取系统信息并检查：
- [具体检查项目1]
- [具体检查项目2]
- [具体检查项目3]
然后给出 [具体建议类型] 建议
```

#### **环境诊断模板**
```
我遇到了 [具体问题描述]，请获取系统信息帮我诊断：
1. 可能的原因分析
2. 解决方案建议
3. 预防措施
```

### **🌐 fetch 模板**

#### **内容分析模板**
```
请读取 [URL] 的内容，然后：
1. 总结 [具体要总结的内容]
2. 提取 [需要提取的信息]
3. 分析 [需要分析的方面]
4. 输出格式：[指定格式，如Markdown、表格等]
```

#### **对比分析模板**
```
请读取以下网站进行对比分析：
- [URL1] - [说明]
- [URL2] - [说明]

对比维度：
- [维度1]
- [维度2]
- [维度3]

输出：[具体要求的输出格式]
```

### **🧠 context7 模板**

#### **项目管理模板**
```
请帮我管理 [项目名称] 的上下文信息：

项目基本信息：
- [项目信息]

需要管理的内容：
1. [管理内容1]
2. [管理内容2]
3. [管理内容3]

请建立相应的知识库结构和关联关系
```

#### **学习跟踪模板**
```
我正在学习 [学习主题]，请帮我建立学习上下文：

学习计划：
- [计划内容]

请帮我：
1. 跟踪学习进度
2. 管理学习资源
3. 建立知识关联
4. 设置复习提醒
```

### **🧠 sequential-thinking 模板**

#### **决策分析模板**
```
我需要在 [决策场景] 中做选择，请用序列思维分析：

背景信息：
- [背景1]
- [背景2]

候选方案：
1. [方案1] - [简要说明]
2. [方案2] - [简要说明]

评估维度：
- [维度1]
- [维度2]
- [维度3]

请进行系统性分析并给出推荐
```

#### **问题分解模板**
```
我面临一个复杂问题：[问题描述]

约束条件：
- [约束1]
- [约束2]

目标：
- [目标1]
- [目标2]

请用序列思维将问题分解为可执行的步骤，并分析每个步骤的风险和注意事项
```

---

## 🎯 使用技巧与最佳实践

### **✅ 成功使用的关键**

1. **明确指令** - 清楚说明需要什么功能
2. **提供上下文** - 给出足够的背景信息
3. **指定格式** - 明确期望的输出格式
4. **分步骤** - 复杂任务分解为多个步骤

### **🚀 提升效果的技巧**

1. **组合使用** - 多个工具配合使用效果更好
2. **迭代优化** - 根据结果调整Prompt
3. **保存模板** - 建立常用Prompt模板库
4. **验证结果** - 交叉验证工具输出的准确性

### **⚠️ 注意事项**

1. **网络依赖** - fetch工具需要网络连接
2. **权限限制** - 某些网站可能限制访问
3. **数据隐私** - 注意敏感信息的处理
4. **性能考虑** - 大量数据处理可能较慢

---

## 🎨 高级使用技巧

### **🔄 工具链式调用**

#### **信息收集 → 分析 → 管理 → 验证**
```
# 第1步：收集信息
请读取 https://example.com/api-docs 的API文档

# 第2步：分析信息
基于刚才的API文档，请用序列思维分析最佳的集成方案

# 第3步：管理信息
请将API集成方案保存到项目上下文中

# 第4步：验证环境
请检查当前系统是否满足API集成的环境要求
```

### **🎯 场景化Prompt设计**

#### **开发场景**
```
作为一名 [角色，如前端开发者]，我需要 [具体需求]。
请使用 [指定工具] 帮我 [具体任务]，
输出格式要求：[格式要求]
```

#### **学习场景**
```
我正在学习 [技术/概念]，当前水平是 [水平描述]。
请 [具体学习任务]，
并将学习成果整理到知识库中。
```

#### **问题解决场景**
```
我遇到了 [问题类型] 问题：[问题描述]
环境信息：[环境描述]
请帮我：
1. 诊断问题原因
2. 提供解决方案
3. 给出预防建议
```

### **📊 效果评估指标**

#### **工具使用效果评估**
- ✅ **准确性** - 信息是否准确完整
- ✅ **相关性** - 结果是否符合需求
- ✅ **可操作性** - 建议是否可执行
- ✅ **时效性** - 信息是否及时有效

#### **Prompt优化指标**
- 🎯 **明确性** - 指令是否清晰明确
- 🔄 **完整性** - 上下文是否充分
- 📝 **结构性** - 格式是否规范
- 🚀 **效率性** - 是否能快速得到结果

---

## 🌟 实战项目案例

### **案例：构建个人知识管理系统**

#### **项目目标**
使用4个MCP工具构建一个完整的个人知识管理工作流

#### **实施步骤**

**第1阶段：信息收集**
```
# 使用 fetch 工具收集学习资源
请读取以下技术博客和文档：
1. https://blog.example.com/best-practices
2. https://docs.example.com/advanced-guide

提取关键知识点，整理成结构化笔记
```

**第2阶段：知识分析**
```
# 使用 sequential-thinking 分析知识结构
基于收集的知识点，请用序列思维分析：
1. 知识点之间的关联关系
2. 学习的最佳顺序
3. 重点和难点识别
4. 实践项目建议
```

**第3阶段：知识管理**
```
# 使用 context7 建立知识库
请将分析后的知识结构保存到个人知识库：
- 建立主题分类
- 创建知识关联图
- 设置学习进度跟踪
- 建立复习提醒机制
```

**第4阶段：环境优化**
```
# 使用 mcp-feedback-enhanced 优化学习环境
请检查我的学习环境配置：
- 开发工具是否齐全
- 系统性能是否满足需求
- 推荐学习环境优化建议
```

### **案例：技术团队协作优化**

#### **项目背景**
5人技术团队，需要优化协作流程和知识共享

#### **解决方案**

**信息收集阶段**
```
请读取以下团队协作相关资源：
1. https://guides.github.com/introduction/flow/
2. https://www.atlassian.com/agile/scrum
3. https://docs.gitlab.com/ee/development/

总结最佳实践和工具推荐
```

**流程设计阶段**
```
基于收集的最佳实践，请用序列思维设计团队协作流程：

团队情况：
- 5名开发者（2前端、2后端、1全栈）
- 远程协作为主
- 项目周期通常2-3个月

设计内容：
1. 代码协作流程
2. 知识分享机制
3. 问题解决流程
4. 团队沟通规范
```

**知识库建设**
```
请建立团队知识库管理系统：
- 技术文档分类管理
- 项目经验总结
- 问题解决案例库
- 团队技能图谱
- 学习资源共享
```

**环境标准化**
```
请制定团队开发环境标准：
- 统一开发工具配置
- 代码规范和检查工具
- 部署环境一致性
- 性能监控标准
```

---

## 🚀 进阶应用模式

### **🔄 自动化工作流**

#### **定期信息监控**
```
# 每日技术新闻摘要
请读取以下技术新闻网站：
- https://news.ycombinator.com
- https://dev.to
- https://medium.com/tag/programming

筛选出与我关注的技术栈相关的新闻，生成日报
```

#### **项目健康检查**
```
# 定期项目状态检查
请执行项目健康检查：
1. 获取系统资源使用情况
2. 检查项目依赖更新状态
3. 分析代码质量指标
4. 更新项目上下文信息
```

### **🎯 专业领域应用**

#### **前端开发专用**
```
# 前端性能优化分析
请读取 https://web.dev/performance/ 的性能优化指南，
然后分析我的项目性能优化方案：

项目信息：
- React + TypeScript
- 用户量：10万+
- 主要性能问题：首屏加载慢

请提供具体的优化建议和实施步骤
```

#### **后端开发专用**
```
# API设计最佳实践
请读取 RESTful API 设计规范，
然后用序列思维分析我的API设计：

当前API结构：
- 用户管理：/api/users/*
- 订单系统：/api/orders/*
- 支付集成：/api/payments/*

请评估设计合理性并提供改进建议
```

#### **DevOps专用**
```
# 部署流程优化
请分析当前的CI/CD流程并提供优化建议：

当前流程：
- Git push → GitHub Actions → Docker build → AWS ECS

请检查：
1. 流程效率和稳定性
2. 安全性考虑
3. 监控和日志
4. 回滚机制
```

---

## 📚 常见问题与解决方案

### **❓ 工具无响应怎么办？**

#### **诊断步骤**
```
# 1. 检查工具状态
请获取系统信息，检查MCP工具的运行状态

# 2. 简化请求
请读取 https://httpbin.org/get 测试fetch工具是否正常

# 3. 重新配置
如果工具无响应，请检查配置文件是否正确
```

### **❓ 如何提高响应准确性？**

#### **优化策略**
1. **提供详细上下文**
```
# ❌ 模糊请求
请分析这个网站

# ✅ 明确请求
请读取 https://example.com 的产品页面，
分析其用户体验设计特点，
重点关注导航结构和转化路径设计
```

2. **分步骤执行**
```
# ❌ 一次性复杂请求
请读取多个网站并进行全面分析

# ✅ 分步骤请求
第1步：请读取 https://site1.com 的首页内容
第2步：基于第1步的结果，分析用户体验设计
第3步：将分析结果保存到项目上下文
```

### **❓ 如何处理大量数据？**

#### **处理策略**
```
# 分批处理
请分批读取以下网站，每次处理2个：
批次1：site1.com, site2.com
批次2：site3.com, site4.com

每批次完成后暂停，等待确认再继续
```

---

## 🎯 成功案例分享

### **案例1：技术博客写作助手**

**需求**: 定期产出高质量技术博客

**解决方案**:
```
# 1. 收集灵感和素材
请读取以下技术社区的热门话题：
- https://dev.to/top/week
- https://stackoverflow.com/questions/tagged/javascript

筛选出适合写博客的话题

# 2. 深度研究
选定话题后，请用序列思维分析：
- 话题的技术深度和广度
- 目标读者群体
- 文章结构规划
- 代码示例需求

# 3. 建立写作素材库
请将研究成果整理到知识库：
- 话题分类管理
- 参考资料链接
- 写作大纲模板
- 代码示例库

# 4. 环境检查
请确认写作环境配置：
- Markdown编辑器
- 代码高亮工具
- 图片处理工具
- 发布平台准备
```

**效果**: 博客产出效率提升300%，内容质量显著提升

### **案例2：开源项目贡献流程**

**需求**: 系统化参与开源项目贡献

**解决方案**:
```
# 1. 项目调研
请读取目标开源项目的文档：
- README.md
- CONTRIBUTING.md
- 最近的Issues和PRs

分析项目现状和贡献机会

# 2. 贡献策略制定
基于项目调研，请用序列思维制定贡献计划：
- 技能匹配度评估
- 贡献类型选择（代码/文档/测试）
- 时间投入规划
- 学习路径设计

# 3. 贡献记录管理
请建立开源贡献管理系统：
- 项目信息跟踪
- 贡献历史记录
- 学习成果总结
- 社区关系维护

# 4. 开发环境准备
请检查开源开发环境：
- Git配置
- 开发工具链
- 测试环境
- 代码规范工具
```

**效果**: 成功参与5个开源项目，获得Maintainer身份

---

*最后更新：2025-01-27*
*基于实际使用经验编写*

> 💡 **提示**：这些Prompt模板可以根据具体需求进行调整和组合使用。
>
> 🎯 **建议**：从简单的单工具使用开始，逐步尝试组合使用以获得更好的效果。
>
> 🚀 **进阶**：建立自己的Prompt模板库，针对常见场景形成标准化工作流。
