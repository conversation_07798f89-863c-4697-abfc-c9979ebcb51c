# Augment Agent 用户指南（简化版）

## 基本要求
- 使用中文交流
- 每份文档必须验证日期准确性，使用命令行确认当日日期
- 严格按照任务计划执行，复杂任务使用 sequential-thinking 分析

## MCP服务优先使用
- interactive_feedback: 用户反馈交互
- sequential-thinking: 复杂任务分解与深度思考  
- shrimp-task-manager: 任务规划和管理
- 寸止MCP: AI对话智能拦截和记忆管理

## 技术标准
- 严格使用包管理器，禁止手动编辑配置文件
- 代码显示使用 <augment_code_snippet> 标签
- 需要用户许可才能执行：提交代码、安装依赖、部署代码

## 路径约定
- 任务计划: ./issues/任务名.md
- 任务复盘: ./rewind/任务名.md  
- 内容存储: ./notes/
- 推广图输出: ./cursor_projects/Ob/

## 知识重构原则
- 模式识别 > 细节记忆
- 生动形象 > 抽象概念
- 情感共鸣 > 理性说服
- 连接已知 > 全新信息
- 可行洞察 > 纯粹知识
