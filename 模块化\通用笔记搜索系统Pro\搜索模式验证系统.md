---
tags:
  - type/dashboard
  - test/search-verification
created: 2025-07-11T14:00
updated: 2025-07-11T14:00
---

# 🔬 搜索模式验证系统

```dataviewjs
// 搜索模式验证系统 - 显示详细的匹配过程
const container = this.container;

// 创建主容器
const mainDiv = document.createElement('div');
mainDiv.style.cssText = `
    font-family: var(--font-interface);
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
`;

// 测试区域
const testDiv = document.createElement('div');
testDiv.style.cssText = `
    background: var(--background-secondary);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--background-modifier-border);
`;

testDiv.innerHTML = `
    <h3 style="margin: 0 0 15px 0; color: var(--text-normal);">🔬 搜索模式验证</h3>
    <div style="margin-bottom: 15px;">
        <input type="text" id="verify-input" value="任务管理" placeholder="输入关键词..." 
               style="width: 100%; padding: 8px; border: 1px solid var(--background-modifier-border); border-radius: 4px; background: var(--background-primary); color: var(--text-normal);">
    </div>
    <div style="display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;">
        <button id="verify-single" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">单个关键词</button>
        <button id="verify-or" style="padding: 8px 16px; background: #17a2b8; color: white; border: none; border-radius: 4px; cursor: pointer;">OR 模式</button>
        <button id="verify-and" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">AND 模式</button>
        <button id="verify-exact" style="padding: 8px 16px; background: #6f42c1; color: white; border: none; border-radius: 4px; cursor: pointer;">精确匹配</button>
    </div>
    <div style="font-size: 12px; color: var(--text-muted);">
        💡 建议测试不同的关键词组合：<br>
        • "任务管理" - 测试中文词汇<br>
        • "Obsidian 搜索" - 测试英文+中文组合<br>
        • "MCP 工具 配置" - 测试三个词的组合
    </div>
`;

// 结果区域
const resultDiv = document.createElement('div');
resultDiv.style.cssText = `
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    padding: 20px;
    background: var(--background-primary);
    min-height: 300px;
`;

const resultContent = document.createElement('div');
resultContent.innerHTML = `
    <div style="text-align: center; color: var(--text-muted); padding: 40px;">
        <div style="font-size: 48px; margin-bottom: 10px;">🔬</div>
        <div style="font-size: 18px;">准备验证搜索模式</div>
    </div>
`;

resultDiv.appendChild(resultContent);

// 组装界面
mainDiv.appendChild(testDiv);
mainDiv.appendChild(resultDiv);
container.appendChild(mainDiv);

// 验证函数
async function verifySearchMode(mode) {
    const input = document.getElementById('verify-input');
    const keyword = input.value.trim();
    
    if (!keyword) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                <div>请输入关键词</div>
            </div>
        `;
        return;
    }
    
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
            <div>正在验证 ${mode} 模式...</div>
        </div>
    `;
    
    try {
        // 获取所有页面
        const allPages = dv.pages();
        const excludeDirs = ['.obsidian'];
        const searchPages = allPages.where(p => 
            !excludeDirs.some(dir => p.file.path.includes(dir))
        );
        
        // 处理搜索词
        let searchTerms;
        let modeDescription;
        
        switch (mode) {
            case 'SINGLE':
                searchTerms = [keyword];
                modeDescription = `单个关键词: "${keyword}"`;
                break;
            case 'OR':
                searchTerms = keyword.split(/\s+/).filter(k => k.length > 0);
                modeDescription = `OR模式: 包含 ${searchTerms.map(t => `"${t}"`).join(' 或 ')} 任意一个`;
                break;
            case 'AND':
                searchTerms = keyword.split(/\s+/).filter(k => k.length > 0);
                modeDescription = `AND模式: 必须同时包含 ${searchTerms.map(t => `"${t}"`).join(' 和 ')}`;
                break;
            case 'EXACT':
                searchTerms = [keyword];
                modeDescription = `精确匹配: 完整短语 "${keyword}"`;
                break;
        }
        
        const results = [];
        let totalChecked = 0;
        let matchDetails = [];
        
        for (const page of searchPages) {
            totalChecked++;
            try {
                const content = await dv.io.load(page.file.path);
                const fileName = page.file.name.replace('.md', '');
                
                // 检查文件名匹配
                const fileNameResult = verifyMatch(fileName, searchTerms, mode);
                
                // 检查内容匹配（只检查前1000字符以提高性能）
                const contentPreview = content.substring(0, 1000);
                const contentResult = verifyMatch(contentPreview, searchTerms, mode);
                
                if (fileNameResult.matched || contentResult.matched) {
                    results.push({
                        name: fileName,
                        path: page.file.path,
                        link: page.file.link,
                        fileNameMatch: fileNameResult,
                        contentMatch: contentResult,
                        mtime: page.file.mtime
                    });
                }
                
                // 收集匹配详情（只显示前10个）
                if (matchDetails.length < 10) {
                    matchDetails.push({
                        fileName: fileName,
                        fileNameResult: fileNameResult,
                        contentResult: contentResult
                    });
                }
                
            } catch (error) {
                // 跳过无法读取的文件
            }
        }
        
        displayVerificationResults(results, searchTerms, mode, modeDescription, totalChecked, matchDetails);
        
    } catch (error) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-error); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div>验证失败: ${error.message}</div>
            </div>
        `;
    }
}

// 验证匹配函数
function verifyMatch(text, searchTerms, mode) {
    if (!text || !searchTerms || searchTerms.length === 0) {
        return { matched: false, details: '无效输入', terms: [] };
    }
    
    const lowerText = text.toLowerCase();
    const lowerTerms = searchTerms.map(term => term.toLowerCase());
    
    switch (mode) {
        case 'SINGLE':
            const singleMatched = lowerText.includes(lowerTerms[0]);
            return {
                matched: singleMatched,
                details: `搜索 "${searchTerms[0]}" → ${singleMatched ? '找到' : '未找到'}`,
                terms: singleMatched ? [searchTerms[0]] : []
            };
            
        case 'OR':
            const orMatches = lowerTerms.filter(term => lowerText.includes(term));
            return {
                matched: orMatches.length > 0,
                details: `需要任一: ${searchTerms.join(', ')} → 找到: ${orMatches.join(', ') || '无'}`,
                terms: orMatches
            };
            
        case 'AND':
            const andMatches = lowerTerms.filter(term => lowerText.includes(term));
            const andMatched = andMatches.length === lowerTerms.length;
            return {
                matched: andMatched,
                details: `需要全部: ${searchTerms.join(', ')} → 找到: ${andMatches.join(', ') || '无'} → ${andMatched ? '完全匹配' : '不完全'}`,
                terms: andMatches
            };
            
        case 'EXACT':
            const exactMatched = lowerText.includes(searchTerms[0].toLowerCase());
            return {
                matched: exactMatched,
                details: `精确匹配 "${searchTerms[0]}" → ${exactMatched ? '找到' : '未找到'}`,
                terms: exactMatched ? [searchTerms[0]] : []
            };
            
        default:
            return { matched: false, details: '未知模式', terms: [] };
    }
}

// 显示验证结果
function displayVerificationResults(results, searchTerms, mode, modeDescription, totalChecked, matchDetails) {
    let html = `
        <div style="margin-bottom: 20px; padding: 15px; background: var(--background-secondary); border-radius: 6px; border-left: 4px solid var(--interactive-accent);">
            <h4 style="margin: 0 0 10px 0; color: var(--text-normal);">🔬 ${mode} 模式验证结果</h4>
            <div style="color: var(--text-muted); font-size: 14px; margin-bottom: 10px;">
                <strong>搜索逻辑:</strong> ${modeDescription}<br>
                <strong>检查文件数:</strong> ${totalChecked}<br>
                <strong>匹配结果数:</strong> ${results.length}
            </div>
        </div>
    `;
    
    // 显示匹配详情示例
    if (matchDetails.length > 0) {
        html += `
            <div style="margin-bottom: 20px; padding: 15px; background: var(--background-secondary); border-radius: 6px;">
                <h5 style="margin: 0 0 10px 0; color: var(--text-normal);">🔍 匹配过程示例（前5个文件）</h5>
        `;
        
        matchDetails.slice(0, 5).forEach(detail => {
            const hasMatch = detail.fileNameResult.matched || detail.contentResult.matched;
            html += `
                <div style="margin-bottom: 8px; padding: 8px; background: var(--background-primary); border-radius: 4px; border-left: 3px solid ${hasMatch ? 'var(--color-green)' : 'var(--color-red)'};">
                    <div style="font-size: 12px; font-weight: bold; margin-bottom: 4px;">${detail.fileName}</div>
                    <div style="font-size: 11px; color: var(--text-muted); font-family: monospace;">
                        文件名: ${detail.fileNameResult.details}<br>
                        内容: ${detail.contentResult.details}
                    </div>
                </div>
            `;
        });
        
        html += `</div>`;
    }
    
    // 显示匹配的文件列表
    if (results.length === 0) {
        html += `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">此模式下未找到匹配结果</div>
                <div style="font-size: 14px;">尝试其他关键词或搜索模式</div>
            </div>
        `;
    } else {
        html += `<h5 style="margin: 20px 0 10px 0; color: var(--text-normal);">📄 匹配的文件列表</h5>`;
        
        results.slice(0, 10).forEach(result => {
            const date = new Date(result.mtime).toLocaleDateString('zh-CN');
            const pathParts = result.path.split('/');
            const directory = pathParts.length > 1 ? pathParts[0] : '根目录';
            
            html += `
                <div style="border: 1px solid var(--background-modifier-border); border-radius: 8px; margin-bottom: 10px; padding: 12px; background: var(--background-secondary);">
                    <h6 style="margin: 0 0 5px 0; color: var(--text-normal);">
                        <a href="${result.link}" style="text-decoration: none; color: var(--link-color); font-weight: bold;">
                            📄 ${result.name}
                        </a>
                    </h6>
                    <div style="font-size: 11px; color: var(--text-muted); margin-bottom: 5px;">
                        📁 ${directory} | 📅 ${date}
                    </div>
                    <div style="font-size: 10px; color: var(--text-muted); font-family: monospace;">
                        ${result.fileNameMatch.matched ? '✅ 文件名匹配' : '❌ 文件名不匹配'} | 
                        ${result.contentMatch.matched ? '✅ 内容匹配' : '❌ 内容不匹配'}
                    </div>
                </div>
            `;
        });
        
        if (results.length > 10) {
            html += `<div style="text-align: center; color: var(--text-muted); margin-top: 10px;">... 还有 ${results.length - 10} 个结果</div>`;
        }
    }
    
    resultContent.innerHTML = html;
}

// 事件绑定
setTimeout(() => {
    const verifyInput = document.getElementById('verify-input');
    const verifySingleBtn = document.getElementById('verify-single');
    const verifyOrBtn = document.getElementById('verify-or');
    const verifyAndBtn = document.getElementById('verify-and');
    const verifyExactBtn = document.getElementById('verify-exact');
    
    if (verifySingleBtn) verifySingleBtn.onclick = () => verifySearchMode('SINGLE');
    if (verifyOrBtn) verifyOrBtn.onclick = () => verifySearchMode('OR');
    if (verifyAndBtn) verifyAndBtn.onclick = () => verifySearchMode('AND');
    if (verifyExactBtn) verifyExactBtn.onclick = () => verifySearchMode('EXACT');
    
    if (verifyInput) {
        verifyInput.onkeypress = function(e) {
            if (e.key === 'Enter') {
                verifySearchMode('SINGLE');
            }
        };
    }
}, 100);
```

## 🔬 验证说明

### 为什么"任务管理"结果都一样？

这个验证系统会显示详细的匹配过程，帮助理解为什么不同模式的结果相同：

1. **单个关键词**：搜索"任务管理"这个完整词汇
2. **OR 模式**：搜索包含"任务"或"管理"的文件
3. **AND 模式**：搜索同时包含"任务"和"管理"的文件
4. **精确匹配**：搜索包含"任务管理"完整短语的文件

### 🧪 建议测试

尝试这些关键词来看到明显区别：
- **"Obsidian 搜索"** - 英文+中文组合
- **"MCP 工具 配置"** - 三个词的组合
- **"系统 管理"** - 可能有区别的中文组合

这个系统会显示每个文件的详细匹配过程，帮助理解搜索逻辑！

---

*💡 现在可以看到每种搜索模式的具体工作过程了！*
