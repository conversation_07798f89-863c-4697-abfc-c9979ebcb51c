# Python脚本封装完整指南

## 🔍 脚本分析结果

### 文件基本信息
| 文件名 | 大小 | 数字签名 | 推测功能 |
|--------|------|----------|----------|
| CursorPro.exe | 15.97 MB | 无签名 | Cursor编辑器激活工具 |
| CursorReset.exe | 17.66 MB | 无签名 | Cursor设置重置工具 |
| cursorFreeReset.exe | 9.82 MB | 无签名 | 免费版重置工具 |

### 封装特征分析
- **标准PE格式**：所有文件都是标准Windows可执行文件
- **无数字签名**：说明是第三方工具，非官方软件
- **大文件体积**：包含完整Python运行环境和依赖库
- **正常熵值**：未经过高度压缩或加密处理

## 🛠️ Python脚本封装工具对比

### 1. PyInstaller（推荐）
**优点**：
- 支持最多的Python库
- 跨平台支持（Windows/Mac/Linux）
- 社区活跃，文档完善
- 支持隐藏导入和动态加载

**缺点**：
- 生成文件较大
- 启动速度相对较慢

**安装**：
```bash
pip install pyinstaller
```

### 2. auto-py-to-exe（GUI版PyInstaller）
**优点**：
- 图形界面，操作简单
- 基于PyInstaller，功能完整
- 实时预览配置

**安装**：
```bash
pip install auto-py-to-exe
```

### 3. cx_Freeze
**优点**：
- 跨平台支持
- 生成文件相对较小
- 配置灵活

**缺点**：
- 对某些库支持不够完善
- 学习曲线较陡

### 4. Nuitka
**优点**：
- 真正编译为机器码
- 执行速度快
- 文件大小适中

**缺点**：
- 编译时间长
- 对某些动态特性支持有限

## 📋 PyInstaller详细使用指南

### 基础打包命令
```bash
# 基础打包（生成文件夹）
pyinstaller script.py

# 打包为单个exe文件
pyinstaller --onefile script.py

# 无控制台窗口（GUI应用）
pyinstaller --onefile --windowed script.py

# 自定义图标
pyinstaller --onefile --icon=icon.ico script.py
```

### 高级配置选项
```bash
# 添加数据文件
pyinstaller --onefile --add-data "data.txt;." script.py

# 添加二进制文件
pyinstaller --onefile --add-binary "lib.dll;." script.py

# 隐藏导入
pyinstaller --onefile --hidden-import=module_name script.py

# 排除模块（减小文件大小）
pyinstaller --onefile --exclude-module=tkinter script.py
```

## 🎯 实战示例：创建简单工具

### 示例脚本（cursor_tool.py）
```python
import os
import sys
import json
import shutil
from pathlib import Path

def reset_cursor_settings():
    """重置Cursor编辑器设置"""
    cursor_paths = [
        os.path.expanduser("~/.cursor"),
        os.path.expanduser("~/AppData/Roaming/Cursor"),
        os.path.expanduser("~/Library/Application Support/Cursor")
    ]
    
    for path in cursor_paths:
        if os.path.exists(path):
            try:
                shutil.rmtree(path)
                print(f"已删除: {path}")
            except Exception as e:
                print(f"删除失败 {path}: {e}")
    
    print("Cursor设置重置完成！")

def main():
    print("=== Cursor设置重置工具 ===")
    choice = input("确认重置Cursor设置？(y/N): ")
    if choice.lower() == 'y':
        reset_cursor_settings()
    else:
        print("操作已取消")
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
```

### 打包配置文件（cursor_tool.spec）
```python
# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['cursor_tool.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'numpy'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='CursorReset',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='cursor.ico'
)
```

### 打包步骤
```bash
# 1. 生成spec文件
pyinstaller --onefile cursor_tool.py

# 2. 编辑spec文件（添加优化配置）
# 3. 使用spec文件打包
pyinstaller cursor_tool.spec
```

## 🚀 文件大小优化技巧

### 1. 排除不需要的模块
```bash
pyinstaller --onefile --exclude-module=tkinter --exclude-module=matplotlib script.py
```

### 2. 使用UPX压缩
```bash
# 安装UPX
# 在spec文件中设置 upx=True
```

### 3. 虚拟环境隔离
```bash
# 创建干净的虚拟环境
python -m venv clean_env
clean_env\Scripts\activate
pip install 只需要的包
pyinstaller script.py
```

## 🔒 安全性考虑

### 1. 代码混淆
- 使用pyarmor进行代码混淆
- 避免硬编码敏感信息

### 2. 数字签名
```bash
# 使用signtool添加数字签名
signtool sign /f certificate.pfx /p password /t http://timestamp.server app.exe
```

### 3. 病毒扫描
- 打包后进行病毒扫描
- 使用知名杀毒软件测试

## 📦 分发策略

### 1. 单文件分发
- 优点：部署简单
- 缺点：启动较慢，文件较大

### 2. 文件夹分发
- 优点：启动快速
- 缺点：文件较多

### 3. 安装包制作
- 使用NSIS或Inno Setup
- 提供卸载功能
- 添加快捷方式

## 🔧 常见问题解决

### 1. 模块找不到
```bash
pyinstaller --hidden-import=missing_module script.py
```

### 2. 文件路径问题
```python
# 获取正确的资源路径
def resource_path(relative_path):
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath("."), relative_path)
```

### 3. 启动速度优化
- 减少导入的模块
- 使用延迟导入
- 考虑使用Nuitka编译

## 📚 推荐资源

- [PyInstaller官方文档](https://pyinstaller.readthedocs.io/)
- [auto-py-to-exe项目](https://github.com/brentvollebregt/auto-py-to-exe)
- [Python打包最佳实践](https://packaging.python.org/)

---

*注意：本指南仅用于学习目的，请遵守相关软件的使用条款和法律法规。*
