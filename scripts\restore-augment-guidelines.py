#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Augment Guidelines 配置恢复脚本
解决 .augment-guidelines 文件重启后重置为空的问题
"""

import os
import shutil
import sys
from pathlib import Path

def main():
    """主函数"""
    # 项目根目录
    project_root = Path(__file__).parent.parent
    
    # 源文件和目标文件路径
    source_file = project_root / "docs" / "Augment Agent工作偏好设置.md"
    guidelines_file = project_root / ".augment-guidelines"
    backup_file = project_root / "config" / "augment-guidelines-backup.md"
    
    print("🔧 Augment Guidelines 配置恢复工具")
    print("=" * 50)
    
    # 检查源文件是否存在
    if not source_file.exists():
        print(f"❌ 源文件不存在: {source_file}")
        return False
    
    # 创建备份目录
    backup_file.parent.mkdir(exist_ok=True)
    
    # 备份当前的工作偏好设置
    try:
        shutil.copy2(source_file, backup_file)
        print(f"✅ 已备份工作偏好设置到: {backup_file}")
    except Exception as e:
        print(f"⚠️ 备份失败: {e}")
    
    # 恢复 .augment-guidelines 文件
    try:
        # 读取工作偏好设置内容
        with open(source_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 写入到 .augment-guidelines
        with open(guidelines_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 已恢复 .augment-guidelines 文件")
        print(f"📁 文件位置: {guidelines_file}")
        print(f"📊 文件大小: {guidelines_file.stat().st_size} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ 恢复失败: {e}")
        return False

def check_status():
    """检查当前状态"""
    project_root = Path(__file__).parent.parent
    guidelines_file = project_root / ".augment-guidelines"
    
    print("\n📋 当前状态检查:")
    print("-" * 30)
    
    if guidelines_file.exists():
        size = guidelines_file.stat().st_size
        print(f"✅ .augment-guidelines 存在 ({size} 字节)")
        
        if size == 0:
            print("⚠️ 文件为空，需要恢复")
            return False
        else:
            print("✅ 文件有内容")
            return True
    else:
        print("❌ .augment-guidelines 不存在")
        return False

if __name__ == "__main__":
    # 检查当前状态
    is_ok = check_status()
    
    if not is_ok:
        print("\n🔄 开始恢复配置...")
        success = main()
        
        if success:
            print("\n🎉 配置恢复完成！")
            print("\n💡 使用建议:")
            print("1. 将此脚本添加到启动脚本中")
            print("2. 每次启动 Augment Agent 前运行此脚本")
            print("3. 定期检查配置文件状态")
        else:
            print("\n❌ 配置恢复失败，请检查错误信息")
    else:
        print("\n✅ 配置文件状态正常，无需恢复")
