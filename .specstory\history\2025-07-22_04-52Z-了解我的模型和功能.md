<!-- Generated by SpecStory -->

# 了解我的模型和功能 (2025-07-22 04:52Z)

_**User**_

你是什么模型

---

_**Assistant**_

[模式：研究]

我是Claude 3.7 Sonnet，一个由Anthropic开发的AI助手，目前在Cursor IDE中为您提供编程支持。我可以帮助您完成代码编写、分析、调试等各种编程任务，并严格遵循您设定的沟通规则和工作流程。

需要我协助您完成什么编程任务吗？

---

_**User**_

环境变量如何设置

---

_**Assistant**_



---

_**User**_

环境变量如何设置

---

_**Assistant**_

[模式：研究]

在Windows系统中设置环境变量有以下几种方法：

**1. 通过系统界面设置（永久性）**
- 右键点击"此电脑"或"我的电脑" → 属性 → 高级系统设置 → 环境变量
- 可以设置"用户变量"（仅当前用户）或"系统变量"（所有用户）

**2. 通过PowerShell命令行设置**

**临时性设置**（仅在当前会话有效）:
```powershell
$env:变量名="变量值"
```

**永久性设置**:
- 用户级别:
```powershell
[Environment]::SetEnvironmentVariable("变量名", "变量值", "User")
```

- 系统级别(需要管理员权限):
```powershell
[Environment]::SetEnvironmentVariable("变量名", "变量值", "Machine")
```

**3. 查看环境变量**:
```powershell
$env:变量名  # 查看特定变量
Get-ChildItem Env:  # 查看所有环境变量
```

您需要设置哪种类型的环境变量？是开发项目相关的还是系统配置相关的？

---

