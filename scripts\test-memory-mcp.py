#!/usr/bin/env python3
"""
Memory MCP 配置测试脚本
测试 memory MCP 服务器的基本功能和配置兼容性
"""

import json
import subprocess
import sys
import time
import os
from pathlib import Path

def test_memory_mcp_installation():
    """测试 memory MCP 是否正确安装"""
    print("🔍 测试 Memory MCP 安装...")
    
    try:
        # 测试 npx 命令是否可用
        result = subprocess.run(
            ["npx", "-y", "@modelcontextprotocol/server-memory", "--help"],
            capture_output=True,
            text=True,
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ Memory MCP 安装测试成功")
            return True
        else:
            print(f"❌ Memory MCP 安装测试失败: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ Memory MCP 测试超时（这可能是正常的，因为服务器在等待输入）")
        return True
    except Exception as e:
        print(f"❌ Memory MCP 测试异常: {e}")
        return False

def validate_config_file():
    """验证配置文件格式"""
    print("🔍 验证配置文件格式...")
    
    config_path = Path("config/mcp/augment/Augment-包含Memory-MCP配置.json")
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要的结构
        if "mcpServers" not in config:
            print("❌ 配置文件缺少 mcpServers 节点")
            return False
        
        if "memory" not in config["mcpServers"]:
            print("❌ 配置文件缺少 memory 服务器配置")
            return False
        
        memory_config = config["mcpServers"]["memory"]
        
        # 检查 memory 配置
        required_fields = ["command", "args"]
        for field in required_fields:
            if field not in memory_config:
                print(f"❌ Memory 配置缺少必要字段: {field}")
                return False
        
        print("✅ 配置文件格式验证成功")
        print(f"📊 配置的 MCP 服务器数量: {len(config['mcpServers'])}")
        
        # 列出所有配置的服务器
        servers = list(config["mcpServers"].keys())
        print(f"📋 配置的服务器: {', '.join(servers)}")
        
        return True
        
    except FileNotFoundError:
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件 JSON 格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 配置文件验证异常: {e}")
        return False

def check_memory_storage_path():
    """检查内存存储路径"""
    print("🔍 检查内存存储路径...")
    
    storage_path = Path("memory.json")
    
    if storage_path.exists():
        print(f"📁 内存文件已存在: {storage_path.absolute()}")
        try:
            with open(storage_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            print(f"📊 现有内存数据结构: {list(data.keys()) if isinstance(data, dict) else type(data)}")
        except Exception as e:
            print(f"⚠️ 读取现有内存文件失败: {e}")
    else:
        print(f"📁 内存文件将创建在: {storage_path.absolute()}")
    
    # 检查目录写权限
    try:
        test_file = Path("test_write_permission.tmp")
        test_file.write_text("test")
        test_file.unlink()
        print("✅ 目录写权限正常")
        return True
    except Exception as e:
        print(f"❌ 目录写权限检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始 Memory MCP 配置测试")
    print("=" * 50)
    
    tests = [
        ("Memory MCP 安装", test_memory_mcp_installation),
        ("配置文件验证", validate_config_file),
        ("存储路径检查", check_memory_storage_path),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 执行测试: {test_name}")
        print("-" * 30)
        
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！Memory MCP 配置就绪")
        return True
    else:
        print("⚠️ 部分测试失败，请检查配置")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
