📁 测试库/
├── 📚 obsidian-vault/     # 完整的Obsidian知识库
├── 🔧 tools/              # 标准化开发工具
├── ⚙️ config/             # 配置文件管理
├── 📚 docs/               # 文档体系
├── 📜 scripts/            # 自动化脚本
├── 📤 output/             # 输出文件
├── 🛠️ shrimp-data /       # 任务规划记忆
├── ⚒ issues/              # 任务计划
├── 🗂️ cursor_projects/    # 项目文件
├── 📝 notes/                # 文档笔记
├── 🔄 rewind/              # 任务复盘
├── 🤖 Chat/                 # 对话后总结
└── 🗃️ temp/                  # 临时文件
├── .cunzhi-memory /          # 寸止MCP管理记忆
├── .specstory /                    # Cursor 聊天记录
├── .augment/rules              # augment rules 项目Rules
├── .augment-guidelines       # augment 全局 User Guidelines
├── .cursor/rules                   # Cursor rules 项目Rules
├── .cursorindexingignore     # Cursor索引检索
├── .env                                # 环境配置
├── .gitignore                        # Git忽略规则
├── package.json                   # Node.js依赖
├── README.md                    # 项目主文档
├── requirements.txt              # Python依赖

