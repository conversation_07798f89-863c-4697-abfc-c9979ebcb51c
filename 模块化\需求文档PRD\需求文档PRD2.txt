已完成 PRD V1.0 初稿并获得确认。下一阶段通常包含：



交互设计（信息架构、用户流程、低保真原型）

视觉设计（设计系统、色彩/排版、高清稿）

技术架构（前后端技术选型、数据库模型、API 规范）



交互设计（Figma）→ 信息架构、线框、可点击原型

视觉设计（Material3）→ Design System、深浅色主题、高保真稿

技术架构：React Native + Expo / Next.js、NestJS + Prisma、PostgreSQL，CI/CD (GitHub Actions + Vercel + Supabase)

TDD：Jest / React-Native-Testing-Library、Supertest、Playwright

时间线：6 周完成 MVP（详见步骤清单）



📋 [模式：计划] 方案1（快速验证型）详细执行清单


— 交互设计 —



docs/IA.drawio：输出信息架构图。

docs/wireframes/：Figma 低保真线框（登陆/仪表板/番茄钟/设置）。

docs/prototype-link.md：可点击原型 URL 记录。


— 视觉设计 —
4. design-system/：建立 Design Tokens（颜色、排版、间距）。
5. 导入 MUI + React Native Paper 组件库，对应 Token 映射。
6. design-system/style-guide.md：视觉规范文档。


— 技术架构 —
7. Monorepo：apps/mobile (React Native + Expo)、apps/web (Next.js)、apps/server (NestJS)、packages/shared。
8. 初始化 ESLint/Prettier/Husky，统一代码风格。
9. 配置 Prisma + PostgreSQL schema（user、activity、energy_score）。
10. 实现 MVP API：/auth, /energy/score, /sync（含 Swagger 文档）。
11. 部署：Vercel (web)；Supabase (DB+server)；Expo EAS（移动）。


— TDD & Playwright —
12. Jest 单元测试：核心算法 packages/shared/energy.ts，覆盖率≥90%。
13. Playwright E2E：注册、仪表板渲染、番茄钟倒计时。
14. GitHub Actions：CI ➜ 单元 + E2E ➜ 自动部署。


— 时间线 —
• T+0–1 周：交互 & 视觉初稿；技术仓库初始化。
• T+2–3 周：MVP 功能开发 + 单元测试。
• T+4 周：E2E 测试 & Beta 发布。


请确认上述计划，或指出需调整的文件/步骤/时间节点。确认后将进入[模式：执行]逐项落地。


--------------------------------------

#背景 

#任务 

#功能综述 

##功能设计 

##整体链路 

##输入 

##处理 

##输出  


#要求


----------------------------------------------------------

你是团队的“执行者”，负责准确完成分配的具体任务，生成代码和文档。

核心工作原则：

所有工作基于《需求定义文档》(REQ_xxx.md) 和《项目任务清单》(TASKLIST_xxx.md)，确保理解任务描述和验收标准。仅完成当前任务，不进行无关操作或扩展。按任务要求格式输出内容，使用 Python 3.8+ 语法和类型提示，使用
logging 模块记录关键信息，妥善处理错误。有疑问时，立即暂停并澄清，避免基于假设执行。保持专业、精确、客观的沟通，高质量产出对项目成功至关重要。

重要限制与权力边界：

无需求定义和功能变更决策权，所有相关决策由项目主导者负责。遇到超出任务范围或与文档不符的情况，报告问题，不自行解决或修改。

工作流程简述：

接收：REQ_xxx.md 和 TASKLIST_xxx.md，理解项目目标和任务分解，但不执行具体任务。确认理解并待命，准备好接收具体任务指令。接收任务详细指令（任务ID、描述、提示词、验收标准）。严格按照任务指令执行。完成任务后，提供产出，总结执行情况，列出偏差。等待审查反馈，根据反馈修正，等待下一个任务指令。

严格遵守以上准则，确保团队开发效率和项目质量。