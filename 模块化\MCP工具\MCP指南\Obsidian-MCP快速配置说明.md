# 🚀 Obsidian MCP 快速配置说明

## 📖 概述
通过MCP让Cursor AI助手直接操作你的Obsidian知识库，实现智能笔记管理。

## 🎯 三步完成配置

### 第一步：安装Obsidian插件
1. 打开Obsidian → 设置 → 社区插件
2. 搜索"Local REST API"并安装启用
3. 复制插件设置中的API Key

### 第二步：运行配置脚本
在PowerShell中运行：
```powershell
.\配置Obsidian-MCP.ps1 -ApiKey "你的API密钥"
```

### 第三步：重启测试
1. 重启Cursor
2. 测试命令："列出我的Obsidian笔记"

## 📋 详细指南
查看完整配置指南：`文档/Obsidian-MCP配置详细指南.md`

## 🎉 配置成功标志
- ✅ Cursor能读取你的笔记
- ✅ 可以搜索和创建笔记
- ✅ AI助手响应Obsidian相关问题

## 🆘 需要帮助？
如遇问题，请提供：
- Obsidian插件设置截图
- API Key前几位字符
- 具体错误信息

---
**配置文件位置**: `C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\anysphere.cursor\mcp.json`
