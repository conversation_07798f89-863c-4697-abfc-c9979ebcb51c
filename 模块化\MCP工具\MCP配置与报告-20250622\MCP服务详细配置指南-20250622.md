---
created: 2025-06-22 10:55
updated: 2025-06-22 10:55
problem:
  - "MCP服务配置"
subject:
  - "配置指南"
importance: "高"
tracking: true
timeliness: "及时"
scenario:
  - "技术配置"
project:
  - "MCP完整报告"
Area:
  - "技术文档"
content_type: "配置指南"
Status: "进行中"
tags:
  - "MCP"
  - "配置"
  - "服务部署"
---

# MCP服务详细配置指南

## 📋 概述

本指南为9个核心MCP服务提供标准化的配置方法，包括安装步骤、配置参数、环境变量设置等详细信息。所有配置信息基于实际测试的配置文件，确保准确性和可用性。

## 🛠️ 通用配置原则

### 配置文件位置
- **Cursor IDE**: `C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\anysphere.cursor\mcp.json`
- **Augment IDE**: `%APPDATA%\Augment\mcp_config.json`

### 基础配置结构
```json
{
  "mcpServers": {
    "服务名称": {
      "command": "启动命令",
      "args": ["参数列表"],
      "env": {
        "环境变量": "值"
      },
      "timeout": 600,
      "autoApprove": ["自动批准的工具"]
    }
  }
}
```

## 🔧 核心MCP服务配置

### 1. 📝 mcp-feedback-enhanced

**功能**: 用户交互反馈工具，系统信息获取

**安装命令**:
```bash
# 自动安装（推荐）
uvx mcp-feedback-enhanced@latest
```

**Cursor配置**:
```json
{
  "mcp-feedback-enhanced": {
    "command": "uvx",
    "args": ["mcp-feedback-enhanced@latest"],
    "timeout": 600,
    "autoApprove": ["interactive_feedback"]
  }
}
```

**Augment配置**:
```json
{
  "mcp-feedback-enhanced": {
    "command": "uvx",
    "args": ["mcp-feedback-enhanced"]
  }
}
```

**环境变量**: 无需额外环境变量

**验证方法**:
```
测试命令: "请获取系统信息"
预期结果: 返回系统配置信息
```

### 2. 📚 mcp-obsidian

**功能**: Obsidian知识库操作，读取、搜索、创建笔记

**前置条件**:
1. Obsidian已安装并运行
2. 安装并启用"Local REST API"插件
3. 获取API密钥

**安装命令**:
```bash
# 方法1: 使用uv
uv tool install mcp-obsidian

# 方法2: 使用pip
pip install mcp-obsidian
```

**Cursor配置**:
```json
{
  "mcp-obsidian": {
    "command": "uv",
    "args": [
      "tool",
      "run",
      "mcp-obsidian",
      "--vault-path",
      "C:\\Users\\<USER>\\Desktop\\测试库"
    ],
    "env": {
      "OBSIDIAN_API_KEY": "你的API密钥",
      "OBSIDIAN_HOST": "127.0.0.1",
      "OBSIDIAN_PORT": "27124"
    }
  }
}
```

**Augment配置**:
```json
{
  "mcp-obsidian": {
    "command": "uvx",
    "args": ["mcp-obsidian"],
    "env": {
      "OBSIDIAN_API_KEY": "你的API密钥",
      "OBSIDIAN_HOST": "https://127.0.0.1:27124/"
    }
  }
}
```

**环境变量**:
- `OBSIDIAN_API_KEY`: 必需，从Local REST API插件获取
- `OBSIDIAN_HOST`: 默认127.0.0.1
- `OBSIDIAN_PORT`: 默认27124

**验证方法**:
```
测试命令: "列出我的Obsidian知识库中的所有文件"
预期结果: 返回文件列表
```

### 3. 📖 context7

**功能**: 查询最新库文档和示例，技术文档检索

**安装命令**:
```bash
# 使用npx（推荐）
npx context7
```

**Cursor配置**:
```json
{
  "context7": {
    "command": "uvx",
    "args": ["context7-mcp"],
    "env": {
      "CONTEXT7_API_KEY": "你的API密钥"
    }
  }
}
```

**Augment配置**:
```json
{
  "context7": {
    "command": "npx",
    "args": ["context7"]
  }
}
```

**环境变量**:
- `CONTEXT7_API_KEY`: 可选，用于高级功能

**验证方法**:
```
测试命令: "查询React库的最新文档"
预期结果: 返回React相关文档信息
```

### 4. 🌐 playwright

**功能**: 浏览器自动化操作，网页截图，自动化测试

**安装命令**:
```bash
# 使用npx
npx @executeautomation/playwright-mcp-server
```

**Cursor配置**:
```json
{
  "playwright": {
    "command": "uvx",
    "args": ["@executeautomation/playwright-mcp-server"],
    "env": {
      "PLAYWRIGHT_API_KEY": "07885b1c-a040-453f-8685-c0d6ee0336e8"
    }
  }
}
```

**Augment配置**:
```json
{
  "playwright": {
    "command": "npx",
    "args": ["@executeautomation/playwright-mcp-server"],
    "env": {
      "PLAYWRIGHT_API_KEY": "07885b1c-a040-453f-8685-c0d6ee0336e8"
    }
  }
}
```

**环境变量**:
- `PLAYWRIGHT_API_KEY`: 必需，用于服务认证

**验证方法**:
```
测试命令: "请截图百度首页"
预期结果: 返回网页截图
```

### 5. 🎨 replicate-flux-mcp

**功能**: AI图像生成（Replicate平台），高质量图像生成

**安装命令**:
```bash
# 使用uvx
uvx replicate-flux-mcp
```

**Cursor配置**:
```json
{
  "replicate-flux": {
    "command": "uvx",
    "args": ["replicate-flux-mcp"],
    "env": {
      "REPLICATE_API_TOKEN": "****************************************"
    }
  }
}
```

**Augment配置**:
```json
{
  "replicate-flux-mcp": {
    "command": "uvx",
    "args": ["replicate-flux-mcp"],
    "env": {
      "REPLICATE_API_TOKEN": "****************************************"
    }
  }
}
```

**环境变量**:
- `REPLICATE_API_TOKEN`: 必需，Replicate平台API令牌

**验证方法**:
```
测试命令: "生成一张山水画"
预期结果: 返回生成的图像
```

### 6. 🖼️ together-image-gen

**功能**: AI图像生成（Together AI平台），快速图像生成

**安装命令**:
```bash
# 使用uvx
uvx together-image-gen
```

**Cursor配置**:
```json
{
  "together-image-gen": {
    "command": "uvx",
    "args": ["together-image-gen"],
    "env": {
      "TOGETHER_API_KEY": "aa188707-1019-4641-a6ea-ff43fa1daa9b"
    }
  }
}
```

**Augment配置**:
```json
{
  "together-image-gen": {
    "command": "uvx",
    "args": ["together-image-gen"],
    "env": {
      "TOGETHER_API_KEY": "aa188707-1019-4641-a6ea-ff43fa1daa9b"
    }
  }
}
```

**环境变量**:
- `TOGETHER_API_KEY`: 必需，Together AI平台API密钥

**验证方法**:
```
测试命令: "使用Together AI生成一张风景图"
预期结果: 返回生成的图像
```

### 7. 🧠 sequential-thinking

**功能**: 序列思维和逻辑分析，复杂问题分解

**安装命令**:
```bash
# 使用npx
npx sequential-thinking
```

**Cursor配置**:
```json
{
  "sequential-thinking": {
    "command": "uvx",
    "args": ["sequential-thinking"]
  }
}
```

**Augment配置**:
```json
{
  "sequential-thinking": {
    "command": "npx",
    "args": ["sequential-thinking"]
  }
}
```

**环境变量**: 无需额外环境变量

**验证方法**:
```
测试命令: "请用序列思维分析这个技术问题"
预期结果: 返回结构化的分析过程
```

### 8. 🦐 shrimp-task-manager

**功能**: 智能任务规划和管理，项目任务分解

**安装命令**:
```bash
# 使用npx
npx shrimp-task-manager
```

**Cursor配置**:
```json
{
  "shrimp-task-manager": {
    "command": "npx",
    "args": ["-y", "mcp-shrimp-task-manager"],
    "timeout": 600,
    "env": {
      "DATA_DIR": "C:\\Users\\<USER>\\Desktop\\测试库\\shrimp-data",
      "TEMPLATES_USE": "zh",
      "ENABLE_GUI": "true"
    }
  }
}
```

**Augment配置**:
```json
{
  "shrimp-task-manager": {
    "command": "npx",
    "args": ["shrimp-task-manager"]
  }
}
```

**环境变量**:
- `DATA_DIR`: 数据存储目录（绝对路径）
- `TEMPLATES_USE`: 模板语言（zh为中文）
- `ENABLE_GUI`: 启用Web界面（true/false）

**验证方法**:
```
测试命令: "帮我规划一个项目任务"
预期结果: 返回任务分解和规划
```

### 9. 🌐 fetch

**功能**: 网页内容获取和API调用，数据抓取

**安装命令**:
```bash
# 使用Python模块
python -m mcp_server_fetch
```

**Cursor配置**:
```json
{
  "fetch": {
    "command": "python",
    "args": ["-m", "mcp_server_fetch"],
    "timeout": 300
  }
}
```

**Augment配置**: 同Cursor配置

**环境变量**: 无需额外环境变量

**验证方法**:
```
测试命令: "请读取百度首页内容"
预期结果: 返回网页文本内容
```

## 🔍 配置验证方法

### 通用验证步骤
1. **配置文件语法检查**:
```bash
# 验证JSON格式
cat mcp.json | jq .
```

2. **服务状态检查**:
   - 在IDE中查看MCP服务状态指示灯
   - 绿色圆圈表示服务正常运行
   - 红色圆圈表示服务启动失败

3. **功能测试**:
   - 使用提供的测试命令验证每个服务
   - 检查返回结果是否符合预期

### 常见问题排查
1. **服务启动失败**: 检查依赖环境和网络连接
2. **配置参数错误**: 验证API密钥和路径设置
3. **权限问题**: 确保文件访问权限正确
4. **版本冲突**: 使用兼容的版本组合

## 🎯 IDE配置差异对比

### Cursor vs Augment 配置差异总结

| 配置项 | Cursor IDE | Augment IDE | 说明 |
|--------|------------|-------------|------|
| **配置文件位置** | `globalStorage\anysphere.cursor\mcp.json` | `%APPDATA%\Augment\mcp_config.json` | 不同的配置目录 |
| **Schema验证** | 宽松 | 严格 | Augment对配置格式要求更严格 |
| **命令支持** | uvx, npx, python | 主要支持npx | Cursor支持更多命令类型 |
| **环境变量** | 灵活设置 | 格式要求严格 | Augment需要特定格式 |
| **兼容性** | 优秀 | 有限 | Cursor兼容性更好 |

### 推荐配置策略

#### 双IDE工作流（推荐）
- **Cursor IDE**: 用于Obsidian集成和复杂MCP服务
- **Augment IDE**: 用于代码开发和简单MCP服务

#### 单IDE选择建议
- **选择Cursor**: 如果需要完整的MCP功能支持
- **选择Augment**: 如果主要用于代码开发，MCP为辅助功能

## 📊 完整配置示例

### Cursor完整配置
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "autoApprove": ["interactive_feedback"]
    },
    "mcp-obsidian": {
      "command": "uv",
      "args": [
        "tool", "run", "mcp-obsidian",
        "--vault-path", "C:\\Users\\<USER>\\Desktop\\测试库"
      ],
      "env": {
        "OBSIDIAN_API_KEY": "你的API密钥",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    },
    "context7": {
      "command": "uvx",
      "args": ["context7-mcp"],
      "env": {
        "CONTEXT7_API_KEY": "你的API密钥"
      }
    },
    "playwright": {
      "command": "uvx",
      "args": ["@executeautomation/playwright-mcp-server"],
      "env": {
        "PLAYWRIGHT_API_KEY": "07885b1c-a040-453f-8685-c0d6ee0336e8"
      }
    },
    "replicate-flux": {
      "command": "uvx",
      "args": ["replicate-flux-mcp"],
      "env": {
        "REPLICATE_API_TOKEN": "****************************************"
      }
    },
    "together-image-gen": {
      "command": "uvx",
      "args": ["together-image-gen"],
      "env": {
        "TOGETHER_API_KEY": "aa188707-1019-4641-a6ea-ff43fa1daa9b"
      }
    },
    "sequential-thinking": {
      "command": "uvx",
      "args": ["sequential-thinking"]
    },
    "shrimp-task-manager": {
      "command": "npx",
      "args": ["-y", "mcp-shrimp-task-manager"],
      "timeout": 600,
      "env": {
        "DATA_DIR": "C:\\Users\\<USER>\\Desktop\\测试库\\shrimp-data",
        "TEMPLATES_USE": "zh",
        "ENABLE_GUI": "true"
      }
    },
    "fetch": {
      "command": "python",
      "args": ["-m", "mcp_server_fetch"],
      "timeout": 300
    }
  }
}
```

### Augment兼容配置
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced"]
    },
    "context7": {
      "command": "npx",
      "args": ["context7"]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["sequential-thinking"]
    },
    "playwright": {
      "command": "npx",
      "args": ["@executeautomation/playwright-mcp-server"],
      "env": {
        "PLAYWRIGHT_API_KEY": "07885b1c-a040-453f-8685-c0d6ee0336e8"
      }
    },
    "shrimp-task-manager": {
      "command": "npx",
      "args": ["shrimp-task-manager"]
    },
    "replicate-flux-mcp": {
      "command": "uvx",
      "args": ["replicate-flux-mcp"],
      "env": {
        "REPLICATE_API_TOKEN": "****************************************"
      }
    },
    "together-image-gen": {
      "command": "uvx",
      "args": ["together-image-gen"],
      "env": {
        "TOGETHER_API_KEY": "aa188707-1019-4641-a6ea-ff43fa1daa9b"
      }
    }
  }
}
```

## 🚀 快速部署指南

### 一键配置脚本
```powershell
# PowerShell脚本示例
# 配置Cursor MCP
$configPath = "$env:APPDATA\Cursor\User\globalStorage\anysphere.cursor\mcp.json"
$config = Get-Content "完整MCP配置.json" | ConvertFrom-Json
$config | ConvertTo-Json -Depth 10 | Set-Content $configPath -Encoding UTF8
Write-Host "✅ Cursor MCP配置完成"
```

### 环境检查清单
- [ ] Node.js 16+ 已安装
- [ ] Python 3.8+ 已安装
- [ ] uv工具已安装 (`pip install uv`)
- [ ] 网络连接正常
- [ ] API密钥已获取
- [ ] Obsidian已运行（如需要）

## 🔧 故障排除快速指南

### 常见错误及解决方案
1. **"command not found"**: 安装对应的运行环境
2. **"connection refused"**: 检查网络和API服务状态
3. **"invalid API key"**: 验证API密钥正确性
4. **"timeout"**: 增加timeout设置或检查网络
5. **"permission denied"**: 检查文件和目录权限

### 诊断命令
```bash
# 检查环境
node --version
python --version
uv --version

# 测试网络连接
curl -I https://api.replicate.com
ping 127.0.0.1

# 验证配置文件
cat mcp.json | jq .
```

---

**配置指南版本**: v1.0
**最后更新**: 2025-06-22
**基于**: 实际测试配置文件
**适用IDE**: Cursor, Augment
