# MCP 完整文档

## 一、简介

MCP（Model Control Protocol）是一套用于AI模型交互的增强协议，提供了丰富的交互功能，包括反馈收集、系统信息获取、网络请求和文档查询等能力。本文档详细介绍MCP的使用方法、部署要求以及常见场景的分步指南。

## 二、使用文档

### 1. MCP核心功能

#### 1.1 交互式反馈 (interactive_feedback)

收集用户反馈，支持文本和图片格式。

```python
result = mcp_mcp-feedback-enhanced_interactive_feedback(
    project_directory=".",  # 项目目录路径
    summary="请提供您的反馈",  # 向用户展示的摘要信息
    timeout=600  # 等待用户回复的超时时间（秒）
)
```

#### 1.2 系统信息获取 (get_system_info)

获取当前系统的环境信息。

```python
system_info = mcp_mcp-feedback-enhanced_get_system_info(random_string="info")
```

#### 1.3 Context7文档查询

查询第三方库的最新文档：

```python
# 步骤1：解析库ID
library_id = mcp_context7_resolve-library-id(libraryName="react")

# 步骤2：获取库文档
docs = mcp_context7_get-library-docs(
    context7CompatibleLibraryID=library_id,
    tokens=10000,  # 文档最大令牌数
    topic="hooks"  # 可选，指定特定主题
)
```

#### 1.4 网络请求 (fetch)

从互联网获取信息：

```python
web_content = mcp_fetch_fetch(
    url="https://example.com",
    max_length=5000,  # 返回内容的最大长度
    raw=false,  # 是否返回原始HTML
    start_index=0  # 开始索引，用于分批获取内容
)
```

### 2. 常用参数说明

| 功能 | 参数 | 类型 | 说明 |
|------|------|------|------|
| interactive_feedback | project_directory | string | 项目目录路径，默认为当前目录 |
| interactive_feedback | summary | string | 向用户展示的摘要信息 |
| interactive_feedback | timeout | integer | 等待用户回复的超时时间（秒） |
| get_system_info | random_string | string | 占位参数 |
| resolve-library-id | libraryName | string | 要查询的库名称 |
| get-library-docs | context7CompatibleLibraryID | string | 库ID，从resolve-library-id获取 |
| get-library-docs | tokens | integer | 文档最大令牌数 |
| get-library-docs | topic | string | 可选，指定特定主题 |
| fetch | url | string | 要获取的URL |
| fetch | max_length | integer | 返回内容的最大长度 |
| fetch | raw | boolean | 是否返回原始HTML |
| fetch | start_index | integer | 开始索引，用于分批获取内容 |

## 三、技术文档

### 1. 部署要求

#### 1.1 基础环境需求

- Python 3.8+ 环境
- Cursor IDE（支持MCP协议的版本）

#### 1.2 必要插件与依赖

- MCP 核心库：`mcp-client`、`mcp-feedback-enhanced`
- Context7 插件：用于文档查询功能
- 网络请求库：用于fetch功能

#### 1.3 配置文件

MCP配置文件（mcp.json）示例：

```json
{
  "MCP_DEBUG": "false",
  "FORCE_WEB": "false",
  "MCP_CONFIG": {
    "feedback": {
      "timeout": 600,
      "ui_preference": "auto"
    },
    "context7": {
      "max_tokens": 10000,
      "cache_ttl": 86400
    },
    "fetch": {
      "default_max_length": 5000,
      "timeout": 30
    }
  }
}
```

#### 1.4 环境变量

- `MCP_DEBUG`: 设置为 "true" 启用调试模式
- `FORCE_WEB`: 设置为 "true" 强制使用Web UI
- `MCP_PATH`: 指定MCP库路径

### 2. 接口规范

MCP工具调用遵循以下格式：

```
mcp_[模块名称]_[功能名称](参数1=值1, 参数2=值2, ...)
```

例如：`mcp_mcp-feedback-enhanced_interactive_feedback()`

## 四、分步骤指南

### 1. MCP环境配置

1. 安装Cursor IDE最新版本
2. 确保Python 3.8+已安装
3. 安装MCP核心依赖：
   ```bash
   pip install mcp-client mcp-feedback-enhanced context7-client
   ```
4. 创建MCP配置文件（mcp.json）并放置在项目根目录

### 2. 交互式反馈使用流程

1. 导入MCP模块（Cursor IDE中自动完成）
2. 编写业务逻辑代码
3. 在需要用户反馈的位置插入交互式反馈代码：
   ```python
   user_feedback = mcp_mcp-feedback-enhanced_interactive_feedback(
       project_directory=".",
       summary="请确认当前方案",
       timeout=600
   )
   ```
4. 处理用户反馈结果
5. 根据反馈继续执行或调整计划

### 3. Context7文档查询流程

1. 确定需要查询的库名称
2. 解析库ID：
   ```python
   library_id = mcp_context7_resolve-library-id(libraryName="react")
   ```
3. 获取库文档：
   ```python
   docs = mcp_context7_get-library-docs(
       context7CompatibleLibraryID=library_id,
       tokens=10000,
       topic="hooks"
   )
   ```
4. 解析并使用文档内容

### 4. 网络请求流程

1. 确定需要获取的URL
2. 发送请求：
   ```python
   web_content = mcp_fetch_fetch(
       url="https://example.com",
       max_length=5000
   )
   ```
3. 处理返回的内容
4. 如果内容被截断，使用start_index参数获取更多内容

## 五、常见问题解答

### 1. 交互式反馈不显示
- 检查环境变量是否正确配置
- 确认GUI可用性（Windows环境通常自动可用）
- 尝试设置`FORCE_WEB=true`使用Web界面

### 2. Context7查询失败
- 确认网络连接正常
- 验证库名称是否正确
- 检查是否已安装context7-client

### 3. 超时问题
- 增加timeout参数值
- 检查网络连接状态
- 考虑分批处理大型请求

## 六、最佳实践

1. **错误处理**：始终包装MCP调用在try/except块中
2. **用户体验**：提供清晰的summary描述用户预期操作
3. **超时设置**：根据任务复杂度合理设置timeout
4. **文档缓存**：重复查询同一库时使用本地缓存
5. **分批请求**：处理大量数据时使用分页参数

## 七、版本历史

| 版本 | 日期 | 主要变更 |
|------|------|---------|
| 1.0.0 | 2023-01-15 | 初始版本发布 |
| 1.1.0 | 2023-03-22 | 添加Context7支持 |
| 1.2.0 | 2023-06-10 | 增强交互式反馈，添加图片支持 |
| 1.3.0 | 2023-09-05 | 改进网络请求功能 |

---

文档最后更新：2023-12-18 