# Fetch MCP 快速测试指南

## 配置文件选择

### 1. 基础版本（推荐新手）
文件：`Fetch-MCP-独立配置.json`
```json
{
  "mcpServers": {
    "fetch": {
      "command": "uvx",
      "args": [
        "mcp-server-fetch"
      ]
    }
  }
}
```

### 2. 增强版本（自定义用户代理）
文件：`Fetch-MCP-增强配置.json`
```json
{
  "mcpServers": {
    "fetch": {
      "command": "uvx",
      "args": [
        "mcp-server-fetch",
        "--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36"
      ]
    }
  }
}
```

## 安装步骤

### 1. 复制配置文件
选择上述任一配置文件内容，复制到您的Augment MCP配置中。

### 2. 重启Augment
保存配置后重启Augment IDE以加载新的MCP服务器。

### 3. 验证安装
在Augment中输入以下测试命令：

## 测试命令

### 基础测试
```
请使用fetch工具获取 https://httpbin.org/html 的内容
```

### 获取JSON数据
```
请获取 https://httpbin.org/json 的内容
```

### 获取纯文本
```
请获取 https://httpbin.org/robots.txt 的内容
```

### 限制长度测试
```
请获取 https://example.com 的前500字符内容
```

### 原始HTML测试
```
请获取 https://example.com 的原始HTML内容
```

## 预期结果

如果配置成功，您应该能看到：

1. **网页内容**：以markdown格式显示的网页内容
2. **JSON数据**：格式化的JSON响应
3. **文本内容**：纯文本内容
4. **限制长度**：截断到指定字符数的内容
5. **原始HTML**：未转换的HTML代码

## 故障排除

### 如果出现错误：

1. **检查网络连接**
   ```bash
   ping httpbin.org
   ```

2. **验证uvx安装**
   ```bash
   uvx --version
   ```

3. **手动测试fetch服务器**
   ```bash
   uvx mcp-server-fetch --help
   ```

4. **检查Augment日志**
   查看Augment的MCP服务器连接日志

### 常见问题

1. **"command not found"错误**
   - 确保uvx已正确安装
   - 检查PATH环境变量

2. **"connection refused"错误**
   - 检查网络连接
   - 确认防火墙设置

3. **"timeout"错误**
   - 尝试更简单的URL（如example.com）
   - 检查网络延迟

## 成功标志

当您看到类似以下输出时，说明fetch MCP已成功安装：

```
# Example Domain

This domain is for use in illustrative examples in documents. You may use this
domain in literature without prior coordination or asking for permission.

[More information...](https://www.iana.org/domains/example)
```

## 下一步

安装成功后，您可以：

1. 尝试获取更复杂的网页内容
2. 使用分块读取功能处理长文章
3. 结合其他MCP工具进行内容分析
4. 探索自定义配置选项

---

**快速开始：** 复制基础配置 → 重启Augment → 测试 `https://example.com` → 开始使用！
