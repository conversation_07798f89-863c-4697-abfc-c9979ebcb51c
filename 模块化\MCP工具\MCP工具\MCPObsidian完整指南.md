# MCP Obsidian 完整指南
> 自动生成 • 日期：2025-06-22  
> 本文汇总了 **MCP Obsidian API** 的安装、配置与最佳实践，适用于 Cursor 环境与独立脚本。

## 目录
1. [产品简介](#产品简介)
2. [快速开始](#快速开始)
   1. [前置条件](#前置条件)
   2. [安装与依赖](#安装与依赖)
   3. [密钥申请](#密钥申请)
3. [环境变量与 .env 配置](#环境变量与-env-配置)
4. [常用 API 速查](#常用-api-速查)
5. [基础操作示例](#基础操作示例)
6. [进阶用法](#进阶用法)
7. [故障排查](#故障排查)
8. [最佳实践](#最佳实践)
9. [参考资料](#参考资料)

---

## 产品简介
**MCP Obsidian** 提供对本地/远程 Obsidian Vault 的编程式访问能力，支持：
- 文件检索（全文、标签、逻辑过滤）
- 读取 / 写入 / 追加 / 删除 Markdown
- Vault 结构元数据查询
- 批量内容处理与自动化生成

它以 RESTful API + SDK 形式集成进 MCP 工具链，可在 Cursor IDE、脚本或 CI/CD 流程中调用。

## 快速开始
### 前置条件
1. 安装 **Obsidian** ≥ 1.2.0，并启用 *Obsidian REST API* 插件。
2. 已创建并打开目标 Vault（本地或远程）。
3. 获取 API 访问密钥（下文详述）。

### 安装与依赖
在 Python / Node 项目中：
```bash
# Python
pip install mcp-obsidian-sdk
# Node
npm i @mcp/obsidian-sdk
```
> 在 Cursor 中无需额外安装，Agent 已预置 SDK。

### 密钥申请
1. 打开 Obsidian → 核心插件 → REST API → **Generate API Key**。
2. 复制 `sk-xxxxxxxx` 保存到安全位置。

## 环境变量与 .env 配置
SDK 按以下优先级获取密钥：
1. **进程环境变量** `MCP_OBSIDIAN_API_KEY`
2. 工作目录 `.env` 文件中的同名字段

### 推荐写法
```dotenv
MCP_OBSIDIAN_API_KEY=<your-key>
OBSIDIAN_HOST=https://127.0.0.1:27124/
OBSIDIAN_PORT=27124
```

#### Windows 持久化（PowerShell）
```powershell
# 当前会话
$Env:MCP_OBSIDIAN_API_KEY = "<your-key>"
# 写入用户级环境变量（重启后仍生效）
setx MCP_OBSIDIAN_API_KEY "<your-key>"
```

## 常用 API 速查
| 分类 | 方法 | 描述 |
|------|------|------|
| 文件列表 | `obsidian_list_files_in_vault` | 列出 Vault 根下所有文件/目录 |
| 简单搜索 | `obsidian_simple_search` | 关键词/标签全文匹配 |
| 复杂搜索 | `obsidian_complex_search` | JsonLogic 组合过滤 |
| 读取文件 | `obsidian_get_file_contents` | 返回文件内容 |
| 批量读取 | `obsidian_batch_get_file_contents` | 一次读多文件 |
| 写入文件 | `obsidian_append_content` | 追加/新建 Markdown |
| 更新内容 | `obsidian_patch_content` | 相对 Heading / Block 精准写入 |
| 删除文件 | `obsidian_delete_file` | 移除文件或目录 |

## 基础操作示例
### 1️⃣ 创建并读取笔记
```python
from mcp_obsidian import ObsidianClient
client = ObsidianClient()
client.append_content("测试笔记.md", "Hello, Obsidian!\n")
print(client.get_file_contents("测试笔记.md"))
```

### 2️⃣ 标签搜索
```python
r = client.simple_search(query="#项目管理", context_length=50)
print(r)
```

## 进阶用法
- **批量生成日报/周报**：结合模板循环 `append_content`。
- **自动 Refactor**：通过 `patch_content` 将旧格式笔记批量升级。
- **结合 Dataview**：在 YAML Front-matter 中写入元数据，实现实时查询。

## 故障排查
| 现象 | 可能原因 | 解决方案 |
|------|----------|----------|
| 401 Unauthorized | API Key 缺失或错误 | 确认环境变量 / .env 内容 |
| Connection refused | HOST/PORT 配置不正确 | 检查插件端口、证书、自签名 HTTPS |
| 搜索无结果 | 标签拼写或正则不匹配 | 测试 simple_search 关键词，排除大小写 |

## 最佳实践
1. **密钥管理**：生产环境存 CI Secret，本地用 `.env`。
2. **分层结构**：使用子目录区隔项目，避免路径冲突。
3. **脚本化备份**：每日 `batch_get_file_contents` → Git commit。
4. **TDD**：为自动化脚本编写单元测试，确保笔记结构稳定。

## 参考资料
- Obsidian REST API 官方文档
- MCP 工具箱 GitHub `mcp-tools`
- 本 Vault 其他指南：`docs/Obsidian-MCP配置详细指南.md`

---
*若本文有更新需求，请在 Issue 区提出。* 