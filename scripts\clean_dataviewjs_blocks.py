#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理日记文件中的dataviewjs代码块
移除YAML front matter后到"🗓️习惯打卡"标题之间的所有内容
"""

import os
import re
from pathlib import Path

def clean_dataviewjs_file(file_path):
    """
    清理单个日记文件，移除YAML后到"🗓️习惯打卡"之间的内容

    Args:
        file_path: 文件路径
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content

        # 使用正则表达式匹配YAML front matter
        yaml_match = re.match(r'^(---.*?---)', content, re.DOTALL)
        if not yaml_match:
            print(f"⚠️  未找到YAML front matter: {file_path}")
            return False

        yaml_content = yaml_match.group(1)

        # 查找"🗓️习惯打卡"标题的位置
        habits_match = re.search(r'## 🗓️习惯打卡', content)
        if not habits_match:
            print(f"⚠️  未找到'🗓️习惯打卡'标题: {file_path}")
            return False

        # 提取"🗓️习惯打卡"及其后的所有内容
        habits_content = content[habits_match.start():]

        # 重新组合文件内容：YAML + 🗓️习惯打卡及其后的内容
        new_content = yaml_content + '\n\n' + habits_content

        # 清理多余的空行
        new_content = re.sub(r'\n\s*\n\s*\n', '\n\n', new_content)

        # 只有内容发生变化时才写入文件
        if new_content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"✅ 已清理: {file_path}")

            # 显示清理前后的行数对比
            original_lines = len(original_content.splitlines())
            new_lines = len(new_content.splitlines())
            print(f"   行数变化: {original_lines} → {new_lines} (减少 {original_lines - new_lines} 行)")
            return True
        else:
            print(f"⚠️  无需清理: {file_path}")
            return False

    except Exception as e:
        print(f"❌ 处理文件失败 {file_path}: {e}")
        return False

def main():
    # 定义文件路径
    base_path = Path("obsidian-vault/0_Bullet Journal/Daily Notes")
    
    # 要处理的文件列表（2025年2月19日至3月9日）
    target_files = [
        "2025-02-19 周三 08.md", "2025-02-20 周四 08.md", "2025-02-21 周五 08.md",
        "2025-02-22 周六 08.md", "2025-02-23 周日 08.md", "2025-02-24 周一 09.md",
        "2025-02-25 周二 09.md", "2025-02-26 周三 09.md", "2025-02-27 周四 09.md",
        "2025-02-28 周五 09.md", "2025-03-01 周六 09.md", "2025-03-02 周日 09.md",
        "2025-03-03 周一 10.md", "2025-03-04 周二 10.md", "2025-03-05 周三 10.md",
        "2025-03-06 周四 10.md", "2025-03-07 周五 10.md", "2025-03-08 周六 10.md",
        "2025-03-09 周日 10.md"
    ]
    
    print("开始清理日记文件的dataviewjs代码块...")
    print(f"目标文件数量: {len(target_files)}")
    print("=" * 50)
    
    success_count = 0
    for filename in target_files:
        file_path = base_path / filename
        if file_path.exists():
            if clean_dataviewjs_file(file_path):
                success_count += 1
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    print("=" * 50)
    print(f"清理完成: {success_count}/{len(target_files)} 个文件成功处理")
    
    # 验证清理结果
    print("\n验证清理结果...")
    for filename in target_files[:3]:  # 验证前3个文件
        file_path = base_path / filename
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否还有dataviewjs代码块
            if '```dataviewjs' in content:
                # 检查dataviewjs是否在"🗓️习惯打卡"之后
                habits_pos = content.find('## 🗓️习惯打卡')
                dataviewjs_pos = content.find('```dataviewjs')
                if habits_pos != -1 and dataviewjs_pos > habits_pos:
                    print(f"✅ {filename}: dataviewjs代码块保留在习惯打卡部分")
                else:
                    print(f"⚠️  {filename}: 可能还有未清理的dataviewjs代码块")
            else:
                print(f"✅ {filename}: 已完全清理dataviewjs代码块")

if __name__ == "__main__":
    main()
