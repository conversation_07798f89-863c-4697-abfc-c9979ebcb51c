---
tags:
  - type/dashboard
  - dashboard/energy
created: 2025-08-01T10:58
updated: 2025-08-01T16:16
---

# 🌟 精力记录查询系统 - 优化版

> 📊 **新增功能**：月度统计报表 | 📈 交互式图表 | 🎨 美观界面
> 
> 🔗 **快速访问**：[打开月度统计报表](cursor_projects/Ob/energy_dashboard.html)

## 📊 月度统计报表

```dataviewjs
// 创建月度统计报表入口
const container = this.container;

// 创建报表入口卡片
const reportCard = document.createElement('div');
reportCard.style.cssText = `
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin: 20px 0;
    text-align: center;
    box-shadow: 0 10px 25px rgba(79, 172, 254, 0.3);
    cursor: pointer;
    transition: transform 0.3s ease;
`;

reportCard.innerHTML = `
    <h3 style="margin: 0 0 10px 0; font-size: 1.5rem;">📈 月度统计报表</h3>
    <p style="margin: 0 0 15px 0; opacity: 0.9;">查看精力记录的月度趋势、影响因素分析和交互式图表</p>
    <button style="
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid white;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
    " onmouseover="this.style.background='white'; this.style.color='#4facfe';" 
       onmouseout="this.style.background='rgba(255, 255, 255, 0.2)'; this.style.color='white';">
        🚀 打开报表
    </button>
`;

// 添加点击事件
reportCard.addEventListener('click', function() {
    // 在新标签页中打开报表
    window.open('cursor_projects/Ob/energy_dashboard.html', '_blank');
});

reportCard.addEventListener('mouseenter', function() {
    this.style.transform = 'translateY(-5px)';
});

reportCard.addEventListener('mouseleave', function() {
    this.style.transform = 'translateY(0)';
});

container.appendChild(reportCard);

// 添加功能说明
const featuresDiv = document.createElement('div');
featuresDiv.style.cssText = `
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
`;

const features = [
    { icon: '📊', title: '月度趋势分析', desc: '查看每月精力记录数量变化趋势' },
    { icon: '🏷️', title: '影响因素统计', desc: '分析各种影响因素的出现频次' },
    { icon: '📈', title: '交互式图表', desc: '支持悬停显示详细数据信息' },
    { icon: '📋', title: '详细数据表格', desc: '月度数据的完整统计表格' }
];

features.forEach(feature => {
    const featureCard = document.createElement('div');
    featureCard.style.cssText = `
        background: white;
        padding: 15px;
        border-radius: 10px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        text-align: center;
        border-left: 4px solid #4facfe;
    `;
    
    featureCard.innerHTML = `
        <div style="font-size: 2rem; margin-bottom: 10px;">${feature.icon}</div>
        <h4 style="margin: 0 0 8px 0; color: #1e293b;">${feature.title}</h4>
        <p style="margin: 0; color: #64748b; font-size: 0.9rem;">${feature.desc}</p>
    `;
    
    featuresDiv.appendChild(featureCard);
});

container.appendChild(featuresDiv);
```

---

## 🔍 按类型查询精力记录

```dataviewjs
// 获取用户选择的类型
const container = this.container;
const typeSelector = document.createElement('select');
typeSelector.id = 'energy-type-selector';
typeSelector.style.cssText = `
    margin: 10px 0;
    padding: 8px 12px;
    width: 200px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    background: white;
`;

// 添加选项
const types = ['全部', '补充剂', '调理', '中医', '西医', '习惯', '恢复', '其他'];
types.forEach(type => {
    const option = document.createElement('option');
    option.value = type;
    option.text = type;
    typeSelector.appendChild(option);
});

// 添加查询按钮
const queryButton = document.createElement('button');
queryButton.textContent = '🔍 查询';
queryButton.style.cssText = `
    margin: 0 10px;
    padding: 8px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: transform 0.2s ease;
`;

queryButton.addEventListener('mouseenter', function() {
    this.style.transform = 'translateY(-2px)';
});

queryButton.addEventListener('mouseleave', function() {
    this.style.transform = 'translateY(0)';
});

// 创建结果容器
const resultContainer = document.createElement('div');
resultContainer.id = 'energy-query-results';
resultContainer.style.marginTop = '20px';

// 添加到页面
const headerDiv = document.createElement('div');
headerDiv.style.cssText = `
    background: #f8fafc;
    padding: 15px;
    border-radius: 10px;
    margin: 10px 0;
    border-left: 4px solid #4facfe;
`;
headerDiv.innerHTML = '<strong>🏷️ 选择精力记录类型:</strong>';

container.appendChild(headerDiv);
container.appendChild(typeSelector);
container.appendChild(queryButton);
container.appendChild(document.createElement('hr'));
container.appendChild(resultContainer);

// 查询函数
async function queryEnergyRecords() {
    const selectedType = typeSelector.value;
    const resultDiv = document.getElementById('energy-query-results');
    resultDiv.innerHTML = '<p style="text-align: center; color: #64748b;">🔄 正在查询...</p>';

    // 获取所有日记文件
    const dailyNotes = await dv.pages('"obsidian-vault/0_Bullet Journal/Daily Notes"')
        .sort(p => p.file.name, 'desc')
        .array();

    // 查找所有精力记录
    let allRecords = [];

    for (const note of dailyNotes) {
        const content = await dv.io.load(note.file.path);
        const lines = content.split('\n');

        for (const line of lines) {
            // 检查是否是精力记录
            if (line.includes('【健康】精力') || line.includes('#精力/')) {
                let matchesType = false;

                // 处理新格式记录 (包含精力/类型 或 #精力/类型)
                if (selectedType === '全部' ||
                    (selectedType !== '其他' && (
                        line.includes(`精力/${selectedType}`) ||
                        line.includes(`#精力/${selectedType}`)
                    )) ||
                    (selectedType === '其他' && !types.slice(1, -1).some(t =>
                        line.includes(`精力/${t}`) ||
                        line.includes(`#精力/${t}`)
                    ))
                ) {
                    matchesType = true;
                }

                // 处理旧格式记录 (根据关键词判断类型)
                if (!matchesType && selectedType !== '全部') {
                    const lowerLine = line.toLowerCase();

                    // 根据关键词判断类型
                    const typeKeywords = {
                        '补充剂': ['维生素', '钙片', '补充剂', '营养素'],
                        '中医': ['中医', '调理', '健胰玉液', '中药'],
                        '习惯': ['sy', 'sy频率', '梦遗', '习惯'],
                        '恢复': ['恢复', '放松', '冥想', '光脚', '晒太阳'],
                        '调理': ['调理', '抽筋', '感冒', '拉肚子'],
                        '西医': ['西医', '医院', '检查', '西药']
                    };

                    // 检查是否包含当前选择类型的关键词
                    if (typeKeywords[selectedType]) {
                        matchesType = typeKeywords[selectedType].some(keyword =>
                            lowerLine.includes(keyword.toLowerCase())
                        );
                    }
                }

                // 如果匹配类型，添加到结果中
                if (matchesType || selectedType === '全部') {
                    allRecords.push({
                        date: note.file.name.split(" ")[0],
                        content: line,
                        link: note.file.path
                    });
                }
            }
        }
    }

    // 显示结果
    if (allRecords.length > 0) {
        let html = `
            <div style="background: #f0f9ff; padding: 15px; border-radius: 10px; margin-bottom: 20px; border-left: 4px solid #0ea5e9;">
                <h3 style="margin: 0; color: #0369a1;">📋 找到 ${allRecords.length} 条${selectedType === '全部' ? '' : selectedType}精力记录</h3>
            </div>
        `;
        html += '<div style="space-y: 10px;">';

        allRecords.forEach(record => {
            // 创建日期链接
            const fullFileName = record.link.split('/').pop().replace('.md', '');
            const dateLink = `<span style="font-weight: bold; color: #0077cc; cursor: pointer;" onclick="app.workspace.openLinkText('${record.link}', '', true)">${fullFileName}</span>`;

            // 提取并格式化时间
            let timeStr = "";
            const timeMatch = record.content.match(/\(start::(\d+:\d+)\)/);
            if (timeMatch && timeMatch[1]) {
                timeStr = ` <span style="color: #64748b;">${timeMatch[1]}</span>`;
            }

            // 提取并格式化内容
            let content = record.content;
            content = content.replace(/^- /, '').replace(/\(start::\d+:\d+\) ：/, '');

            // 高亮关键部分
            content = content.replace(/\*\*(.*?)\*\*/g, '<span style="color: #dc2626; font-weight: bold;">$1</span>');
            content = content.replace(/\*(.*?)\*/g, '<span style="color: #0ea5e9;">$1</span>');

            // 格式化标签
            content = content.replace(/#精力\/(\w+)/g, '<span style="background: #fbbf24; color: white; padding: 2px 6px; border-radius: 12px; font-size: 0.8rem;">#精力/$1</span>');

            // 格式化效果评分
            content = content.replace(/效果:(\d)/g, '<span style="background: #10b981; color: white; padding: 2px 6px; border-radius: 12px; font-size: 0.8rem;">效果:$1</span>');

            // 组合最终显示
            html += `
                <div style="background: white; padding: 15px; border-radius: 10px; margin-bottom: 10px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-left: 3px solid #4facfe;">
                    <div style="margin-bottom: 5px;">
                        ${dateLink}${timeStr}
                    </div>
                    <div style="color: #374151; line-height: 1.5;">
                        ${content}
                    </div>
                </div>
            `;
        });

        html += '</div>';
        resultDiv.innerHTML = html;
    } else {
        resultDiv.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #64748b;">
                <div style="font-size: 3rem; margin-bottom: 10px;">🔍</div>
                <p>未找到${selectedType === '全部' ? '' : selectedType}精力记录</p>
            </div>
        `;
    }
}

// 添加点击事件
queryButton.addEventListener('click', queryEnergyRecords);
```

---

## 🔍 精力记录搜索

```dataviewjs
// 创建搜索界面
const container = this.container;

// 创建搜索头部
const searchHeader = document.createElement('div');
searchHeader.style.cssText = `
    background: #f8fafc;
    padding: 15px;
    border-radius: 10px;
    margin: 10px 0;
    border-left: 4px solid #10b981;
`;
searchHeader.innerHTML = '<strong>🔍 关键词搜索:</strong>';

const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.placeholder = '输入关键词搜索精力记录...';
searchInput.style.cssText = `
    width: 300px;
    padding: 10px 15px;
    margin: 10px 0;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
`;

searchInput.addEventListener('focus', function() {
    this.style.borderColor = '#10b981';
});

searchInput.addEventListener('blur', function() {
    this.style.borderColor = '#e2e8f0';
});

const searchButton = document.createElement('button');
searchButton.textContent = '🔍 搜索';
searchButton.style.cssText = `
    margin: 0 10px;
    padding: 10px 20px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    cursor: pointer;
    transition: transform 0.2s ease;
`;

searchButton.addEventListener('mouseenter', function() {
    this.style.transform = 'translateY(-2px)';
});

searchButton.addEventListener('mouseleave', function() {
    this.style.transform = 'translateY(0)';
});

const searchResults = document.createElement('div');
searchResults.id = 'energy-search-results';
searchResults.style.marginTop = '20px';

container.appendChild(searchHeader);
container.appendChild(searchInput);
container.appendChild(searchButton);
container.appendChild(document.createElement('hr'));
container.appendChild(searchResults);

// 搜索函数
async function searchEnergyRecords() {
    const keyword = searchInput.value.trim().toLowerCase();
    if (!keyword) {
        searchResults.innerHTML = `
            <div style="text-align: center; padding: 20px; color: #f59e0b; background: #fef3c7; border-radius: 10px;">
                ⚠️ 请输入搜索关键词
            </div>
        `;
        return;
    }

    searchResults.innerHTML = '<p style="text-align: center; color: #64748b;">🔄 正在搜索...</p>';

    // 获取所有日记文件
    const dailyNotes = await dv.pages('"obsidian-vault/0_Bullet Journal/Daily Notes"')
        .sort(p => p.file.name, 'desc')
        .array();

    // 搜索精力记录
    let matchedRecords = [];

    for (const note of dailyNotes) {
        const content = await dv.io.load(note.file.path);
        const lines = content.split('\n');

        for (const line of lines) {
            // 检查是否是精力记录且包含关键词
            if ((line.includes('【健康】精力') || line.includes('#精力/')) &&
                line.toLowerCase().includes(keyword)) {
                matchedRecords.push({
                    date: note.file.name.split(" ")[0],
                    content: line,
                    link: note.file.path
                });
            }
        }
    }

    // 显示结果
    if (matchedRecords.length > 0) {
        let html = `
            <div style="background: #ecfdf5; padding: 15px; border-radius: 10px; margin-bottom: 20px; border-left: 4px solid #10b981;">
                <h3 style="margin: 0; color: #059669;">🎯 找到 ${matchedRecords.length} 条包含"${keyword}"的精力记录</h3>
            </div>
        `;
        html += '<div>';

        matchedRecords.forEach(record => {
            const fullFileName = record.link.split('/').pop().replace('.md', '');
            const dateLink = `<span style="font-weight: bold; color: #0077cc; cursor: pointer;" onclick="app.workspace.openLinkText('${record.link}', '', true)">${fullFileName}</span>`;

            // 提取并格式化时间
            let timeStr = "";
            const timeMatch = record.content.match(/\(start::(\d+:\d+)\)/);
            if (timeMatch && timeMatch[1]) {
                timeStr = ` <span style="color: #64748b;">${timeMatch[1]}</span>`;
            }

            // 提取并格式化内容
            let content = record.content;
            content = content.replace(/^- /, '').replace(/\(start::\d+:\d+\) ：/, '');

            // 高亮关键词
            content = content.replace(
                new RegExp(keyword, 'gi'),
                match => `<span style="background: #fef08a; color: #a16207; font-weight: bold; padding: 2px 4px; border-radius: 4px;">${match}</span>`
            );

            // 高亮关键部分
            content = content.replace(/\*\*(.*?)\*\*/g, '<span style="color: #dc2626; font-weight: bold;">$1</span>');
            content = content.replace(/\*(.*?)\*/g, '<span style="color: #0ea5e9;">$1</span>');

            // 格式化标签
            content = content.replace(/#精力\/(\w+)/g, '<span style="background: #fbbf24; color: white; padding: 2px 6px; border-radius: 12px; font-size: 0.8rem;">#精力/$1</span>');

            // 格式化效果评分
            content = content.replace(/效果:(\d)/g, '<span style="background: #10b981; color: white; padding: 2px 6px; border-radius: 12px; font-size: 0.8rem;">效果:$1</span>');

            // 组合最终显示
            html += `
                <div style="background: white; padding: 15px; border-radius: 10px; margin-bottom: 10px; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); border-left: 3px solid #10b981;">
                    <div style="margin-bottom: 5px;">
                        ${dateLink}${timeStr}
                    </div>
                    <div style="color: #374151; line-height: 1.5;">
                        ${content}
                    </div>
                </div>
            `;
        });

        html += '</div>';
        searchResults.innerHTML = html;
    } else {
        searchResults.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #64748b;">
                <div style="font-size: 3rem; margin-bottom: 10px;">🔍</div>
                <p>未找到包含"${keyword}"的精力记录</p>
                <p style="font-size: 0.9rem; margin-top: 10px;">💡 尝试使用其他关键词或检查拼写</p>
            </div>
        `;
    }
}

// 添加点击事件
searchButton.addEventListener('click', searchEnergyRecords);
searchInput.addEventListener('keypress', e => {
    if (e.key === 'Enter') {
        searchEnergyRecords();
    }
});
```

---

## 🔍 精力影响因素分析

```dataviewjs
// 获取所有精力记录
const allEnergyRecords = dv.pages('"obsidian-vault/0_Bullet Journal/Daily Notes"')
    .file.lists
    .where(li =>
        li.text.includes("【健康】精力") ||
        li.text.includes("#精力/")
    );

// 创建分析容器
const container = this.container;

// 创建分析头部
const analysisHeader = document.createElement('div');
analysisHeader.style.cssText = `
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 20px;
    border-radius: 15px;
    margin: 20px 0;
    text-align: center;
`;
analysisHeader.innerHTML = `
    <h3 style="margin: 0 0 10px 0; font-size: 1.5rem;">🔍 影响因素智能分析</h3>
    <p style="margin: 0; opacity: 0.9;">基于 ${allEnergyRecords.length} 条记录的数据分析</p>
`;

container.appendChild(analysisHeader);

// 提取关键词和频率
let keywords = {};
const possibleKeywords = [
    "维生素D", "钙片", "SY", "医院", "口腔溃疡", "拉肚子",
    "感冒", "MY", "低烧", "喉咙发炎", "梦遗", "抽筋",
    "中药", "调理", "疲惫", "晒太阳", "恢复"
];

allEnergyRecords.forEach(record => {
    const text = record.text.toLowerCase();
    possibleKeywords.forEach(keyword => {
        if (text.includes(keyword.toLowerCase())) {
            keywords[keyword] = (keywords[keyword] || 0) + 1;
        }
    });
});

// 按月份分组分析
const monthlyRecords = {};
allEnergyRecords.forEach(record => {
    const dateMatch = record.link.path.match(/(\d{4}-\d{2})-\d{2}/);
    if (dateMatch) {
        const monthYear = dateMatch[1];
        monthlyRecords[monthYear] = (monthlyRecords[monthYear] || 0) + 1;
    }
});

// 显示关键词频率
const sortedKeywords = Object.entries(keywords)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 12);

if (sortedKeywords.length > 0) {
    // 创建影响因素卡片网格
    const factorsGrid = document.createElement('div');
    factorsGrid.style.cssText = `
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin: 20px 0;
    `;

    sortedKeywords.forEach(([keyword, count], index) => {
        const percentage = Math.round((count / allEnergyRecords.length) * 100);
        const colors = [
            '#ef4444', '#f97316', '#eab308', '#22c55e',
            '#06b6d4', '#3b82f6', '#8b5cf6', '#ec4899',
            '#f59e0b', '#10b981', '#6366f1', '#84cc16'
        ];

        const factorCard = document.createElement('div');
        factorCard.style.cssText = `
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            text-align: center;
            border-top: 4px solid ${colors[index % colors.length]};
            transition: transform 0.3s ease;
        `;

        factorCard.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });

        factorCard.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });

        factorCard.innerHTML = `
            <div style="font-size: 2rem; margin-bottom: 10px; color: ${colors[index % colors.length]};">${count}</div>
            <div style="font-weight: bold; color: #1e293b; margin-bottom: 5px;">${keyword}</div>
            <div style="color: #64748b; font-size: 0.9rem;">${percentage}% 出现率</div>
            <div style="background: ${colors[index % colors.length]}20; color: ${colors[index % colors.length]}; padding: 4px 8px; border-radius: 12px; font-size: 0.8rem; margin-top: 8px; display: inline-block;">
                ${count} 次记录
            </div>
        `;

        factorsGrid.appendChild(factorCard);
    });

    container.appendChild(factorsGrid);

    // 显示月度趋势简要信息
    if (Object.keys(monthlyRecords).length > 0) {
        const trendDiv = document.createElement('div');
        trendDiv.style.cssText = `
            background: #f8fafc;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            border-left: 4px solid #6366f1;
        `;

        const sortedMonths = Object.keys(monthlyRecords).sort();
        const trendText = sortedMonths.map(month =>
            `<span style="background: white; padding: 5px 10px; border-radius: 8px; margin: 2px; display: inline-block; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                ${month}: <strong style="color: #6366f1;">${monthlyRecords[month]}条</strong>
            </span>`
        ).join(' ');

        trendDiv.innerHTML = `
            <h4 style="margin: 0 0 15px 0; color: #1e293b;">📈 月度记录趋势</h4>
            <div style="line-height: 2;">${trendText}</div>
        `;

        container.appendChild(trendDiv);
    }

    // 添加数据洞察
    const insightsDiv = document.createElement('div');
    insightsDiv.style.cssText = `
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 12px;
        margin: 20px 0;
    `;

    const topFactor = sortedKeywords[0];
    const totalDays = new Set(allEnergyRecords.map(r => r.link.path.match(/(\d{4}-\d{2}-\d{2})/)?.[1]).filter(Boolean)).size;

    insightsDiv.innerHTML = `
        <h4 style="margin: 0 0 15px 0;">💡 数据洞察</h4>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <div style="font-size: 1.5rem; margin-bottom: 5px;">🏆</div>
                <div style="font-size: 0.9rem; opacity: 0.9;">最常见因素</div>
                <div style="font-weight: bold;">${topFactor[0]} (${topFactor[1]}次)</div>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <div style="font-size: 1.5rem; margin-bottom: 5px;">📅</div>
                <div style="font-size: 0.9rem; opacity: 0.9;">记录覆盖天数</div>
                <div style="font-weight: bold;">${totalDays} 天</div>
            </div>
            <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px;">
                <div style="font-size: 1.5rem; margin-bottom: 5px;">📊</div>
                <div style="font-size: 0.9rem; opacity: 0.9;">平均每天记录</div>
                <div style="font-weight: bold;">${(allEnergyRecords.length / totalDays).toFixed(1)} 条</div>
            </div>
        </div>
    `;

    container.appendChild(insightsDiv);

} else {
    const noDataDiv = document.createElement('div');
    noDataDiv.style.cssText = `
        text-align: center;
        padding: 40px;
        color: #64748b;
        background: #f8fafc;
        border-radius: 12px;
        margin: 20px 0;
    `;
    noDataDiv.innerHTML = `
        <div style="font-size: 3rem; margin-bottom: 15px;">📊</div>
        <h3 style="margin: 0 0 10px 0;">暂无足够数据分析</h3>
        <p style="margin: 0;">请继续记录精力相关信息以获得更好的分析结果</p>
    `;
    container.appendChild(noDataDiv);
}
```
