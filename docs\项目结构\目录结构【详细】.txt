 理想目录结构
测试库/
├── README.md                    # 项目主文档
├── LICENSE                      # 许可证文件
├── .gitignore                   # Git忽略规则
├── requirements.txt             # Python依赖
├── package.json                 # Node.js依赖
├── pyproject.toml              # Python项目配置
│
├── docs/                        # 📚 文档目录
│   ├── installation.md         # 安装指南
│   ├── user-guide.md           # 使用指南
│   ├── api-reference.md        # API参考
│   ├── troubleshooting.md      # 故障排除
│   ├── contributing.md         # 贡献指南
│   └── images/                 # 文档图片
│
├── scripts/                     # 🔧 自动化脚本
│   ├── install/                # 安装脚本
│   │   ├── setup.py            # 主安装脚本
│   │   ├── install-deps.py     # 依赖安装
│   │   └── verify-install.py   # 安装验证
│   ├── mcp/                    # MCP管理脚本
│   │   ├── setup-mcp.py        # MCP配置
│   │   ├── test-mcp.py         # MCP测试
│   │   └── diagnose-mcp.py     # MCP诊断
│   ├── content/                # 内容生成脚本
│   │   ├── generate-promo.py   # 推广图生成
│   │   ├── batch-content.py    # 批量内容生成
│   │   └── export-data.py      # 数据导出
│   └── maintenance/            # 维护脚本
│       ├── cleanup.py          # 清理脚本
│       ├── backup.py           # 备份脚本
│       └── update.py           # 更新脚本
│
├── config/                      # ⚙️ 配置文件
│   ├── mcp/                    # MCP配置
│   │   ├── cursor.json         # Cursor MCP配置
│   │   ├── augment.json        # Augment MCP配置
│   │   ├── claude.json         # Claude MCP配置
│   │   └── templates/          # 配置模板
│   ├── obsidian/               # Obsidian配置
│   │   ├── plugins.json        # 插件配置
│   │   └── settings.json       # 设置配置
│   └── templates/              # 配置模板
│       ├── .env.template       # 环境变量模板
│       └── config.template.json
│
├── tools/                       # 🛠️ 开发工具
│   ├── content-generator/      # 内容生成工具
│   │   ├── __init__.py
│   │   ├── promo_generator.py
│   │   ├── batch_generator.py
│   │   └── templates/
│   ├── data-processor/         # 数据处理工具
│   │   ├── __init__.py
│   │   ├── chat_extractor.py
│   │   ├── data_exporter.py
│   │   └── parsers/
│   ├── mcp-tools/              # MCP工具集
│   │   ├── __init__.py
│   │   ├── obsidian_client.py
│   │   ├── config_manager.py
│   │   └── diagnostics.py
│   └── web-tools/              # Web工具
│       ├── __init__.py
│       ├── html_generator.py
│       └── assets/
│
├── obsidian-vault/             # 📝 Obsidian知识库
│   ├── 0_Bullet Journal/       # 日记系统
│   ├── 1_Fleeting obsidian-vault/       # 闪念笔记
│   ├── 2_Literature obsidian-vault/     # 文献笔记
│   ├── 3_Permanent obsidian-vault/      # 永久笔记
│   ├── 4_References/           # 参考资料
│   ├── 5_Structures/           # 结构化知识
│   ├── 6_Project Notes/        # 项目笔记
│   ├── 7_Task Notes/           # 任务笔记
│   ├── Templates/              # 模板文件
│   └── .obsidian/              # Obsidian配置
│
├── output/                      # 📤 输出文件
│   ├── images/                 # 生成的图片
│   │   ├── promotional/        # 推广图
│   │   ├── screenshots/        # 截图
│   │   └── charts/             # 图表
│   ├── exports/                # 导出数据
│   │   ├── markdown/           # Markdown导出
│   │   ├── json/               # JSON数据
│   │   └── csv/                # CSV数据
│   └── reports/                # 报告文件
│
├── tests/                       # 🧪 测试文件
│   ├── unit/                   # 单元测试
│   ├── integration/            # 集成测试
│   ├── fixtures/               # 测试数据
│   └── conftest.py             # 测试配置
│
├── .github/                     # 🔄 GitHub配置
│   ├── workflows/              # GitHub Actions
│   ├── ISSUE_TEMPLATE/         # Issue模板
│   └── PULL_REQUEST_TEMPLATE.md
│
└── temp/                        # 🗑️ 临时文件
    ├── cache/                  # 缓存文件
    ├── logs/                   # 日志文件
    └── backup/                 # 临时备份