# 测试库项目 Augment Agent Guidelines

> 📅 更新时间：2025-08-04 星期一
> 🤖 适用对象：Augment Agent (测试库项目)
> 📝 版本：v6.0 (精简优化版)
> 🎯 项目范围：测试库项目特定配置和工作流程

---

## 📂 项目概述

测试库项目是一个MCP工具测试和集成项目，专注于验证和优化各类MCP服务在实际开发中的应用。本文档定义项目特有的配置、工具组合和工作流程。

## 🛠️ 项目验证工具清单

### 核心MCP工具（已测试）
| 工具名称 | 主要用途 | 配置状态 |
|----------|----------|----------|
| 寸止MCP | 智能拦截和项目记忆 | ✅ 已优化 |
| Sequential Thinking | 复杂任务分析 | ✅ 已优化 |
| Shrimp Task Manager | 任务规划管理 | ✅ 已优化 |
| Context7 | 技术文档查询 | ✅ 可用 |
| Playwright MCP | 浏览器自动化 | ✅ 可用 |
| Fetch MCP | 网页内容获取 | ✅ 可用 |
| Together Image Gen | 图像生成备用 | ✅ 可用 |

### 项目特有配置
- **git仓库要求**：寸止MCP需要在git仓库环境中使用
- **Windows路径**：注意路径格式兼容性问题
- **功能验证**：新工具必须完成测试后才能加入清单

## 📋 项目工作流程

### 1. 复杂任务处理流程
```yaml
触发条件: 预计超过3个主要步骤的任务
执行步骤:
  1. 使用Sequential Thinking深度分析
  2. 创建任务计划文档: ./issues/任务名-YYYYMMDD.md
  3. 使用Shrimp Task Manager跟踪进度
  4. 关键节点通过寸止MCP确认
  5. 完成后创建复盘文档: ./rewind/任务名.md
```

### 2. 日常开发流程
```yaml
适用场景: 代码编写、调试、重构
工具组合: 
  - Codebase Retrieval (定位代码)
  - Sequential Thinking (分析方案)
  - File Operations (实施修改)
  - 寸止MCP (确认反馈)
```

### 3. 技术调研流程
```yaml
适用场景: 新技术评估、工具对比
工具组合:
  - ACE + Web Search (基础信息)
  - Context7 (权威文档)
  - Fetch MCP (详细内容)
  - Sequential Thinking (深度分析)
输出位置: ./notes/调研主题.md
```

## 📁 项目路径约定

```
测试库/
├── issues/          # 任务计划文档
├── rewind/          # 任务复盘文档
├── notes/           # 技术笔记和调研报告
├── cursor_projects/
│   ├── Ob/         # 推广图HTML输出
│   ├── pic/images/ # 图片导出目录
│   └── together/   # 文本转图输出
└── .augment/       # 项目配置文件
```

## 🎨 项目特色功能

### 推广图制作规范
- **触发格式**：`推广图：[内容来源] → [风格要求]`
- **工作目录**：`./cursor_projects/Ob`
- **技术方案**：HTML设计 + Playwright截图
- **输出规格**：1400x2000px, JPG格式, 质量95

### Capture Notes处理
- **搜索模式**：OR搜索 + 时间排序
- **输出格式**：卡片式布局（名称+网址+简介）
- **存储位置**：`./notes/专题名称.md`

## 🔄 项目记忆管理

### 寸止MCP项目记忆
- **存储内容**：项目特定规则、临时决策、阶段性配置
- **更新时机**：任务完成、规则变更、重要决策
- **查询方式**：通过寸止MCP的记忆管理功能

### 与全局记忆协同
- **分工明确**：全局原则不在项目记忆中重复
- **定期提升**：重要经验定期更新到全局规则
- **避免冲突**：项目规则优先级高于全局规则

## 📊 项目质量指标

### MCP工具性能基准
- 寸止MCP响应时间：<500ms
- Sequential Thinking分析时间：15-45秒
- Shrimp任务创建：<2秒
- 文件操作延迟：<200ms

### 项目交付标准
- 所有代码必须通过测试
- 文档必须包含完整示例
- 复杂功能需要使用说明
- 重要决策需要记录依据

## 🚀 项目最佳实践

### MCP工具组合模式
1. **分析型任务**：Sequential Thinking → 寸止MCP确认 → 执行
2. **开发型任务**：Codebase搜索 → 文件编辑 → 测试验证
3. **管理型任务**：Shrimp规划 → 进度跟踪 → 复盘总结

### 项目协作约定
- 重要变更必须留下记录
- 实验性功能需要标注风险
- 工具冲突问题优先解决
- 保持项目文档的时效性

---

*📝 备注：本文档仅包含测试库项目的特定配置。通用原则请参考全局规则文档。*

*🔄 更新记录：v6.0 (2025-08-04) - 精简优化，专注项目特色，避免与全局规则重复*
