# 配置示例与故障排除指南报告

> **编写时间**：2025-07-07  
> **编写目标**：基于项目实际遇到的问题提供完整的配置示例和故障排除指南  
> **编写状态**：✅ 已完成

## 📋 指南概述

本报告基于测试库项目的实际配置经验和故障排除实践，整理了完整的MCP工具配置示例和故障排除指南，包括Windows环境特殊性处理、版本兼容性问题、常见错误解决方案，重点关注实际遇到的问题和验证有效的解决方案。

## 🛠️ 完整配置示例

### 1. 📁 Augment IDE 完整配置

#### 标准配置文件 (推荐)
```json
{
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "MEMORY_FILE_PATH": "C:\\Users\\<USER>\\Desktop\\测试库\\memory.json"
      }
    },
    "mcp-obsidian": {
      "command": "uvx",
      "args": ["mcp-obsidian"],
      "env": {
        "OBSIDIAN_API_KEY": "your_api_key_here",
        "OBSIDIAN_HOST": "https://127.0.0.1:27124/"
      }
    },
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced"]
    },
    "context7": {
      "command": "npx",
      "args": ["context7"]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["sequential-thinking"]
    },
    "playwright": {
      "command": "npx",
      "args": ["@executeautomation/playwright-mcp-server"],
      "env": {
        "PLAYWRIGHT_API_KEY": "your_playwright_key_here"
      }
    },
    "shrimp-task-manager": {
      "command": "npx",
      "args": ["shrimp-task-manager"]
    },
    "replicate-flux-mcp": {
      "command": "uvx",
      "args": ["replicate-flux-mcp"],
      "env": {
        "REPLICATE_API_TOKEN": "your_replicate_token_here"
      }
    },
    "together-image-gen": {
      "command": "uvx",
      "args": ["together-image-gen"],
      "env": {
        "TOGETHER_API_KEY": "your_together_key_here"
      }
    },
    "fetch": {
      "command": "uvx",
      "args": ["mcp-server-fetch"]
    }
  }
}
```

#### 配置文件位置
```yaml
Windows:
  - 主配置: %APPDATA%\Augment\mcp_config.json
  - 备用位置: C:\Users\<USER>\AppData\Roaming\Augment\mcp_config.json

macOS:
  - 主配置: ~/Library/Application Support/Augment/mcp_config.json

Linux:
  - 主配置: ~/.config/Augment/mcp_config.json
```

### 2. 🎯 Cursor IDE 配置示例

#### 基础配置
```json
{
  "mcpServers": {
    "mcp-obsidian": {
      "command": "uvx",
      "args": ["mcp-obsidian"],
      "env": {
        "OBSIDIAN_API_KEY": "your_api_key_here",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    },
    "shrimp-task-manager": {
      "command": "uvx",
      "args": ["shrimp-task-manager"]
    },
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced"]
    },
    "fetch": {
      "command": "uvx",
      "args": ["mcp-server-fetch"]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]
    },
    "playwright": {
      "command": "npx",
      "args": ["@executeautomation/playwright-mcp-server"]
    }
  }
}
```

#### 配置文件位置
```yaml
Windows:
  - 主配置: C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\anysphere.cursor\mcp.json

macOS:
  - 主配置: ~/Library/Application Support/Cursor/User/globalStorage/anysphere.cursor/mcp.json

Linux:
  - 主配置: ~/.config/Cursor/User/globalStorage/anysphere.cursor/mcp.json
```

### 3. 🔧 寸止MCP 特殊配置

#### 配置要求
```yaml
前置条件:
  - 必须在git仓库中使用
  - 需要设置项目路径环境变量

配置示例:
{
  "mcpServers": {
    "cunzhi": {
      "command": "uvx",
      "args": ["cunzhi"],
      "env": {
        "CUNZHI_PROJECT_PATH": "C:\\Users\\<USER>\\Desktop\\测试库"
      }
    }
  }
}

初始化命令:
git init  # 如果不是git仓库
```

## 🖥️ Windows环境特殊处理

### 1. 路径处理规范

#### Windows路径格式
```yaml
正确格式:
  - JSON中使用双反斜杠: "C:\\Users\\<USER>\\Desktop\\测试库"
  - 或使用正斜杠: "C:/Users/<USER>/Desktop/测试库"

错误格式:
  - 单反斜杠: "C:\Users\<USER>\Desktop\测试库"  # 会导致转义错误
  - 混合格式: "C:\Users/Administrator\Desktop\测试库"  # 不一致
```

#### 环境变量设置
```powershell
# PowerShell中设置环境变量
$env:OBSIDIAN_API_KEY = "your_api_key_here"
$env:OBSIDIAN_HOST = "127.0.0.1"
$env:OBSIDIAN_PORT = "27124"
$env:CUNZHI_PROJECT_PATH = "C:\Users\<USER>\Desktop\测试库"

# 永久设置环境变量
[Environment]::SetEnvironmentVariable("OBSIDIAN_API_KEY", "your_api_key_here", "User")
```

### 2. 权限处理

#### 管理员权限要求
```yaml
需要管理员权限的操作:
  - 安装全局npm包
  - 修改系统环境变量
  - 访问某些系统目录

解决方案:
  - 以管理员身份运行PowerShell
  - 使用用户级别的包管理器(uvx)
  - 配置用户级别的环境变量
```

#### 执行策略设置
```powershell
# 检查当前执行策略
Get-ExecutionPolicy

# 设置执行策略(如果需要)
Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser

# 临时绕过执行策略
powershell -ExecutionPolicy Bypass -File "script.ps1"
```

### 3. 编码处理

#### 中文路径支持
```yaml
问题: Windows中文路径可能导致编码问题
解决方案:
  - 使用UTF-8编码保存配置文件
  - 在PowerShell中设置正确的编码
  - 避免在路径中使用特殊字符

PowerShell编码设置:
[Console]::OutputEncoding = [System.Text.Encoding]::UTF8
$OutputEncoding = [System.Text.Encoding]::UTF8
```

## 🔄 版本兼容性问题

### 1. Node.js版本要求

#### 版本兼容性矩阵
```yaml
推荐版本:
  - Node.js: 18.x 或 20.x LTS
  - npm: 9.x 或 10.x
  - Python: 3.8+ (用于uvx工具)

兼容性检查:
node --version    # 应该显示 v18.x.x 或 v20.x.x
npm --version     # 应该显示 9.x.x 或 10.x.x
python --version  # 应该显示 3.8.x 或更高
uv --version      # 应该显示最新版本
```

#### 版本冲突解决
```bash
# 使用nvm管理Node.js版本
nvm install 20.11.0
nvm use 20.11.0

# 更新npm到最新版本
npm install -g npm@latest

# 更新uv工具
pip install --upgrade uv
```

### 2. MCP服务器版本

#### 版本锁定策略
```json
{
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory@0.1.0"],
      "env": {
        "MEMORY_FILE_PATH": "C:\\Users\\<USER>\\Desktop\\测试库\\memory.json"
      }
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking@0.1.0"]
    }
  }
}
```

#### 版本更新检查
```bash
# 检查可用版本
npm view @modelcontextprotocol/server-memory versions --json
npm view @modelcontextprotocol/server-sequential-thinking versions --json

# 检查已安装版本
npm list -g @modelcontextprotocol/server-memory
uvx --version mcp-obsidian
```

### 3. IDE版本兼容性

#### Augment IDE兼容性
```yaml
已测试版本:
  - Augment IDE: 最新版本
  - 兼容的MCP服务器: Memory, Sequential Thinking, Shrimp Task Manager
  - 已知问题: Obsidian MCP schema验证过于严格

解决方案:
  - 使用简化的配置文件
  - 避免使用第三方MCP服务器
  - 等待官方兼容性更新
```

#### Cursor IDE兼容性
```yaml
已测试版本:
  - Cursor IDE: 最新版本
  - 完全兼容: 所有测试的MCP服务器
  - 推荐用途: Obsidian集成和复杂MCP工具

配置建议:
  - 使用完整的MCP配置
  - 支持所有第三方服务器
  - 定期更新到最新版本
```

## 🚨 常见错误解决方案

### 1. JSON格式错误

#### 错误类型与解决方案
```yaml
错误1: "Invalid JSON format in mcp.json"
原因: JSON语法错误，如缺少逗号、引号不匹配
解决方案:
  - 使用JSON验证工具检查格式
  - 检查所有括号和引号是否匹配
  - 确保最后一个对象后没有多余的逗号

错误2: "Unexpected token in JSON"
原因: 包含注释或非标准JSON语法
解决方案:
  - 移除所有注释(// 或 /* */)
  - 确保所有字符串都用双引号包围
  - 检查特殊字符是否正确转义
```

#### JSON验证工具
```bash
# 使用jq验证JSON格式
cat mcp.json | jq .

# 使用Python验证
python -m json.tool mcp.json

# 在线验证工具
# https://jsonlint.com/
```

### 2. 环境变量问题

#### 常见环境变量错误
```yaml
错误1: "Environment variable not found"
原因: 环境变量未设置或名称错误
解决方案:
  - 检查环境变量名称拼写
  - 确保环境变量已正确设置
  - 重启IDE以加载新的环境变量

错误2: "Invalid API key format"
原因: API密钥格式错误或包含特殊字符
解决方案:
  - 检查API密钥是否完整
  - 确保没有多余的空格或换行符
  - 验证API密钥的有效性
```

#### 环境变量调试
```powershell
# 检查环境变量是否设置
echo $env:OBSIDIAN_API_KEY
echo $env:OBSIDIAN_HOST
echo $env:CUNZHI_PROJECT_PATH

# 列出所有环境变量
Get-ChildItem Env: | Where-Object Name -like "*OBSIDIAN*"
Get-ChildItem Env: | Where-Object Name -like "*CUNZHI*"
```

### 3. 网络连接问题

#### 连接测试方法
```bash
# 测试Obsidian API连接
curl -H "Authorization: Bearer your_api_key" http://127.0.0.1:27124/vault

# 测试网络连接
ping 127.0.0.1
telnet 127.0.0.1 27124

# 检查端口占用
netstat -an | findstr :27124
```

#### 防火墙和代理问题
```yaml
防火墙问题:
  - 检查Windows防火墙设置
  - 确保允许Obsidian和相关端口
  - 临时关闭防火墙进行测试

代理问题:
  - 检查系统代理设置
  - 配置npm代理(如果需要)
  - 使用直连方式测试
```

### 4. 依赖安装问题

#### 包管理器问题
```yaml
npm问题:
  - 清理npm缓存: npm cache clean --force
  - 重新安装包: npm install -g package_name
  - 检查npm配置: npm config list

uvx问题:
  - 更新uv工具: pip install --upgrade uv
  - 清理uvx缓存: uvx cache clean
  - 重新安装包: uvx install package_name
```

#### 权限问题
```bash
# Windows权限问题解决
# 1. 以管理员身份运行命令提示符
# 2. 或者使用用户级别安装
npm config set prefix %APPDATA%\npm
npm install -g package_name

# 3. 或者使用yarn作为替代
npm install -g yarn
yarn global add package_name
```

## 🔍 配置验证和测试方法

### 1. 基础连接测试

#### 自动化测试脚本
```python
#!/usr/bin/env python3
"""
MCP配置验证脚本
"""
import requests
import json
import os
from pathlib import Path

def test_obsidian_api():
    """测试Obsidian API连接"""
    api_key = os.getenv('OBSIDIAN_API_KEY')
    host = os.getenv('OBSIDIAN_HOST', '127.0.0.1')
    port = os.getenv('OBSIDIAN_PORT', '27124')
    
    if not api_key:
        print("❌ OBSIDIAN_API_KEY环境变量未设置")
        return False
    
    url = f"http://{host}:{port}/vault"
    headers = {"Authorization": f"Bearer {api_key}"}
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            print("✅ Obsidian API连接成功")
            return True
        else:
            print(f"❌ Obsidian API连接失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Obsidian API连接异常: {e}")
        return False

def test_mcp_config():
    """测试MCP配置文件"""
    config_paths = [
        Path.home() / "AppData/Roaming/Augment/mcp_config.json",
        Path.home() / "AppData/Roaming/Cursor/User/globalStorage/anysphere.cursor/mcp.json"
    ]
    
    for config_path in config_paths:
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"✅ 配置文件格式正确: {config_path}")
                print(f"   配置的MCP服务器数量: {len(config.get('mcpServers', {}))}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ 配置文件JSON格式错误: {config_path}")
                print(f"   错误详情: {e}")
                return False
    
    print("❌ 未找到MCP配置文件")
    return False

def main():
    """主测试函数"""
    print("🔍 MCP配置验证测试")
    print("=" * 50)
    
    # 测试环境变量
    print("\n1. 环境变量检查:")
    env_vars = ['OBSIDIAN_API_KEY', 'OBSIDIAN_HOST', 'OBSIDIAN_PORT', 'CUNZHI_PROJECT_PATH']
    for var in env_vars:
        value = os.getenv(var)
        if value:
            print(f"   ✅ {var}: {value[:10]}..." if len(value) > 10 else f"   ✅ {var}: {value}")
        else:
            print(f"   ❌ {var}: 未设置")
    
    # 测试配置文件
    print("\n2. 配置文件检查:")
    config_ok = test_mcp_config()
    
    # 测试API连接
    print("\n3. API连接测试:")
    api_ok = test_obsidian_api()
    
    # 总结
    print("\n" + "=" * 50)
    if config_ok and api_ok:
        print("🎉 所有测试通过！MCP配置正常")
    else:
        print("❌ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
```

### 2. 功能测试清单

#### 基础功能测试
```yaml
Obsidian MCP测试:
  - [ ] 列出vault中的文件
  - [ ] 读取指定文件内容
  - [ ] 搜索文件内容
  - [ ] 创建新文件
  - [ ] 更新现有文件

Memory MCP测试:
  - [ ] 创建实体
  - [ ] 创建关系
  - [ ] 添加观察
  - [ ] 搜索节点
  - [ ] 读取图谱

Sequential Thinking测试:
  - [ ] 启动思维分析
  - [ ] 调整思维步骤
  - [ ] 生成分析报告
  - [ ] 验证假设

Shrimp Task Manager测试:
  - [ ] 创建任务
  - [ ] 列出任务
  - [ ] 更新任务状态
  - [ ] 删除任务
  - [ ] 验证任务
```

### 3. 性能测试

#### 响应时间测试
```python
import time
import statistics

def measure_response_time(test_func, iterations=5):
    """测量函数响应时间"""
    times = []
    for i in range(iterations):
        start_time = time.time()
        try:
            test_func()
            end_time = time.time()
            times.append(end_time - start_time)
        except Exception as e:
            print(f"测试失败: {e}")
            return None
    
    if times:
        avg_time = statistics.mean(times)
        min_time = min(times)
        max_time = max(times)
        print(f"平均响应时间: {avg_time:.2f}秒")
        print(f"最快响应时间: {min_time:.2f}秒")
        print(f"最慢响应时间: {max_time:.2f}秒")
        return avg_time
    return None

# 使用示例
# avg_time = measure_response_time(test_obsidian_api)
```

## 🔧 故障排除标准流程

### 1. 四步诊断法

#### 第一步：环境检查
```bash
# 1.1 检查基础环境
node --version          # 应该是 18.x 或 20.x
npm --version           # 应该是 9.x 或 10.x
python --version        # 应该是 3.8+
uv --version           # 应该有输出

# 1.2 检查网络连接
ping 127.0.0.1
curl http://127.0.0.1:27124/vault -H "Authorization: Bearer test"

# 1.3 检查权限
whoami                  # 确认当前用户
echo $env:PATH         # 检查PATH环境变量
```

#### 第二步：配置验证
```powershell
# 2.1 检查配置文件存在性
Test-Path "$env:APPDATA\Augment\mcp_config.json"
Test-Path "$env:APPDATA\Cursor\User\globalStorage\anysphere.cursor\mcp.json"

# 2.2 验证JSON格式
Get-Content "$env:APPDATA\Augment\mcp_config.json" | ConvertFrom-Json

# 2.3 检查环境变量
echo $env:OBSIDIAN_API_KEY
echo $env:OBSIDIAN_HOST
echo $env:CUNZHI_PROJECT_PATH
```

#### 第三步：服务测试
```bash
# 3.1 测试Obsidian API
curl -X GET "http://127.0.0.1:27124/vault" \
     -H "Authorization: Bearer your_api_key"

# 3.2 测试MCP服务器启动
uvx mcp-obsidian --help
npx @modelcontextprotocol/server-memory --help

# 3.3 测试寸止MCP
cd /path/to/git/repo
uvx cunzhi --help
```

#### 第四步：集成测试
```yaml
IDE测试步骤:
  1. 重启IDE
  2. 检查MCP服务器连接状态
  3. 测试基础功能调用
  4. 验证错误处理机制
  5. 检查日志输出
```

### 2. 问题分类与优先级

#### 🔴 高优先级问题（立即处理）
```yaml
配置文件损坏:
  症状: JSON解析错误、IDE无法启动
  解决: 使用备份配置文件或重新生成

API连接失败:
  症状: 无法访问Obsidian、超时错误
  解决: 检查Obsidian状态、验证API密钥

依赖缺失:
  症状: 命令未找到、模块导入错误
  解决: 重新安装依赖包、检查环境变量
```

#### 🟡 中优先级问题（计划处理）
```yaml
性能问题:
  症状: 响应缓慢、超时警告
  解决: 调整超时设置、优化配置

兼容性问题:
  症状: 版本冲突、功能异常
  解决: 更新到兼容版本、使用替代方案

配置冲突:
  症状: 部分功能不工作、错误日志
  解决: 检查配置重复、调整优先级
```

#### 🟢 低优先级问题（可延后处理）
```yaml
警告信息:
  症状: 非致命警告、功能正常
  解决: 记录问题、计划优化

文档问题:
  症状: 说明不清、示例过时
  解决: 更新文档、补充说明

非关键功能:
  症状: 可选功能异常、主功能正常
  解决: 禁用功能、等待修复
```

## 📚 实际案例分析

### 案例1：Obsidian MCP配置失败

#### 问题描述
```yaml
用户报告: "Cursor无法连接到Obsidian，显示API错误"
错误信息: "Failed to connect to Obsidian API"
环境: Windows 11, Cursor IDE, Obsidian 1.4.16
```

#### 诊断过程
```bash
# 步骤1：检查Obsidian状态
# 发现：Obsidian未启动Local REST API插件

# 步骤2：检查API密钥
echo $env:OBSIDIAN_API_KEY
# 发现：环境变量未设置

# 步骤3：检查配置文件
cat "$env:APPDATA\Cursor\User\globalStorage\anysphere.cursor\mcp.json"
# 发现：缺少--vault-path参数
```

#### 解决方案
```json
{
  "mcpServers": {
    "mcp-obsidian": {
      "command": "uv",
      "args": [
        "tool", "run", "mcp-obsidian",
        "--vault-path", "C:\\Users\\<USER>\\Desktop\\测试库"
      ],
      "env": {
        "OBSIDIAN_API_KEY": "正确的API密钥"
      }
    }
  }
}
```

#### 验证结果
```bash
# 测试连接
curl -H "Authorization: Bearer API密钥" http://127.0.0.1:27124/vault
# 返回：{"name": "测试库", "files": [...]}

# 功能测试
# Cursor中执行："列出我的Obsidian笔记"
# 结果：成功返回笔记列表
```

### 案例2：寸止MCP git仓库问题

#### 问题描述
```yaml
用户报告: "寸止MCP提示需要git仓库"
错误信息: "This tool requires a git repository"
环境: Windows 11, 非git目录
```

#### 诊断过程
```bash
# 步骤1：检查git状态
git status
# 输出：fatal: not a git repository

# 步骤2：检查寸止MCP要求
# 研究GitHub Issue #30，确认Windows路径识别问题
```

#### 解决方案
```bash
# 初始化git仓库
git init
git add .
git commit -m "Initial commit"

# 验证寸止MCP功能
# 测试记忆管理和对话拦截功能
```

#### 验证结果
```yaml
记忆功能: ✅ 正常工作
对话拦截: ✅ 正常工作
项目路径识别: ✅ 正常工作
```

### 案例3：Memory MCP路径问题

#### 问题描述
```yaml
用户报告: "Memory MCP无法保存数据"
错误信息: "Cannot write to memory file"
环境: Windows 11, 路径包含中文
```

#### 诊断过程
```bash
# 步骤1：检查内存文件路径
echo $env:MEMORY_FILE_PATH
# 输出：C:\Users\<USER>\Desktop\测试库\memory.json

# 步骤2：检查文件权限
Test-Path "C:\Users\<USER>\Desktop\测试库\memory.json"
# 输出：False

# 步骤3：检查目录权限
Test-Path "C:\Users\<USER>\Desktop\测试库"
# 输出：True
```

#### 解决方案
```json
{
  "mcpServers": {
    "memory": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "MEMORY_FILE_PATH": "C:\\Users\\<USER>\\Desktop\\测试库\\memory.json"
      }
    }
  }
}
```

#### 验证结果
```bash
# 检查内存文件创建
Test-Path "C:\Users\<USER>\Desktop\测试库\memory.json"
# 输出：True

# 测试内存功能
# 创建实体、关系、观察等操作均正常
```

## 🛠️ 自动化修复工具

### 1. 一键诊断脚本

#### PowerShell诊断脚本
```powershell
# MCP-诊断工具.ps1
param(
    [string]$ConfigType = "augment"  # augment 或 cursor
)

Write-Host "🔍 MCP配置诊断工具" -ForegroundColor Cyan
Write-Host "=" * 50

# 检查基础环境
Write-Host "`n1. 基础环境检查:" -ForegroundColor Yellow
$nodeVersion = node --version 2>$null
$npmVersion = npm --version 2>$null
$pythonVersion = python --version 2>$null
$uvVersion = uv --version 2>$null

if ($nodeVersion) {
    Write-Host "   ✅ Node.js: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "   ❌ Node.js: 未安装" -ForegroundColor Red
}

if ($npmVersion) {
    Write-Host "   ✅ npm: $npmVersion" -ForegroundColor Green
} else {
    Write-Host "   ❌ npm: 未安装" -ForegroundColor Red
}

if ($pythonVersion) {
    Write-Host "   ✅ Python: $pythonVersion" -ForegroundColor Green
} else {
    Write-Host "   ❌ Python: 未安装" -ForegroundColor Red
}

if ($uvVersion) {
    Write-Host "   ✅ uv: $uvVersion" -ForegroundColor Green
} else {
    Write-Host "   ❌ uv: 未安装" -ForegroundColor Red
}

# 检查配置文件
Write-Host "`n2. 配置文件检查:" -ForegroundColor Yellow
if ($ConfigType -eq "augment") {
    $configPath = "$env:APPDATA\Augment\mcp_config.json"
} else {
    $configPath = "$env:APPDATA\Cursor\User\globalStorage\anysphere.cursor\mcp.json"
}

if (Test-Path $configPath) {
    Write-Host "   ✅ 配置文件存在: $configPath" -ForegroundColor Green
    try {
        $config = Get-Content $configPath | ConvertFrom-Json
        $serverCount = $config.mcpServers.PSObject.Properties.Count
        Write-Host "   ✅ JSON格式正确，包含 $serverCount 个MCP服务器" -ForegroundColor Green
    } catch {
        Write-Host "   ❌ JSON格式错误: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ 配置文件不存在: $configPath" -ForegroundColor Red
}

# 检查环境变量
Write-Host "`n3. 环境变量检查:" -ForegroundColor Yellow
$envVars = @("OBSIDIAN_API_KEY", "OBSIDIAN_HOST", "OBSIDIAN_PORT", "CUNZHI_PROJECT_PATH", "MEMORY_FILE_PATH")
foreach ($var in $envVars) {
    $value = [Environment]::GetEnvironmentVariable($var)
    if ($value) {
        $displayValue = if ($value.Length -gt 20) { $value.Substring(0, 20) + "..." } else { $value }
        Write-Host "   ✅ $var`: $displayValue" -ForegroundColor Green
    } else {
        Write-Host "   ❌ $var`: 未设置" -ForegroundColor Red
    }
}

# 检查Obsidian API连接
Write-Host "`n4. Obsidian API连接测试:" -ForegroundColor Yellow
$apiKey = $env:OBSIDIAN_API_KEY
$host = if ($env:OBSIDIAN_HOST) { $env:OBSIDIAN_HOST } else { "127.0.0.1" }
$port = if ($env:OBSIDIAN_PORT) { $env:OBSIDIAN_PORT } else { "27124" }

if ($apiKey) {
    try {
        $headers = @{ "Authorization" = "Bearer $apiKey" }
        $response = Invoke-RestMethod -Uri "http://$host`:$port/vault" -Headers $headers -TimeoutSec 10
        Write-Host "   ✅ Obsidian API连接成功" -ForegroundColor Green
        Write-Host "   📁 Vault名称: $($response.name)" -ForegroundColor Cyan
    } catch {
        Write-Host "   ❌ Obsidian API连接失败: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "   ❌ OBSIDIAN_API_KEY未设置，跳过连接测试" -ForegroundColor Red
}

# 检查git仓库（寸止MCP需要）
Write-Host "`n5. Git仓库检查:" -ForegroundColor Yellow
try {
    $gitStatus = git status 2>$null
    if ($LASTEXITCODE -eq 0) {
        Write-Host "   ✅ 当前目录是git仓库" -ForegroundColor Green
    } else {
        Write-Host "   ❌ 当前目录不是git仓库（寸止MCP需要）" -ForegroundColor Red
        Write-Host "   💡 解决方案: 运行 'git init' 初始化仓库" -ForegroundColor Yellow
    }
} catch {
    Write-Host "   ❌ Git未安装或不可用" -ForegroundColor Red
}

Write-Host "`n🎯 诊断完成！" -ForegroundColor Cyan
```

### 2. 自动修复脚本

#### 配置修复脚本
```powershell
# MCP-自动修复.ps1
param(
    [string]$Action = "all",  # all, config, env, deps
    [string]$ConfigType = "augment"
)

Write-Host "🔧 MCP自动修复工具" -ForegroundColor Cyan
Write-Host "=" * 50

function Repair-Dependencies {
    Write-Host "`n📦 修复依赖问题..." -ForegroundColor Yellow

    # 检查并安装Node.js
    if (-not (Get-Command node -ErrorAction SilentlyContinue)) {
        Write-Host "   安装Node.js..." -ForegroundColor Yellow
        # 这里可以添加自动安装逻辑
        Write-Host "   ❌ 请手动安装Node.js 18.x或20.x LTS版本" -ForegroundColor Red
        return $false
    }

    # 检查并安装Python
    if (-not (Get-Command python -ErrorAction SilentlyContinue)) {
        Write-Host "   ❌ 请手动安装Python 3.8+版本" -ForegroundColor Red
        return $false
    }

    # 安装或更新uv
    try {
        pip install --upgrade uv
        Write-Host "   ✅ uv工具已更新" -ForegroundColor Green
    } catch {
        Write-Host "   ❌ uv工具安装失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }

    return $true
}

function Repair-Config {
    param([string]$Type)

    Write-Host "`n⚙️ 修复配置文件..." -ForegroundColor Yellow

    if ($Type -eq "augment") {
        $configPath = "$env:APPDATA\Augment\mcp_config.json"
        $configDir = "$env:APPDATA\Augment"
    } else {
        $configPath = "$env:APPDATA\Cursor\User\globalStorage\anysphere.cursor\mcp.json"
        $configDir = "$env:APPDATA\Cursor\User\globalStorage\anysphere.cursor"
    }

    # 创建配置目录
    if (-not (Test-Path $configDir)) {
        New-Item -ItemType Directory -Path $configDir -Force
        Write-Host "   ✅ 创建配置目录: $configDir" -ForegroundColor Green
    }

    # 生成标准配置
    $config = @{
        mcpServers = @{
            "memory" = @{
                command = "npx"
                args = @("-y", "@modelcontextprotocol/server-memory")
                env = @{
                    "MEMORY_FILE_PATH" = "C:\\Users\\<USER>\\Desktop\\测试库\\memory.json"
                }
            }
            "mcp-obsidian" = @{
                command = "uvx"
                args = @("mcp-obsidian")
                env = @{
                    "OBSIDIAN_API_KEY" = "your_api_key_here"
                    "OBSIDIAN_HOST" = "https://127.0.0.1:27124/"
                }
            }
            "mcp-feedback-enhanced" = @{
                command = "uvx"
                args = @("mcp-feedback-enhanced")
            }
            "sequential-thinking" = @{
                command = "npx"
                args = @("sequential-thinking")
            }
            "shrimp-task-manager" = @{
                command = "npx"
                args = @("shrimp-task-manager")
            }
        }
    }

    # 保存配置文件
    try {
        $config | ConvertTo-Json -Depth 10 | Set-Content -Path $configPath -Encoding UTF8
        Write-Host "   ✅ 配置文件已生成: $configPath" -ForegroundColor Green
        Write-Host "   ⚠️ 请手动设置OBSIDIAN_API_KEY" -ForegroundColor Yellow
        return $true
    } catch {
        Write-Host "   ❌ 配置文件生成失败: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Repair-Environment {
    Write-Host "`n🌍 修复环境变量..." -ForegroundColor Yellow

    # 设置基础环境变量
    $envVars = @{
        "OBSIDIAN_HOST" = "127.0.0.1"
        "OBSIDIAN_PORT" = "27124"
        "CUNZHI_PROJECT_PATH" = (Get-Location).Path
        "MEMORY_FILE_PATH" = Join-Path (Get-Location).Path "memory.json"
    }

    foreach ($var in $envVars.GetEnumerator()) {
        try {
            [Environment]::SetEnvironmentVariable($var.Key, $var.Value, "User")
            Write-Host "   ✅ 设置环境变量: $($var.Key) = $($var.Value)" -ForegroundColor Green
        } catch {
            Write-Host "   ❌ 设置环境变量失败: $($var.Key)" -ForegroundColor Red
        }
    }

    Write-Host "   ⚠️ 请手动设置OBSIDIAN_API_KEY环境变量" -ForegroundColor Yellow
    Write-Host "   💡 命令: [Environment]::SetEnvironmentVariable('OBSIDIAN_API_KEY', 'your_key', 'User')" -ForegroundColor Cyan
}

# 执行修复操作
switch ($Action) {
    "deps" { Repair-Dependencies }
    "config" { Repair-Config -Type $ConfigType }
    "env" { Repair-Environment }
    "all" {
        $depsOk = Repair-Dependencies
        $configOk = Repair-Config -Type $ConfigType
        Repair-Environment

        if ($depsOk -and $configOk) {
            Write-Host "`n🎉 修复完成！请重启IDE并测试功能" -ForegroundColor Green
        } else {
            Write-Host "`n⚠️ 部分修复失败，请检查错误信息" -ForegroundColor Yellow
        }
    }
}
```

## 📋 配置检查清单

### 配置前检查清单
```yaml
环境准备:
  - [ ] Node.js 18.x 或 20.x LTS 已安装
  - [ ] Python 3.8+ 已安装
  - [ ] uv 工具已安装 (pip install uv)
  - [ ] Git 已安装（寸止MCP需要）
  - [ ] 网络连接正常

Obsidian准备:
  - [ ] Obsidian 已安装并运行
  - [ ] Local REST API 插件已安装
  - [ ] Local REST API 插件已启用
  - [ ] API Key 已生成并复制
  - [ ] 端口设置为 27124（默认）

权限检查:
  - [ ] 用户具有写入配置目录的权限
  - [ ] 防火墙允许本地连接
  - [ ] 杀毒软件未阻止相关进程
```

### 配置后验证清单
```yaml
配置文件验证:
  - [ ] JSON 格式正确（无语法错误）
  - [ ] 所有必需的环境变量已设置
  - [ ] 路径使用正确的格式（双反斜杠或正斜杠）
  - [ ] API 密钥格式正确且有效

连接测试:
  - [ ] Obsidian API 连接成功
  - [ ] MCP 服务器启动正常
  - [ ] IDE 能够识别 MCP 服务器
  - [ ] 基础功能调用成功

功能验证:
  - [ ] 文件列表功能正常
  - [ ] 文件读取功能正常
  - [ ] 搜索功能正常
  - [ ] 创建文件功能正常
  - [ ] 错误处理正常

性能检查:
  - [ ] 响应时间合理（< 5秒）
  - [ ] 内存使用正常
  - [ ] CPU 使用率正常
  - [ ] 无异常日志输出
```

---

**配置指南总结**：本报告提供了完整的MCP工具配置示例和故障排除指南，基于项目实际经验，涵盖Windows环境特殊处理、版本兼容性问题和常见错误解决方案。通过详细的配置示例、四步诊断法、实际案例分析、自动化修复工具和完整的检查清单，确保用户能够成功配置和使用MCP工具生态系统。
