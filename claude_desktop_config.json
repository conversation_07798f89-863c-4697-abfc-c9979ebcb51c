{"mcpServers": {"Playwright": {"command": "npx", "args": ["@playwright/mcp@latest"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced@latest"]}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"DATA_DIR": "C:\\Users\\<USER>\\Desktop\\测试库\\shrimp-data", "TEMPLATES_USE": "zh", "ENABLE_GUI": "true"}}, "寸止": {"command": "寸止", "args": []}}}