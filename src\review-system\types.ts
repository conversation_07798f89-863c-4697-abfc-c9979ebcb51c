/**
 * 智能复盘系统 - 核心类型定义
 * 
 * 定义复盘系统中使用的所有接口、枚举和类型
 */

// ==================== 基础枚举 ====================

/**
 * 复盘类型枚举
 */
export enum ReviewType {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly'
}

/**
 * 提醒频率枚举
 */
export enum ReviewFrequency {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  CUSTOM = 'custom'
}

// ==================== 基础接口 ====================

/**
 * 日期范围接口
 */
export interface DateRange {
  start: Date;
  end: Date;
}

/**
 * 时间跨度接口
 */
export interface Timespan {
  start: Date;
  end: Date;
  duration: number; // 以天为单位
}

/**
 * 周期接口
 */
export interface Period {
  type: ReviewType;
  start: Date;
  end: Date;
  label: string; // 如 "2025年第1周", "2025年1月"
}

// ==================== 模板相关接口 ====================

/**
 * 自定义字段接口
 */
export interface CustomField {
  id: string;
  name: string;
  type: 'text' | 'number' | 'boolean' | 'select' | 'multiselect';
  required: boolean;
  defaultValue?: any;
  options?: string[]; // 用于select和multiselect类型
  description?: string;
}

/**
 * 提醒配置接口
 */
export interface ReminderConfig {
  enabled: boolean;
  frequency: ReviewFrequency;
  time: string; // HH:mm格式
  daysOfWeek?: number[]; // 0-6，周日到周六
  customDays?: number[]; // 自定义间隔天数
}

/**
 * 模板配置接口
 */
export interface TemplateConfig {
  dimensions: string[];        // 复盘维度，如 ["成就", "挑战", "学习", "反思"]
  customFields: CustomField[]; // 自定义字段
  autoFillData: boolean;      // 是否自动填充数据
  reminderSettings: ReminderConfig; // 提醒设置
  templatePath?: string;      // 自定义模板路径
  outputPath?: string;        // 输出路径模板
}

/**
 * 模板历史记录接口
 */
export interface TemplateHistory {
  id: string;
  type: ReviewType;
  date: Date;
  templateVersion: string;
  configSnapshot: TemplateConfig;
  filePath: string;
  createdAt: Date;
}

// ==================== 复盘数据接口 ====================

/**
 * 睡眠数据接口
 */
export interface SleepData {
  bedtime: string;
  fallAsleep: string;
  wakeup: string;
  outOfBed: string;
  quality: number; // 1-5评分
  wakeups?: number;
  totalWakeupTime?: number; // 分钟
}

/**
 * 习惯打卡接口
 */
export interface HabitCheck {
  habitName: string;
  completed: boolean;
  value?: number; // 用于量化习惯，如跑步距离
  unit?: string;  // 单位，如 "km", "min"
  notes?: string;
}

/**
 * 日常数据接口
 */
export interface DailyData {
  date: Date;
  mood: number; // 1-10评分
  highlights: string[];
  challenges: string[];
  learnings: string[];
  gratitude: string[];
  sleepData?: SleepData;
  habitChecks: HabitCheck[];
  projectActivities?: string[]; // 参与的项目
  customData?: Record<string, any>; // 自定义数据
}

/**
 * 项目数据接口
 */
export interface ProjectData {
  projectName: string;
  progress: number; // 0-100
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  activities: string[];
  timeSpent?: number; // 分钟
  milestones?: string[];
}

/**
 * 任务数据接口
 */
export interface TaskData {
  taskId: string;
  title: string;
  completed: boolean;
  priority: 'high' | 'medium' | 'low';
  project?: string;
  timeSpent?: number; // 分钟
  completedAt?: Date;
}

// ==================== 分析相关接口 ====================

/**
 * 成长分析接口
 */
export interface GrowthAnalysis {
  trends: Trend[];
  milestones: Milestone[];
  improvements: Improvement[];
  regressions: Regression[];
  overallScore: number; // 0-100
  period: Period;
}

/**
 * 趋势接口
 */
export interface Trend {
  metric: string;
  direction: 'up' | 'down' | 'stable';
  confidence: number; // 0-1
  changeRate: number; // 变化率
  description: string;
}

/**
 * 里程碑接口
 */
export interface Milestone {
  id: string;
  title: string;
  description: string;
  date: Date;
  category: string;
  significance: 'high' | 'medium' | 'low';
}

/**
 * 改进点接口
 */
export interface Improvement {
  area: string;
  description: string;
  impact: number; // 1-10
  effort: number; // 1-10
  priority: number; // 计算得出的优先级
}

/**
 * 退步点接口
 */
export interface Regression {
  area: string;
  description: string;
  severity: number; // 1-10
  causes: string[];
  recommendations: string[];
}

// ==================== 系统配置接口 ====================

/**
 * 显示设置接口
 */
export interface DisplaySettings {
  theme: 'light' | 'dark' | 'auto';
  language: string;
  dateFormat: string;
  timeFormat: '12h' | '24h';
  showAdvancedFeatures: boolean;
}

/**
 * 隐私设置接口
 */
export interface PrivacySettings {
  enableDataCollection: boolean;
  shareAnonymousUsage: boolean;
  encryptSensitiveData: boolean;
  dataRetentionDays: number;
}

/**
 * 用户偏好接口
 */
export interface UserPreferences {
  reviewFrequency: ReviewFrequency;
  reminderSettings: ReminderConfig;
  displaySettings: DisplaySettings;
  privacySettings: PrivacySettings;
  favoriteTemplates: string[];
  customDimensions: string[];
}

/**
 * 系统配置接口
 */
export interface SystemConfiguration {
  userPreferences: UserPreferences;
  templateConfigs: Map<ReviewType, TemplateConfig>;
  version: string;
  lastUpdated: Date;
  configPath: string;
}

// ==================== 错误处理接口 ====================

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK_ERROR = 'network_error',
  DATA_CORRUPTION = 'data_corruption',
  PERMISSION_DENIED = 'permission_denied',
  RESOURCE_NOT_FOUND = 'resource_not_found',
  VALIDATION_ERROR = 'validation_error',
  SYSTEM_ERROR = 'system_error'
}

/**
 * 错误上下文接口
 */
export interface ErrorContext {
  operation: string;
  component: string;
  userId?: string;
  additionalInfo?: Record<string, any>;
}

/**
 * 系统错误接口
 */
export interface SystemError {
  type: ErrorType;
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  context: ErrorContext;
  timestamp: Date;
  recoverable: boolean;
  stack?: string;
}

// ==================== 工具函数类型 ====================

/**
 * 模板生成选项
 */
export interface TemplateGenerationOptions {
  includeDataview: boolean;
  includeCustomFields: boolean;
  autoFillData: boolean;
  templateStyle: 'minimal' | 'standard' | 'detailed';
}

/**
 * 搜索查询接口
 */
export interface SearchQuery {
  text?: string;
  tags?: string[];
  dateRange?: DateRange;
  reviewType?: ReviewType;
  limit?: number;
}

/**
 * 搜索结果接口
 */
export interface SearchResult {
  filePath: string;
  title: string;
  excerpt: string;
  relevanceScore: number;
  lastModified: Date;
  reviewType?: ReviewType;
}
