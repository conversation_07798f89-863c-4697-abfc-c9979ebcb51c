# Claude Code 启动脚本 - 修复版
# 解决 API Key 配置问题

Write-Host "======================================" -ForegroundColor Cyan
Write-Host "        启动 Claude Code (修复版)" -ForegroundColor Cyan  
Write-Host "======================================" -ForegroundColor Cyan
Write-Host ""

# 创建配置文件
$configContent = @"
{
  "apiKey": "sk-hzzntp11lud8ep8cT3UC7m2VNLsLpQWZvVAHQ4qS3GuKYxuU",
  "baseUrl": "https://code.wenwen-ai.com/v1"
}
"@

$configPath = "$env:USERPROFILE\.claude.json"
Write-Host "正在创建配置文件: $configPath" -ForegroundColor Green

try {
    $configContent | Out-File -FilePath $configPath -Encoding UTF8 -Force
    Write-Host "配置文件创建成功!" -ForegroundColor Green
} catch {
    Write-Host "配置文件创建失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "配置信息:" -ForegroundColor Green
Write-Host "  API 地址: https://code.wenwen-ai.com/v1" -ForegroundColor Yellow
Write-Host "  配置文件: $configPath" -ForegroundColor Yellow
Write-Host ""

# 设置环境变量作为备用
$env:CLAUDE_CODE_MAX_OUTPUT_TOKENS = "64000"
$env:ANTHROPIC_BASE_URL = "https://code.wenwen-ai.com/v1"
$env:ANTHROPIC_AUTH_TOKEN = "sk-hzzntp11lud8ep8cT3UC7m2VNLsLpQWZvVAHQ4qS3GuKYxuU"

Write-Host "正在启动 Claude Code..." -ForegroundColor Green
Write-Host "提示: 如果仍显示需要登录，请在 Claude Code 中运行 /login" -ForegroundColor Cyan
Write-Host ""

try {
    # 检查 Git Bash 是否存在
    if (Test-Path "C:\Program Files\Git\usr\bin\bash.exe") {
        & "C:\Program Files\Git\usr\bin\bash.exe" -c "export SHELL='/usr/bin/bash'; export CLAUDE_CODE_MAX_OUTPUT_TOKENS='$env:CLAUDE_CODE_MAX_OUTPUT_TOKENS'; export ANTHROPIC_BASE_URL='$env:ANTHROPIC_BASE_URL'; export ANTHROPIC_AUTH_TOKEN='$env:ANTHROPIC_AUTH_TOKEN'; claude"
    } else {
        Write-Host "错误: 未找到 Git Bash，尝试直接启动..." -ForegroundColor Yellow
        claude
    }
} catch {
    Write-Host ""
    Write-Host "启动过程中出现错误:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host ""
    Write-Host "请尝试在 Claude Code 中手动运行:" -ForegroundColor Yellow
    Write-Host "/login" -ForegroundColor White
    Write-Host "然后输入您的 API 密钥和 Base URL" -ForegroundColor White
}

Write-Host ""
Write-Host "======================================" -ForegroundColor Cyan
Write-Host "按任意键关闭此窗口..." -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
