# FLUX.1 Kontext MCP 安装使用指南

## 概述

FLUX.1 Kontext MCP 是一个强大的图像生成和编辑 MCP 服务器，可以直接集成到 Augment 等 AI 编程助手中。

## 系统要求

✅ **您的系统状态**：
- Windows 11 (win32) ✅
- Node.js v22.15.0 ✅ (要求 18+)
- npm v10.9.2 ✅
- Python 3.11.12 ⚠️ (建议升级到 3.12+)

## 安装步骤

### 1. 获取 Flux API Key

1. 访问 [Black Forest Labs](https://bfl.ai/)
2. 注册账户并获取 API Key
3. 记录您的 API Key

### 2. 配置 MCP 服务器

我已经为您更新了 `claude_desktop_config.json` 配置文件，添加了 FLUX.1 Kontext MCP 服务器。

**重要**：请将配置中的 `your_flux_api_key_here` 替换为您的实际 API Key。

### 3. 手动安装（可选）

如果您想手动安装：

```bash
# 克隆仓库
git clone https://github.com/jmanhype/mcp-flux-studio.git
cd mcp-flux-studio

# 安装依赖
npm install

# 构建项目
npm run build
```

### 4. 环境变量配置

创建 `.env` 文件：

```env
BFL_API_KEY=your_actual_flux_api_key
FLUX_PATH=/path/to/flux/installation
```

## 可用工具

### 1. generate - 图像生成
```json
{
  "prompt": "A photorealistic cat",
  "model": "flux.1.1-pro",
  "aspect_ratio": "1:1",
  "output": "generated.jpg"
}
```

### 2. img2img - 图像转换
```json
{
  "image": "input.jpg",
  "prompt": "Convert to oil painting",
  "model": "flux.1.1-pro",
  "strength": 0.85,
  "output": "output.jpg"
}
```

### 3. inpaint - 图像修复
```json
{
  "image": "input.jpg",
  "prompt": "Add flowers",
  "mask_shape": "circle",
  "position": "center",
  "output": "inpainted.jpg"
}
```

### 4. control - 结构控制
```json
{
  "type": "canny",
  "image": "control.jpg",
  "prompt": "A realistic photo",
  "output": "controlled.jpg"
}
```

## 支持的模型

- **flux.1.1-pro** - 最新专业版本
- **flux.1-pro** - 专业版本
- **flux.1-dev** - 开发版本
- **flux.1.1-ultra** - 超高质量版本

## 使用示例

在 Augment 中，您可以这样使用：

```
请帮我生成一张猫咪的照片，要求：
- 风格：写实摄影
- 尺寸：正方形
- 模型：使用最新的 flux.1.1-pro
```

## 故障排除

### 常见问题

1. **API Key 错误**
   - 确保 API Key 正确
   - 检查账户余额

2. **连接超时**
   - 检查网络连接
   - 增加 timeout 设置

3. **模型不可用**
   - 检查模型名称拼写
   - 确认模型可用性

### 日志查看

检查 MCP 服务器日志：
```bash
# 查看 Claude Desktop 日志
# Windows: %APPDATA%\Claude\logs
```

## 高级配置

### 自定义输出路径

```json
{
  "env": {
    "BFL_API_KEY": "your_api_key",
    "OUTPUT_DIR": "./generated_images",
    "MAX_RESOLUTION": "2048x2048"
  }
}
```

### 性能优化

```json
{
  "timeout": 1200,
  "env": {
    "CONCURRENT_REQUESTS": "3",
    "CACHE_ENABLED": "true"
  }
}
```

## 下一步

1. 获取 Flux API Key
2. 更新配置文件中的 API Key
3. 重启 Claude Desktop
4. 开始使用图像生成功能

## 相关资源

- [FLUX.1 Kontext 官方文档](https://bfl.ai/announcements/flux-1-kontext)
- [MCP Flux Studio GitHub](https://github.com/jmanhype/mcp-flux-studio)
- [Black Forest Labs API 文档](https://docs.bfl.ai)
