#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理日记文件中的指定标题和代码块
"""

import os
import re
from pathlib import Path

def clean_diary_file(file_path, sections_to_remove):
    """
    清理单个日记文件，移除指定的标题和代码块
    
    Args:
        file_path: 文件路径
        sections_to_remove: 要移除的标题列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 为每个要移除的标题创建正则表达式
        for section in sections_to_remove:
            # 匹配标题及其后续内容直到下一个同级或更高级标题
            pattern = rf'^## {re.escape(section)}.*?(?=^##|\Z)'
            content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL)
            
            # 也匹配三级标题的情况
            pattern = rf'^### {re.escape(section)}.*?(?=^###|^##|\Z)'
            content = re.sub(pattern, '', content, flags=re.MULTILINE | re.DOTALL)
        
        # 移除多余的空行和分隔符
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = re.sub(r'---\s*\n\s*---', '---', content)
        
        # 只有内容发生变化时才写入文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 已清理: {file_path}")
            return True
        else:
            print(f"⚠️  未找到匹配内容: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ 处理文件失败 {file_path}: {e}")
        return False

def main():
    # 定义文件路径
    base_path = Path("obsidian-vault/0_Bullet Journal/Daily Notes")
    
    # 第一批文件：2025年2月19日至3月9日
    batch1_sections = [
        "🛠 临时任务创建区",
        "📋待办事项", 
        "7️⃣ 天内到期",
        "🔝高优先级任务",
        "📦 所有任务"
    ]
    
    # 第二批文件：2025年3月10日至3月31日  
    batch2_sections = [
        "🛠 临时任务创建区",
        "📋待办事项",
        "7️⃣ 天内到期", 
        "🔝高优先级任务",
        "今日输入",
        "今日输出"
    ]
    
    # 第一批文件列表
    batch1_files = [
        "2025-02-26 周三 09.md", "2025-02-27 周四 09.md", "2025-02-28 周五 09.md",
        "2025-03-01 周六 09.md", "2025-03-02 周日 09.md", "2025-03-03 周一 10.md",
        "2025-03-04 周二 10.md", "2025-03-05 周三 10.md", "2025-03-06 周四 10.md",
        "2025-03-07 周五 10.md", "2025-03-08 周六 10.md", "2025-03-09 周日 10.md"
    ]
    
    # 第二批文件列表
    batch2_files = [
        "2025-03-10 周一 11.md", "2025-03-11 周二 11.md", "2025-03-12 周三 11.md",
        "2025-03-13 周四 11.md", "2025-03-14 周五 11.md", "2025-03-15 周六 11.md",
        "2025-03-16 周日 11.md", "2025-03-17 周一 12.md", "2025-03-18 周二 12.md",
        "2025-03-19 周三 12.md", "2025-03-20 周四 12.md", "2025-03-21 周五 12.md",
        "2025-03-22 周六 12.md", "2025-03-23 周日 12.md", "2025-03-24 周一 13.md",
        "2025-03-25 周二 13.md", "2025-03-26 周三 13.md", "2025-03-27 周四 13.md",
        "2025-03-28 周五 13.md", "2025-03-29 周六 13.md", "2025-03-30 周日 13.md",
        "2025-03-31 周一 14.md"
    ]
    
    print("开始清理第一批文件...")
    batch1_success = 0
    for filename in batch1_files:
        file_path = base_path / filename
        if file_path.exists():
            if clean_diary_file(file_path, batch1_sections):
                batch1_success += 1
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    print(f"\n第一批文件清理完成: {batch1_success}/{len(batch1_files)} 个文件成功处理")
    
    print("\n开始清理第二批文件...")
    batch2_success = 0
    for filename in batch2_files:
        file_path = base_path / filename
        if file_path.exists():
            if clean_diary_file(file_path, batch2_sections):
                batch2_success += 1
        else:
            print(f"⚠️  文件不存在: {file_path}")
    
    print(f"\n第二批文件清理完成: {batch2_success}/{len(batch2_files)} 个文件成功处理")
    print(f"\n总计处理: {batch1_success + batch2_success}/{len(batch1_files) + len(batch2_files)} 个文件")

if __name__ == "__main__":
    main()
