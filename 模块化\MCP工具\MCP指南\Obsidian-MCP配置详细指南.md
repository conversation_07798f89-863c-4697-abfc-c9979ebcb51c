# Obsidian MCP配置详细指南

## 🎯 配置目标
通过MCP让Cursor AI助手直接操作Obsidian知识库，实现：
- 读取笔记内容
- 搜索笔记
- 创建和修改笔记
- 管理文件和目录

## 📋 第一步：安装Obsidian Local REST API插件

### 1.1 启用社区插件
1. 打开Obsidian
2. 点击左下角的设置图标（⚙️）
3. 在左侧菜单中找到"社区插件"
4. 如果显示"安全模式"，点击"关闭安全模式"

### 1.2 安装插件
1. 点击"浏览"按钮
2. 在搜索框中输入"Local REST API"
3. 找到"Local REST API"插件（作者：coddingtonbear）
4. 点击"安装"
5. 安装完成后点击"启用"

### 1.3 配置插件
1. 在"已安装插件"列表中找到"Local REST API"
2. 点击插件右侧的设置图标
3. 在设置页面中：
   - **API Key**: 复制这个密钥（重要！）
   - **Host**: 默认为 `127.0.0.1`
   - **Port**: 默认为 `27124`
   - **Enable HTTPS**: 通常保持关闭

## 📋 第二步：测试插件功能

### 2.1 访问API文档
1. 在插件设置页面，找到"API Documentation"链接
2. 点击链接打开浏览器
3. 页面应该显示Swagger API文档

### 2.2 授权测试
1. 在API文档页面，点击右上角的"Authorize"按钮
2. 输入刚才复制的API Key
3. 点击"Authorize"
4. 如果成功，应该显示授权成功信息

### 2.3 简单测试
1. 在API文档中找到"GET /vault/"端点
2. 点击"Try it out"
3. 点击"Execute"
4. 如果返回200状态码和文件列表，说明插件工作正常

## 📋 第三步：配置Cursor MCP

### 3.1 当前MCP配置位置
```
C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\anysphere.cursor\mcp.json
```

### 3.2 配置模板
将以下配置添加到现有的mcp.json文件中：

```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "autoApprove": ["interactive_feedback"]
    },
    "mcp-obsidian": {
      "command": "uvx",
      "args": ["mcp-obsidian"],
      "env": {
        "OBSIDIAN_API_KEY": "你的API密钥",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```

### 3.3 替换API密钥
将配置中的"你的API密钥"替换为从Obsidian插件设置中复制的实际API Key。

## 📋 第四步：重启和测试

### 4.1 重启Cursor
1. 保存配置文件
2. 完全关闭Cursor
3. 重新启动Cursor

### 4.2 测试MCP功能
在Cursor中尝试以下提示：
- "请列出我的Obsidian知识库中的所有文件"
- "搜索包含'任务'关键词的笔记"
- "读取某个具体笔记的内容"

## ⚠️ 常见问题

### Q1: 插件安装后找不到设置
**解决方案**: 确保插件已启用，在"已安装插件"列表中查找

### Q2: API Key无法复制
**解决方案**: 手动选择API Key文本，右键复制

### Q3: MCP连接失败
**解决方案**: 
1. 检查Obsidian是否运行
2. 检查插件是否启用
3. 检查API Key是否正确
4. 检查端口是否被占用

### Q4: 权限错误
**解决方案**: 确保Obsidian有足够的文件访问权限

## 🎯 成功标志

配置成功后，你应该能够：
- ✅ 在Cursor中询问Obsidian相关问题
- ✅ AI助手能够读取你的笔记内容
- ✅ AI助手能够搜索和创建笔记
- ✅ 在Cursor日志中看到MCP连接成功信息

## 📞 需要帮助？

如果遇到问题，请提供：
1. Obsidian插件设置截图
2. API Key（前几位字符）
3. 错误信息
4. Cursor版本信息

我将帮助你解决配置问题！
