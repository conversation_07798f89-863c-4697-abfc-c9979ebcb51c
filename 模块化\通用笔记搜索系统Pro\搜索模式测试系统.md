---
tags:
  - type/dashboard
  - test/search-modes
created: 2025-07-11T13:00
updated: 2025-07-11T13:00
---

# 🧪 搜索模式测试系统

```dataviewjs
// 搜索模式测试系统 - 验证不同搜索模式的区别
const container = this.container;

// 创建主容器
const mainDiv = document.createElement('div');
mainDiv.style.cssText = `
    font-family: var(--font-interface);
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
`;

// 测试区域
const testDiv = document.createElement('div');
testDiv.style.cssText = `
    background: var(--background-secondary);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--background-modifier-border);
`;

testDiv.innerHTML = `
    <h3 style="margin: 0 0 15px 0; color: var(--text-normal);">🧪 搜索模式测试</h3>
    <div style="margin-bottom: 15px;">
        <input type="text" id="test-keywords" placeholder="输入多个关键词测试（用空格分隔）..." 
               style="width: 100%; padding: 8px; border: 1px solid var(--background-modifier-border); border-radius: 4px; background: var(--background-primary); color: var(--text-normal);">
    </div>
    <div style="display: flex; gap: 10px; margin-bottom: 15px;">
        <button id="test-or" style="padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">测试 OR 模式</button>
        <button id="test-and" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">测试 AND 模式</button>
        <button id="test-exact" style="padding: 8px 16px; background: #6f42c1; color: white; border: none; border-radius: 4px; cursor: pointer;">测试精确匹配</button>
    </div>
    <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 15px;">
        💡 建议测试关键词：<br>
        • "MCP 配置" - 测试两个词的组合<br>
        • "任务 管理 系统" - 测试三个词的组合<br>
        • "Obsidian专用搜索系统" - 测试精确匹配
    </div>
`;

// 结果区域
const resultDiv = document.createElement('div');
resultDiv.style.cssText = `
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    padding: 20px;
    background: var(--background-primary);
    min-height: 300px;
`;

const resultContent = document.createElement('div');
resultContent.innerHTML = `
    <div style="text-align: center; color: var(--text-muted); padding: 40px;">
        <div style="font-size: 48px; margin-bottom: 10px;">🧪</div>
        <div style="font-size: 18px;">准备测试搜索模式</div>
    </div>
`;

resultDiv.appendChild(resultContent);

// 组装界面
mainDiv.appendChild(testDiv);
mainDiv.appendChild(resultDiv);
container.appendChild(mainDiv);

// 测试函数
async function testSearchMode(mode) {
    const input = document.getElementById('test-keywords');
    const keywords = input.value.trim().split(/\s+/).filter(k => k.length > 0);
    
    if (keywords.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                <div>请输入测试关键词</div>
            </div>
        `;
        return;
    }
    
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
            <div>正在测试 ${mode} 模式...</div>
        </div>
    `;
    
    try {
        // 获取所有页面
        const allPages = dv.pages();
        const excludeDirs = ['.obsidian'];
        const searchPages = allPages.where(p => 
            !excludeDirs.some(dir => p.file.path.includes(dir))
        );
        
        const results = [];
        let totalChecked = 0;
        
        for (const page of searchPages) {
            totalChecked++;
            try {
                const content = await dv.io.load(page.file.path);
                const fileName = page.file.name.replace('.md', '');
                
                let matched = false;
                let matchType = '';
                let matchDetails = '';
                
                // 检查文件名匹配
                const fileNameMatch = testMatch(fileName, keywords, mode);
                if (fileNameMatch.matched) {
                    matched = true;
                    matchType = '文件名';
                    matchDetails = fileNameMatch.details;
                }
                
                // 检查内容匹配
                if (!matched) {
                    const contentMatch = testMatch(content, keywords, mode);
                    if (contentMatch.matched) {
                        matched = true;
                        matchType = '文件内容';
                        matchDetails = contentMatch.details;
                    }
                }
                
                if (matched) {
                    results.push({
                        name: fileName,
                        path: page.file.path,
                        link: page.file.link,
                        matchType: matchType,
                        matchDetails: matchDetails,
                        mtime: page.file.mtime
                    });
                }
            } catch (error) {
                // 跳过无法读取的文件
            }
        }
        
        displayTestResults(results, keywords, mode, totalChecked);
        
    } catch (error) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-error); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div>测试失败: ${error.message}</div>
            </div>
        `;
    }
}

// 增强的匹配函数，返回详细信息
function testMatch(text, keywords, mode) {
    if (!text || !keywords || keywords.length === 0) {
        return { matched: false, details: '无效输入' };
    }
    
    const lowerText = text.toLowerCase();
    const lowerKeywords = keywords.map(k => k.toLowerCase());
    
    switch (mode) {
        case 'AND':
            const andMatches = lowerKeywords.filter(k => lowerText.includes(k));
            const andMatched = andMatches.length === lowerKeywords.length;
            return {
                matched: andMatched,
                details: `需要所有关键词: ${lowerKeywords.join(', ')} | 找到: ${andMatches.join(', ')} | 匹配: ${andMatched ? '是' : '否'}`
            };
            
        case 'OR':
            const orMatches = lowerKeywords.filter(k => lowerText.includes(k));
            const orMatched = orMatches.length > 0;
            return {
                matched: orMatched,
                details: `需要任一关键词: ${lowerKeywords.join(', ')} | 找到: ${orMatches.join(', ')} | 匹配: ${orMatched ? '是' : '否'}`
            };
            
        case 'EXACT':
            const exactPhrase = keywords.join(' ').toLowerCase();
            const exactMatched = lowerText.includes(exactPhrase);
            return {
                matched: exactMatched,
                details: `精确短语: "${exactPhrase}" | 匹配: ${exactMatched ? '是' : '否'}`
            };
            
        default:
            return { matched: false, details: '未知模式' };
    }
}

// 显示测试结果
function displayTestResults(results, keywords, mode, totalChecked) {
    let modeDescription = '';
    switch (mode) {
        case 'AND':
            modeDescription = '必须包含所有关键词';
            break;
        case 'OR':
            modeDescription = '包含任意一个关键词即可';
            break;
        case 'EXACT':
            modeDescription = '完全匹配整个短语';
            break;
    }
    
    let html = `
        <div style="margin-bottom: 20px; padding: 15px; background: var(--background-secondary); border-radius: 6px; border-left: 4px solid var(--interactive-accent);">
            <h4 style="margin: 0 0 10px 0; color: var(--text-normal);">🧪 ${mode} 模式测试结果</h4>
            <div style="color: var(--text-muted); font-size: 14px;">
                <strong>模式说明:</strong> ${modeDescription}<br>
                <strong>测试关键词:</strong> ${keywords.join(', ')}<br>
                <strong>检查文件数:</strong> ${totalChecked}<br>
                <strong>匹配结果数:</strong> ${results.length}
            </div>
        </div>
    `;
    
    if (results.length === 0) {
        html += `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">此模式下未找到匹配结果</div>
                <div style="font-size: 14px;">尝试其他搜索模式或关键词</div>
            </div>
        `;
    } else {
        results.forEach((result, index) => {
            const date = new Date(result.mtime).toLocaleDateString('zh-CN');
            const pathParts = result.path.split('/');
            const directory = pathParts.length > 1 ? pathParts[0] : '根目录';
            
            html += `
                <div style="border: 1px solid var(--background-modifier-border); border-radius: 8px; margin-bottom: 15px; padding: 15px; background: var(--background-secondary);">
                    <h4 style="margin: 0 0 8px 0; color: var(--text-normal);">
                        <a href="${result.link}" style="text-decoration: none; color: var(--link-color); font-weight: bold;">
                            📄 ${result.name}
                        </a>
                    </h4>
                    <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 8px;">
                        <span style="margin-right: 15px;">📁 ${directory}</span>
                        <span style="margin-right: 15px;">📅 ${date}</span>
                        <span>🔍 匹配: ${result.matchType}</span>
                    </div>
                    <div style="background: var(--background-primary); padding: 10px; border-radius: 4px; border-left: 3px solid var(--color-green);">
                        <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 5px;">
                            <strong>匹配详情:</strong>
                        </div>
                        <div style="font-size: 11px; color: var(--text-normal); font-family: monospace;">
                            ${result.matchDetails}
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    resultContent.innerHTML = html;
}

// 事件绑定
setTimeout(() => {
    const testInput = document.getElementById('test-keywords');
    const testOrBtn = document.getElementById('test-or');
    const testAndBtn = document.getElementById('test-and');
    const testExactBtn = document.getElementById('test-exact');
    
    if (testOrBtn) testOrBtn.onclick = () => testSearchMode('OR');
    if (testAndBtn) testAndBtn.onclick = () => testSearchMode('AND');
    if (testExactBtn) testExactBtn.onclick = () => testSearchMode('EXACT');
    
    if (testInput) {
        testInput.onkeypress = function(e) {
            if (e.key === 'Enter') {
                testSearchMode('OR'); // 默认测试OR模式
            }
        };
    }
    
    // 设置默认测试关键词
    if (testInput) {
        testInput.value = 'MCP 配置';
    }
}, 100);
```

## 📖 测试说明

### 🧪 测试目的
验证三种搜索模式的区别：
- **OR 模式**：包含任意一个关键词即可
- **AND 模式**：必须包含所有关键词
- **精确匹配**：完全匹配整个短语

### 🔍 建议测试
1. **输入 "MCP 配置"**：
   - OR：找到包含"MCP"或"配置"的文件
   - AND：只找到同时包含"MCP"和"配置"的文件
   - 精确：只找到包含"MCP 配置"这个完整短语的文件

2. **输入 "任务 管理 系统"**：
   - OR：包含任意一个词的文件
   - AND：必须同时包含三个词的文件
   - 精确：包含"任务 管理 系统"完整短语的文件

### 📊 结果分析
每个结果会显示详细的匹配信息，帮助理解不同模式的工作原理。

---

*💡 这个测试系统会显示每种模式的详细匹配过程，帮助验证搜索逻辑是否正确。*
