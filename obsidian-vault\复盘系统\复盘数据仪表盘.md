---
tags:
  - dashboard
  - review-system
created: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
updated: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
---
# 📊 复盘数据仪表盘

> 智能复盘系统的数据可视化中心，汇总展示各层次复盘数据和趋势分析

## 🎯 快速概览

### 📈 复盘完成情况统计
```dataviewjs
// 统计各类型复盘的完成情况
const now = new Date();
const thisYear = now.getFullYear();
const thisMonth = now.getMonth() + 1;
const thisWeek = Math.ceil((now.getDate() - now.getDay()) / 7);

// 查询包含复盘内容的笔记
const dailyReviews = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.name.includes(thisYear.toString()))
    .length;

const weeklyReviews = dv.pages('"0_Bullet Journal/Weekly Notes"')
    .where(p => p.file.name.includes(thisYear.toString()))
    .length;

const monthlyReviews = dv.pages('"0_Bullet Journal/Monthly Notes"')
    .where(p => p.file.name.includes(thisYear.toString()))
    .length;

const yearlyReviews = dv.pages('"0_Bullet Journal/Yearly Notes"')
    .where(p => p.file.name.includes(thisYear.toString()))
    .length;

// 显示统计表格
dv.table(
    ["复盘类型", "已完成", "完成率", "状态"],
    [
        ["📅 日复盘", dailyReviews, `${Math.round(dailyReviews/365*100)}%`, dailyReviews > 0 ? "✅ 进行中" : "⏸️ 未开始"],
        ["📊 周复盘", weeklyReviews, `${Math.round(weeklyReviews/52*100)}%`, weeklyReviews > 0 ? "✅ 进行中" : "⏸️ 未开始"],
        ["📈 月复盘", monthlyReviews, `${Math.round(monthlyReviews/12*100)}%`, monthlyReviews > 0 ? "✅ 进行中" : "⏸️ 未开始"],
        ["🎉 年复盘", yearlyReviews, yearlyReviews > 0 ? "100%" : "0%", yearlyReviews > 0 ? "✅ 已完成" : "⏸️ 未开始"]
    ]
);
```

---
## 📅 最近复盘记录

### 🌟 最近7天的日复盘
```dataview
TABLE without id 
    file.link as "日期",
    choice(contains(string(file), "今日成就"), "✅", "⏸️") as "成就",
    choice(contains(string(file), "今日反思"), "✅", "⏸️") as "反思", 
    choice(contains(string(file), "今日收获"), "✅", "⏸️") as "收获",
    choice(contains(string(file), "今日感恩"), "✅", "⏸️") as "感恩"
FROM "0_Bullet Journal/Daily Notes"
WHERE file.ctime >= date(today) - dur(7 days)
SORT file.name DESC
LIMIT 7
```

### 📊 最近4周的周复盘
```dataview
TABLE without id 
    file.link as "周期",
    choice(contains(string(file), "汇总本周事件"), "✅", "⏸️") as "事件汇总",
    choice(contains(string(file), "计划完成情况"), "✅", "⏸️") as "计划完成", 
    choice(contains(string(file), "本周反思"), "✅", "⏸️") as "反思总结",
    choice(contains(string(file), "下周调整与计划"), "✅", "⏸️") as "下周计划"
FROM "0_Bullet Journal/Weekly Notes"
WHERE file.ctime >= date(today) - dur(28 days)
SORT file.name DESC
LIMIT 4
```

### 📈 最近12个月的月复盘
```dataview
TABLE without id 
    file.link as "月份",
    choice(contains(string(file), "汇总本月成就"), "✅", "⏸️") as "成就汇总",
    choice(contains(string(file), "月目标完成情况"), "✅", "⏸️") as "目标完成", 
    choice(contains(string(file), "本月满意度打分"), "✅", "⏸️") as "满意度评分",
    choice(contains(string(file), "下月目标"), "✅", "⏸️") as "下月目标"
FROM "0_Bullet Journal/Monthly Notes"
WHERE file.ctime >= date(today) - dur(365 days)
SORT file.name DESC
LIMIT 12
```

---
## 🎯 复盘质量分析

### 📝 复盘内容完整度
```dataviewjs
// 分析复盘内容的完整度
const recentDailies = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.ctime >= dv.date('today') - dv.duration('30 days'))
    .sort(p => p.file.name, 'desc')
    .limit(10);

let completenessData = [];

for (let page of recentDailies) {
    const content = await dv.io.load(page.file.path);
    
    // 检查各个复盘维度是否有内容
    const hasAchievement = content.includes('今日成就') && content.match(/今日成就[\s\S]*?-\s+\S/);
    const hasReflection = content.includes('今日反思') && content.match(/今日反思[\s\S]*?-\s+\S/);
    const hasLearning = content.includes('今日收获') && content.match(/今日收获[\s\S]*?-\s+\S/);
    const hasGratitude = content.includes('今日感恩') && content.match(/今日感恩[\s\S]*?-\s+\S/);
    
    const completeness = [hasAchievement, hasReflection, hasLearning, hasGratitude].filter(Boolean).length;
    const completenessPercent = Math.round(completeness / 4 * 100);
    
    completenessData.push([
        page.file.link,
        `${completenessPercent}%`,
        hasAchievement ? "✅" : "⏸️",
        hasReflection ? "✅" : "⏸️",
        hasLearning ? "✅" : "⏸️",
        hasGratitude ? "✅" : "⏸️"
    ]);
}

if (completenessData.length > 0) {
    dv.table(
        ["日期", "完整度", "成就", "反思", "收获", "感恩"],
        completenessData
    );
} else {
    dv.paragraph("暂无最近的日复盘数据");
}
```

---
## 📊 趋势分析

### 📈 复盘频率趋势
```dataviewjs
// 分析最近30天的复盘频率
const last30Days = [];
const today = new Date();

for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];
    
    // 检查该日期是否有复盘记录
    const hasReview = dv.pages('"0_Bullet Journal/Daily Notes"')
        .where(p => p.file.name.includes(dateStr))
        .length > 0;
    
    last30Days.push({
        date: dateStr,
        hasReview: hasReview
    });
}

// 计算复盘频率
const reviewDays = last30Days.filter(day => day.hasReview).length;
const frequency = Math.round(reviewDays / 30 * 100);

dv.paragraph(`**最近30天复盘频率**: ${frequency}% (${reviewDays}/30天)`);

// 显示最近7天的复盘情况
const recent7Days = last30Days.slice(-7);
const frequencyChart = recent7Days.map(day => 
    `${day.date.split('-')[2]}: ${day.hasReview ? '✅' : '⏸️'}`
).join(' | ');

dv.paragraph(`**最近7天**: ${frequencyChart}`);
```

---
## 🎯 快速操作

### 📝 创建复盘笔记
- [[Templates/5_BuJo - Daily Log|📅 创建日复盘]]
- [[Templates/5_BuJo - Weekly Log|📊 创建周复盘]]  
- [[Templates/5_BuJo - Monthly Log|📈 创建月复盘]]
- [[Templates/5_BuJo - Yearly Log|🎉 创建年复盘]]

### 🔍 复盘数据查询
```dataview
LIST
FROM "0_Bullet Journal"
WHERE contains(file.name, "复盘") OR contains(string(file), "日复盘") OR contains(string(file), "周复盘") OR contains(string(file), "月复盘") OR contains(string(file), "年复盘")
SORT file.mtime DESC
LIMIT 10
```

---
## ⚙️ 系统配置

### 📋 复盘模板配置
- 复盘维度：成就、反思、收获、美好、感恩
- 数据收集：自动从现有笔记中提取
- 提醒设置：每日21:00提醒复盘
- 数据保留：永久保存，支持历史查询

### 🔄 数据同步状态
- ✅ Daily Notes 集成完成
- ✅ Weekly Notes 集成完成  
- ✅ Monthly Notes 集成完成
- ✅ Yearly Notes 集成完成
- ✅ 数据仪表盘运行正常

---
*最后更新：<% tp.date.now("YYYY-MM-DD HH:mm") %>*
