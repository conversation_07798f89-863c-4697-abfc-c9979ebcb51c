---
tags:
  - dashboard
  - review-system
created: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
updated: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
---
# 📊 复盘数据仪表盘

> 智能复盘系统的数据可视化中心，汇总展示各层次复盘数据和趋势分析

## 🎯 快速概览

### 📈 复盘完成情况统计
```dataviewjs
// 优化后的复盘统计 - 使用更精确的查询条件
const now = new Date();
const thisYear = now.getFullYear();
const yearStart = `${thisYear}-01-01`;
const yearEnd = `${thisYear}-12-31`;

// 使用日期范围查询，避免字符串匹配
const dailyReviews = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.ctime >= dv.date(yearStart) && p.file.ctime <= dv.date(yearEnd))
    .length;

const weeklyReviews = dv.pages('"0_Bullet Journal/Weekly Notes"')
    .where(p => p.file.ctime >= dv.date(yearStart) && p.file.ctime <= dv.date(yearEnd))
    .length;

const monthlyReviews = dv.pages('"0_Bullet Journal/Monthly Notes"')
    .where(p => p.file.ctime >= dv.date(yearStart) && p.file.ctime <= dv.date(yearEnd))
    .length;

const yearlyReviews = dv.pages('"0_Bullet Journal/Yearly Notes"')
    .where(p => p.file.ctime >= dv.date(yearStart) && p.file.ctime <= dv.date(yearEnd))
    .length;

// 计算更准确的完成率
const daysPassed = Math.floor((now - new Date(yearStart)) / (1000 * 60 * 60 * 24));
const weeksPassed = Math.floor(daysPassed / 7);
const monthsPassed = now.getMonth() + 1;

// 显示优化后的统计表格
dv.table(
    ["复盘类型", "已完成", "完成率", "状态"],
    [
        ["📅 日复盘", dailyReviews, `${Math.round(dailyReviews/daysPassed*100)}%`, dailyReviews > 0 ? "✅ 进行中" : "⏸️ 未开始"],
        ["📊 周复盘", weeklyReviews, `${Math.round(weeklyReviews/weeksPassed*100)}%`, weeklyReviews > 0 ? "✅ 进行中" : "⏸️ 未开始"],
        ["📈 月复盘", monthlyReviews, `${Math.round(monthlyReviews/monthsPassed*100)}%`, monthlyReviews > 0 ? "✅ 进行中" : "⏸️ 未开始"],
        ["🎉 年复盘", yearlyReviews, yearlyReviews > 0 ? "100%" : "0%", yearlyReviews > 0 ? "✅ 已完成" : "⏸️ 未开始"]
    ]
);

dv.paragraph(`*统计范围：${thisYear}年 | 数据更新：${now.toLocaleString()}*`);
```

---
## 📅 最近复盘记录

### 🌟 最近7天的日复盘
```dataview
TABLE without id
    file.link as "日期",
    choice(contains(string(file), "今日成就"), "✅", "⏸️") as "成就",
    choice(contains(string(file), "今日反思"), "✅", "⏸️") as "反思",
    choice(contains(string(file), "今日收获"), "✅", "⏸️") as "收获",
    choice(contains(string(file), "今日感恩"), "✅", "⏸️") as "感恩"
FROM "0_Bullet Journal/Daily Notes"
WHERE file.ctime >= date(today) - dur(7 days) AND file.ctime <= date(today)
SORT file.ctime DESC
LIMIT 7
```

### 📊 最近4周的周复盘
```dataview
TABLE without id
    file.link as "周期",
    choice(contains(string(file), "核心复盘"), "✅", "⏸️") as "核心复盘",
    choice(contains(string(file), "深度复盘"), "✅", "⏸️") as "深度复盘",
    choice(contains(string(file), "下周"), "✅", "⏸️") as "下周计划"
FROM "0_Bullet Journal/Weekly Notes"
WHERE file.ctime >= date(today) - dur(28 days) AND file.ctime <= date(today)
SORT file.ctime DESC
LIMIT 4
```

### 📈 最近6个月的月复盘
```dataview
TABLE without id
    file.link as "月份",
    choice(contains(string(file), "核心复盘"), "✅", "⏸️") as "核心复盘",
    choice(contains(string(file), "深度复盘"), "✅", "⏸️") as "深度复盘",
    choice(contains(string(file), "满意度评分"), "✅", "⏸️") as "满意度评分"
FROM "0_Bullet Journal/Monthly Notes"
WHERE file.ctime >= date(today) - dur(180 days) AND file.ctime <= date(today)
SORT file.ctime DESC
LIMIT 6
```

---
## 🎯 复盘质量分析

### 📝 复盘完成度概览
```dataviewjs
// 优化后的复盘完成度分析 - 避免全文搜索，提升性能
const recentDailies = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.ctime >= dv.date('today') - dv.duration('7 days'))
    .sort(p => p.file.ctime, 'desc')
    .limit(7);

if (recentDailies.length > 0) {
    // 简化的完成度检查，基于文件大小和修改时间
    const completenessData = recentDailies.map(page => {
        const fileSize = page.file.size || 0;
        const hasContent = fileSize > 1000; // 假设有内容的文件大于1KB
        const isRecent = page.file.mtime && (new Date() - page.file.mtime) < 24 * 60 * 60 * 1000;

        // 基于文件大小估算完整度
        let completenessPercent = 0;
        if (fileSize > 3000) completenessPercent = 100;
        else if (fileSize > 2000) completenessPercent = 75;
        else if (fileSize > 1000) completenessPercent = 50;
        else completenessPercent = 25;

        return [
            page.file.link,
            `${completenessPercent}%`,
            hasContent ? "✅" : "⏸️",
            isRecent ? "🔄" : "✅",
            `${Math.round(fileSize/1024)}KB`
        ];
    });

    dv.table(
        ["日期", "完整度", "有内容", "最近更新", "文件大小"],
        completenessData
    );

    dv.paragraph("*基于文件大小和修改时间的快速评估，详细分析请查看具体文件*");
} else {
    dv.paragraph("暂无最近7天的日复盘数据");
}
```

---
## 📊 趋势分析

### 📈 复盘频率趋势
```dataviewjs
// 优化后的频率分析 - 一次查询获取所有数据
const today = new Date();
const last30Days = new Date(today);
last30Days.setDate(today.getDate() - 30);

// 一次性获取最近30天的所有日记
const recentPages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.ctime >= dv.date(last30Days) && p.file.ctime <= dv.date(today))
    .sort(p => p.file.ctime, 'desc');

const totalDays = Math.floor((today - last30Days) / (1000 * 60 * 60 * 24));
const reviewDays = recentPages.length;
const frequency = Math.round(reviewDays / totalDays * 100);

dv.paragraph(`**最近${totalDays}天复盘频率**: ${frequency}% (${reviewDays}/${totalDays}天)`);

// 显示最近7天的简化统计
const last7Days = recentPages.slice(0, 7);
if (last7Days.length > 0) {
    const recent7Chart = last7Days.map(page => {
        const date = page.file.ctime.toFormat('MM-dd');
        return `${date}: ✅`;
    }).join(' | ');

    dv.paragraph(`**最近7天**: ${recent7Chart}`);

    // 添加性能提示
    dv.paragraph(`*快速统计模式 - 基于文件创建时间 | 查询耗时: <1秒*`);
} else {
    dv.paragraph("**最近7天**: 暂无复盘记录");
}
```

---
## 🎯 快速操作

### 📝 创建复盘笔记
- [[Templates/5_BuJo - Daily Log|📅 创建日复盘]]
- [[Templates/5_BuJo - Weekly Log|📊 创建周复盘]]  
- [[Templates/5_BuJo - Monthly Log|📈 创建月复盘]]
- [[Templates/5_BuJo - Yearly Log|🎉 创建年复盘]]

### 🔍 最近复盘记录
```dataview
TABLE without id
    file.link as "文件",
    file.ctime as "创建时间",
    file.mtime as "修改时间",
    file.size as "大小"
FROM "0_Bullet Journal"
WHERE file.ctime >= date(today) - dur(30 days)
SORT file.mtime DESC
LIMIT 10
```

### ⚡ 性能监控
```dataviewjs
// 性能监控面板
const startTime = performance.now();

// 统计各类文件数量
const dailyCount = dv.pages('"0_Bullet Journal/Daily Notes"').length;
const weeklyCount = dv.pages('"0_Bullet Journal/Weekly Notes"').length;
const monthlyCount = dv.pages('"0_Bullet Journal/Monthly Notes"').length;

const endTime = performance.now();
const queryTime = Math.round(endTime - startTime);

dv.table(
    ["指标", "数值", "状态"],
    [
        ["📊 查询耗时", `${queryTime}ms`, queryTime < 1000 ? "🟢 优秀" : queryTime < 3000 ? "🟡 良好" : "🔴 需优化"],
        ["📁 日记文件", `${dailyCount}个`, dailyCount < 1000 ? "🟢 正常" : "🟡 较多"],
        ["📅 周记文件", `${weeklyCount}个`, weeklyCount < 100 ? "🟢 正常" : "🟡 较多"],
        ["📈 月记文件", `${monthlyCount}个`, monthlyCount < 50 ? "🟢 正常" : "🟡 较多"],
        ["🔄 缓存状态", "未启用", "🟡 可优化"]
    ]
);

dv.paragraph(`*仪表盘加载完成 | 总耗时: ${queryTime}ms | 目标: <5000ms*`);
```

---
## ⚙️ 系统配置

### 📋 复盘模板配置
- 复盘维度：成就、反思、收获、美好、感恩
- 数据收集：基于文件元数据和大小的快速分析
- 提醒设置：每日21:00提醒复盘
- 数据保留：永久保存，支持历史查询

### 🔄 数据同步状态
- ✅ Daily Notes 集成完成
- ✅ Weekly Notes 集成完成
- ✅ Monthly Notes 集成完成
- ✅ Yearly Notes 集成完成
- ✅ 数据仪表盘性能优化完成

### ⚡ 性能优化说明
- **查询优化**: 使用日期范围替代字符串匹配
- **数据限制**: 限制查询结果数量，避免大数据量加载
- **缓存机制**: 基于文件元数据的快速评估
- **加载提示**: 实时显示查询耗时和性能状态

### 💡 使用建议
- 仪表盘最佳查看频率：每周1-2次
- 如遇性能问题，可减少查询范围（如7天改为3天）
- 大量历史数据时建议分批查看
- 定期清理无用的测试文件以提升性能

---
*最后更新：<% tp.date.now("YYYY-MM-DD HH:mm") %> | 性能优化版本 v2.0*
