## MCP Prompt 示例

### 基础MCP工具调用

```python
# 交互式反馈工具
result = mcp_mcp-feedback-enhanced_interactive_feedback(
    project_directory=".",
    summary="任务已完成，请查看结果",
    timeout=600
)

# 获取系统信息
system_info = mcp_mcp-feedback-enhanced_get_system_info(random_string="info")
```

### Context7 文档查询示例

```python
# 解析库ID
library_id = mcp_context7_resolve-library-id(libraryName="react")

# 获取库文档
docs = mcp_context7_get-library-docs(
    context7CompatibleLibraryID=library_id,
    tokens=10000,
    topic="hooks"
)
```

### 网络请求示例

```python
# 获取网页内容
web_content = mcp_fetch_fetch(
    url="https://example.com",
    max_length=5000,
    raw=false
)
``` 