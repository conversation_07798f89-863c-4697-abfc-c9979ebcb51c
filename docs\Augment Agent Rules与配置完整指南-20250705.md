# Augment Agent Rules与配置完整指南

> 📅 创建时间：2025-07-05 星期六
> 🤖 适用对象：Augment Agent (基于 Claude Sonnet 4)
> 📝 版本：v1.0
> 🎯 目标：提供完整的 Augment Agent 配置与使用指南

---

## 📋 目录

1. [功能概述](#功能概述)
2. [配置方法](#配置方法)
3. [最佳实践](#最佳实践)
4. [实际应用](#实际应用)
5. [故障排除](#故障排除)
6. [进阶配置](#进阶配置)
7. [附录](#附录)

---

## 🎯 功能概述

### Rules 和 User Guidelines 的作用机制

**Augment Agent** 通过两种配置方式来定制AI助手的行为：

#### 1. Rules（项目级别配置）
- **文件位置**：项目根目录的 `.augment-guidelines` 文件
- **作用范围**：仅在当前项目/工作区生效
- **优先级**：最高（覆盖全局设置）
- **自动加载**：每次会话开始时自动读取
- **适用场景**：项目特定的工作流程、技术栈偏好、文件结构约定

#### 2. User Guidelines（全局用户偏好）
- **配置位置**：Augment 设置面板中的全局配置
- **作用范围**：在所有项目中生效
- **优先级**：低于项目级别配置
- **适用场景**：通用的沟通方式、代码风格、工作习惯

### 配置优先级机制

```
项目级别 .augment-guidelines > 全局 User Guidelines > 系统默认行为
```

### 影响范围

- **响应风格**：语言偏好、详细程度、专业术语使用
- **工作流程**：任务分解方式、文档创建规范、反馈机制
- **工具选择**：MCP服务优先级、技术栈偏好
- **文件管理**：路径约定、命名规范、存储结构

---

## 🔧 配置方法

### 方法1：项目级别配置（推荐）

#### 步骤1：创建配置文件

在项目根目录创建 `.augment-guidelines` 文件：

```bash
# 在项目根目录执行
touch .augment-guidelines
```

#### 步骤2：编写配置内容

使用以下模板结构：

```markdown
# 项目名称 Augment Agent Guidelines

> 📅 更新时间：YYYY-MM-DD 星期X
> 🤖 适用对象：Augment Agent
> 📝 版本：v1.0

你是Augment IDE的AI编程助手，用中文协助用户

## 📋 任务执行流程
[定义任务处理流程]

## 🛠️ MCP服务优先使用
[列出优先使用的MCP工具]

## 🔧 技术配置标准
[技术实现偏好]

## 💡 沟通协作偏好
[沟通方式和协作规范]

## 📁 路径约定
[文件存储和命名规范]
```

#### 步骤3：验证配置生效

1. **保存文件**：确保文件保存在正确位置
2. **重启会话**：开始新的 Augment Agent 会话
3. **测试行为**：观察AI是否按照配置行为

### 方法2：全局 User Guidelines 配置

#### 访问设置面板

1. 打开 Augment 设置（Ctrl/Cmd + ,）
2. 找到 "User Guidelines" 选项
3. 在文本框中输入全局偏好设置

#### 配置内容示例

```markdown
## 🌐 全局工作偏好

### 沟通方式
- 使用中文进行所有交流
- 提供详细的解释和步骤说明
- 遇到不确定情况时主动询问

### 代码风格
- 优先使用现代化的编程实践
- 注重代码可读性和维护性
- 添加必要的注释和文档

### 工具偏好
- 优先使用包管理器而非手动编辑配置文件
- 使用 Git 进行版本控制
- 重视测试和代码质量
```

#### 已知问题及解决方案

**问题**：User Guidelines 面板可能出现以下问题：
- 重启后配置清空
- 设置面板不显示
- 保存按钮无响应

**解决方案**：
1. **主要依赖项目级别配置**：使用 `.augment-guidelines` 文件
2. **创建备份文件**：保存全局配置的备份版本
3. **定期验证**：检查配置是否生效

---

## 🎯 最佳实践

### 配置内容设计原则

#### 1. 结构化组织
```markdown
# 使用清晰的分层结构
## 一级标题：主要功能模块
### 二级标题：具体配置项
- 列表项：详细说明
```

#### 2. 具体化描述
```markdown
# ❌ 避免模糊描述
- 提高代码质量

# ✅ 使用具体描述
- 使用现代化的编程实践
- 添加必要的注释和文档
- 编写单元测试验证功能
```

#### 3. 优先级明确
```markdown
## 🛠️ MCP服务优先使用（已测试可用）

### 核心服务 ✅
- `interactive_feedback`: 用户反馈交互（已测试）
- `sequential-thinking`: 复杂任务分解（已测试）

### 扩展服务
- `Context7`: 查询最新库文档
- `Playwright`: 浏览器自动化操作
```

### 配置文件管理

#### 版本控制
```markdown
> 📝 版本：v3.0
> 🔄 更新日志：
> - v3.0 (2025-07-05) - 基于MCP工具测试结果完善配置
> - v2.0 (2025-07-03) - 新增记忆管理协同约定
> - v1.0 (2025-06-16) - 初始版本
```

#### 文件备份
```bash
# 创建配置备份
cp .augment-guidelines .augment-guidelines.backup

# 定期备份到版本控制
git add .augment-guidelines
git commit -m "更新 Augment Agent 配置"
```

### 测试验证流程

#### 1. 功能测试清单
- [ ] 中文交流测试
- [ ] 任务流程测试
- [ ] MCP工具调用测试
- [ ] 文档创建测试
- [ ] 路径约定测试

#### 2. 行为验证方法
```markdown
# 测试复杂任务处理
给AI一个需要多步骤的任务，观察是否：
1. 自动识别为复杂任务
2. 提议创建issues文档
3. 使用指定的MCP工具
4. 按照配置的路径约定工作
```

---

## 🚀 实际应用

### 基于测试库项目的配置示例

#### 完整配置文件
```markdown
# 测试库项目 Augment Agent Guidelines

> 📅 更新时间：2025-07-05 星期六
> 🤖 适用对象：Augment Agent (基于 Claude Sonnet 4)
> 📝 版本：v3.0

你是Augment IDE的AI编程助手，用中文协助用户

## 📋 任务执行流程

### 阶段1：任务分析与计划制定
- **复杂任务识别**：预计超过3个主要步骤的任务自动创建任务计划文档
- **任务命名格式**：`核心功能-YYYYMMDD`（如：`MCP配置-20250705`）
- **计划文档存储**：`./issues/任务名.md`，使用标准模板
- **深度分析**：复杂任务优先使用 `sequential-thinking` 进行分析

### 阶段2：任务执行与反馈
- **严格按计划执行**：按照 issues 文档中的计划逐步执行
- **关键节点反馈**：在以下时机使用 `interactive-feedback`：
  - 完成计划制定后
  - 每个主要步骤完成后
  - 遇到问题需要用户确认时
  - 任务完成时

### 阶段3：任务复盘与总结
- **复盘文档**：任务完成后创建复盘文档：`./rewind/任务名.md`
- **复盘内容**：问题分析、解决方案、经验总结、后续建议
- **经验提取**：将重要经验更新到工作偏好设置中

## 🛠️ MCP服务优先使用（已测试可用）

### 核心服务 ✅
- `interactive_feedback`: 用户反馈交互（已测试）
- `sequential-thinking`: 复杂任务分解与深度思考（已测试）
- `shrimp-task-manager`: 任务规划和管理（已测试）
- `寸止MCP`: AI对话智能拦截和记忆管理（已测试）

### 扩展服务
- `Context7`: 查询最新库文档/示例
- `Playwright`: 浏览器自动化操作
- `together-image-gen`: 图像生成
- 其他可用MCP服务根据需要调用
```

### MCP工具集成配置

#### 已测试工具配置经验
```markdown
## 🤝 记忆管理协同约定

### 双记忆系统分工
- **Augment Agent记忆**：全局工作偏好、长期协作原则、标准流程模板
- **寸止MCP记忆**：项目特定规则、临时上下文、动态配置信息

### 记忆使用原则
- **分层存储**：核心原则存储在Augment记忆，具体项目经验存储在寸止记忆
- **避免重复**：相同信息不在两个系统中重复存储
- **定期同步**：重要的项目经验定期提升为全局原则
- **清晰边界**：明确区分全局性和项目性的信息
```

---

## 🔧 故障排除

### 常见问题及解决方案

#### 问题1：配置不生效

**症状**：
- AI行为没有按照配置文件执行
- 仍然使用默认的英文交流
- 没有按照指定的工作流程

**排查步骤**：
1. **检查文件位置**：确认 `.augment-guidelines` 在项目根目录
2. **检查文件格式**：确认使用UTF-8编码，Markdown格式正确
3. **检查语法**：确认没有格式错误或特殊字符
4. **重启会话**：开始新的Augment Agent会话

**解决方案**：
```bash
# 检查文件位置
ls -la .augment-guidelines

# 检查文件内容
cat .augment-guidelines | head -10

# 检查文件编码
file .augment-guidelines
```

#### 问题2：User Guidelines 面板问题

**症状**：
- 设置面板中看不到User Guidelines选项
- 配置保存后重启清空
- 保存按钮无响应

**解决方案**：
1. **使用项目级别配置**：主要依赖 `.augment-guidelines` 文件
2. **创建备份文件**：
```bash
# 创建全局配置备份
mkdir -p config
cat > config/augment-global-guidelines.md << 'EOF'
# Augment Agent 全局用户偏好设置
[配置内容]
EOF
```

#### 问题3：MCP工具冲突

**症状**：
- 某些MCP工具无法正常调用
- 工具功能重复或冲突
- 性能下降

**解决方案**：
1. **功能分工明确**：
```markdown
## 🛠️ MCP工具协同原则
- **功能互补**：不同MCP工具负责不同功能领域，避免重复
- **分层管理**：Augment记忆处理全局偏好，寸止记忆处理项目特定规则
- **测试验证**：新MCP工具配置后必须进行功能测试和冲突检查
```

2. **逐步测试**：
```bash
# 测试单个MCP工具
# 1. 先测试核心工具
# 2. 逐个添加扩展工具
# 3. 验证功能正常
```

### 调试技巧

#### 1. 配置验证脚本
```bash
#!/bin/bash
# augment-config-check.sh

echo "=== Augment Agent 配置检查 ==="

# 检查配置文件存在
if [ -f ".augment-guidelines" ]; then
    echo "✅ .augment-guidelines 文件存在"
    echo "📄 文件大小: $(wc -c < .augment-guidelines) 字节"
    echo "📝 行数: $(wc -l < .augment-guidelines) 行"
else
    echo "❌ .augment-guidelines 文件不存在"
fi

# 检查文件编码
echo "🔤 文件编码: $(file -b --mime-encoding .augment-guidelines)"

# 检查基本语法
echo "📋 配置文件预览:"
head -5 .augment-guidelines
```

#### 2. 行为测试清单
```markdown
## 🧪 配置生效测试

### 基础功能测试
- [ ] AI使用中文回复
- [ ] 复杂任务自动创建issues文档
- [ ] 使用指定的MCP工具
- [ ] 遵循路径约定

### 高级功能测试
- [ ] 任务分解和规划
- [ ] 交互式反馈机制
- [ ] 复盘文档创建
- [ ] 记忆管理协同

### 性能测试
- [ ] 响应速度正常
- [ ] 工具调用无冲突
- [ ] 内存使用合理
```

---

## 🎓 进阶配置

### 多项目配置管理

#### 项目类型分类配置
```markdown
# 不同项目类型的配置策略

## Web开发项目
- 优先使用 Playwright 进行浏览器测试
- 强调响应式设计和性能优化
- 使用现代前端框架最佳实践

## Python数据科学项目
- 优先使用 Jupyter Notebook 工作流
- 强调数据可视化和统计分析
- 注重代码可重现性

## 文档管理项目
- 优先使用 Obsidian 相关工具
- 强调知识重构和内容组织
- 注重 Markdown 格式规范
```

#### 配置继承机制
```bash
# 创建通用配置模板
mkdir -p config/templates

# 基础配置模板
cat > config/templates/.augment-guidelines-base << 'EOF'
# 通用 Augment Agent Guidelines

你是Augment IDE的AI编程助手，用中文协助用户

## 💡 通用沟通协作偏好
- 中文交流
- 详细解释
- 主动询问

## 🔧 通用技术标准
- 使用包管理器
- 代码质量优先
- 测试驱动开发
EOF

# 项目特定配置
cat config/templates/.augment-guidelines-base > .augment-guidelines
echo "## 📋 项目特定配置" >> .augment-guidelines
echo "[添加项目特定内容]" >> .augment-guidelines
```

### 动态配置更新

#### 配置版本管理
```markdown
## 🔄 配置版本控制策略

### 语义化版本
- **主版本号**：重大配置结构变更
- **次版本号**：新增功能配置
- **修订版本号**：bug修复和小调整

### 变更记录格式
```markdown
> 🔄 更新日志：
> - v3.1.2 (2025-07-05) - 修复MCP工具冲突问题
> - v3.1.0 (2025-07-03) - 新增寸止MCP集成配置
> - v3.0.0 (2025-07-01) - 重构整体配置架构
```

#### 自动化配置管理
```bash
#!/bin/bash
# config-manager.sh - 配置管理脚本

# 备份当前配置
backup_config() {
    timestamp=$(date +%Y%m%d_%H%M%S)
    cp .augment-guidelines "config/backups/.augment-guidelines_$timestamp"
    echo "✅ 配置已备份到: config/backups/.augment-guidelines_$timestamp"
}

# 恢复配置
restore_config() {
    if [ -z "$1" ]; then
        echo "❌ 请指定要恢复的备份文件"
        return 1
    fi
    cp "config/backups/$1" .augment-guidelines
    echo "✅ 配置已恢复从: $1"
}

# 验证配置
validate_config() {
    if [ ! -f ".augment-guidelines" ]; then
        echo "❌ 配置文件不存在"
        return 1
    fi

    # 检查基本格式
    if ! grep -q "你是Augment IDE的AI编程助手" .augment-guidelines; then
        echo "⚠️  警告：缺少基本身份定义"
    fi

    echo "✅ 配置文件验证通过"
}
```

### 团队协作配置

#### 共享配置标准
```markdown
## 👥 团队配置协作规范

### 配置文件结构标准
```
.augment-guidelines          # 项目主配置
config/
├── templates/              # 配置模板
├── backups/               # 配置备份
├── team-standards.md      # 团队标准
└── migration-guide.md     # 迁移指南
```

### 团队配置同步
```bash
# 团队配置同步脚本
sync_team_config() {
    # 拉取最新的团队标准
    git pull origin main

    # 合并团队标准到本地配置
    if [ -f "config/team-standards.md" ]; then
        echo "📥 应用团队标准配置..."
        # 这里可以添加自动合并逻辑
    fi

    # 验证配置兼容性
    validate_team_compatibility
}
```

---

## 📚 附录

### A. 配置文件模板库

#### A.1 基础模板
```markdown
# 项目名称 Augment Agent Guidelines

> 📅 更新时间：YYYY-MM-DD 星期X
> 📝 版本：v1.0

你是Augment IDE的AI编程助手，用中文协助用户

## 📋 任务执行流程
### 阶段1：任务分析与计划制定
- 复杂任务识别和文档创建

### 阶段2：任务执行与反馈
- 按计划执行和关键节点反馈

### 阶段3：任务复盘与总结
- 创建复盘文档和经验总结

## 🛠️ MCP服务优先使用
- `interactive_feedback`: 用户反馈交互
- `sequential-thinking`: 复杂任务分解

## 💡 沟通协作偏好
- 中文交流
- 详细解释
- 主动询问

## 📁 路径约定
- 任务计划: `./issues/任务名.md`
- 任务复盘: `./rewind/任务名.md`
```

#### A.2 Web开发项目模板
```markdown
# Web开发项目 Augment Agent Guidelines

你是专业的Web开发AI助手，用中文协助用户

## 🌐 Web开发特定配置
### 技术栈偏好
- 现代前端框架（React, Vue, Angular）
- 响应式设计优先
- 性能优化重点关注

### 开发工具
- `Playwright`: 浏览器自动化测试
- `together-image-gen`: UI原型图生成
- 包管理器：npm/yarn/pnpm

### 代码质量标准
- ESLint + Prettier 代码格式化
- TypeScript 类型安全
- 单元测试覆盖率 > 80%

## 📱 响应式设计约定
- 移动优先设计
- 断点标准：320px, 768px, 1024px, 1440px
- 图片优化和懒加载
```

#### A.3 数据科学项目模板
```markdown
# 数据科学项目 Augment Agent Guidelines

你是专业的数据科学AI助手，用中文协助用户

## 📊 数据科学特定配置
### 工作流程
- Jupyter Notebook 为主要开发环境
- 数据探索 → 特征工程 → 模型训练 → 评估优化

### 技术栈
- Python: pandas, numpy, scikit-learn
- 可视化: matplotlib, seaborn, plotly
- 机器学习: tensorflow, pytorch

### 数据管理
- 数据版本控制（DVC）
- 实验跟踪（MLflow）
- 模型部署（Docker）

## 📈 可视化标准
- 图表标题和轴标签清晰
- 颜色方案考虑色盲友好
- 交互式图表优先
```

### B. 常用命令参考手册

#### B.1 配置管理命令
```bash
# 创建和编辑配置
touch .augment-guidelines                    # 创建配置文件
nano .augment-guidelines                     # 编辑配置文件
code .augment-guidelines                     # 用VS Code编辑

# 配置验证
cat .augment-guidelines                      # 查看配置内容
head -10 .augment-guidelines                # 查看前10行
tail -10 .augment-guidelines                # 查看后10行
wc -l .augment-guidelines                   # 统计行数

# 文件属性检查
ls -la .augment-guidelines                  # 查看文件详情
file .augment-guidelines                    # 检查文件类型
stat .augment-guidelines                    # 查看文件状态

# 备份和恢复
cp .augment-guidelines .augment-guidelines.backup    # 创建备份
mv .augment-guidelines.backup .augment-guidelines    # 恢复备份
```

#### B.2 日期和时间命令
```bash
# 获取当前日期（不同格式）
date '+%Y-%m-%d %A'                         # 2025-07-05 星期六
date '+%Y-%m-%d'                           # 2025-07-05
date '+%Y%m%d'                             # 20250705

# PowerShell 日期命令（Windows）
Get-Date -Format 'yyyy-MM-dd dddd'         # 2025-07-05 星期六
Get-Date -Format 'yyyy-MM-dd'              # 2025-07-05
```

#### B.3 Git版本控制命令
```bash
# 配置文件版本控制
git add .augment-guidelines                 # 添加到暂存区
git commit -m "更新Augment配置"             # 提交更改
git log --oneline .augment-guidelines      # 查看配置文件历史

# 配置文件差异比较
git diff .augment-guidelines               # 查看未暂存的更改
git diff --cached .augment-guidelines     # 查看已暂存的更改
git show HEAD:.augment-guidelines         # 查看上次提交的版本
```

### C. 故障排除速查表

#### C.1 常见问题快速诊断
```markdown
| 问题症状 | 可能原因 | 快速检查命令 | 解决方案 |
|---------|---------|-------------|---------|
| AI不使用中文 | 配置未生效 | `cat .augment-guidelines \| grep "中文"` | 检查配置文件位置和内容 |
| 任务不创建issues | 流程配置错误 | `grep -n "issues" .augment-guidelines` | 检查任务流程配置 |
| MCP工具不调用 | 工具配置问题 | `grep -A5 "MCP" .augment-guidelines` | 验证MCP工具列表 |
| 配置重启丢失 | 文件权限问题 | `ls -la .augment-guidelines` | 检查文件权限和位置 |
```

#### C.2 性能优化检查
```bash
# 配置文件大小检查
du -h .augment-guidelines                   # 查看文件大小
if [ $(wc -c < .augment-guidelines) -gt 10240 ]; then
    echo "⚠️  配置文件过大，建议精简"
fi

# 配置复杂度检查
line_count=$(wc -l < .augment-guidelines)
if [ $line_count -gt 200 ]; then
    echo "⚠️  配置文件过长，建议模块化"
fi
```

### D. 相关资源链接

#### D.1 官方文档
- [Augment Agent 官方文档](https://docs.augmentcode.com/)
- [Augment Guidelines 配置指南](https://docs.augmentcode.com/setup-augment/guidelines)
- [MCP工具生态系统](https://modelcontextprotocol.io/)

#### D.2 项目内相关文档
- [MCP完整配置与使用报告](./MCP完整配置与使用报告-20250622.md)
- [Augment Agent工作偏好设置](../docs/Augment Agent工作偏好设置.md)
- [寸止MCP工具安装配置复盘](./寸止MCP工具安装配置复盘-20250701.md)

#### D.3 社区资源
- [Augment Community Forum](https://community.augmentcode.com/)
- [MCP Tools Registry](https://github.com/modelcontextprotocol/servers)
- [Best Practices Collection](https://github.com/augmentcode/best-practices)

---

## 🎯 总结

### 核心要点回顾

1. **配置优先级**：项目级别 `.augment-guidelines` > 全局 User Guidelines > 系统默认
2. **自动加载**：配置文件会在每次会话开始时自动读取，无需手动激活
3. **最佳实践**：使用结构化、具体化的配置描述，定期测试验证
4. **故障排除**：遇到问题时优先检查文件位置、格式和权限
5. **团队协作**：建立配置标准和版本控制机制

### 下一步行动建议

1. **立即行动**：
   - ✅ 验证当前 `.augment-guidelines` 配置是否生效
   - ✅ 创建配置备份文件
   - ✅ 测试关键功能（中文交流、任务流程、MCP工具）

2. **短期优化**：
   - 📝 根据使用经验调整配置内容
   - 🔧 建立配置版本控制流程
   - 📊 收集配置效果反馈数据

3. **长期规划**：
   - 🚀 建立多项目配置管理体系
   - 👥 制定团队配置协作标准
   - 🔄 持续优化和更新配置策略

---

*📝 备注：此文档为 Augment Agent 的完整配置指南，建议定期更新以反映最新的使用经验和最佳实践。*

*🔄 更新日志：*
- *v1.0 (2025-07-05) - 初始版本，整合Rules和User Guidelines配置方法，包含完整的配置、应用、故障排除和进阶管理内容*
