# Augment Agent Rules与任务管理完整指南

## 📋 概述

本文档详细介绍了Augment Agent的工作规则、任务管理流程以及相关工具的使用方法，帮助用户建立高效的AI辅助编程工作流。

## 🎯 核心理念

### 设计原则
- **结构化管理**：通过标准化的文档模板管理任务
- **过程透明**：关键节点提供用户反馈
- **经验积累**：通过复盘总结提升效率
- **工具优先**：充分利用MCP服务提升能力

### 适用场景
- 复杂的编程任务
- 多步骤的系统配置
- 需要文档记录的项目
- 团队协作的开发工作

## 📊 任务分类体系

### 复杂任务（需要创建issues文档）
**判断标准**：
- 预计步骤数 > 3个主要步骤
- 涉及多个技术栈或工具
- 需要分阶段验证的任务
- 可能遇到多个问题点的任务

**示例**：
- MCP服务器配置
- 完整系统搭建
- 多组件集成项目
- 复杂功能开发

### 简单任务（直接执行）
**判断标准**：
- 单一步骤或简单操作
- 标准化的重复性任务
- 明确的单一目标任务

**示例**：
- 单个文件修改
- 简单查询操作
- 基础配置调整
- 文档格式化

## 🔄 三阶段工作流程

### 阶段1：任务分析与计划制定

#### 1.1 任务识别
```
IF 任务复杂度 > 3个主要步骤 THEN
    创建issues文档
ELSE
    直接执行
END IF
```

#### 1.2 任务命名规范
**格式**：`核心功能-YYYYMMDD`

**示例**：
- `MCP配置-20241201`
- `Obsidian插件开发-20241202`
- `数据库迁移-20241203`

#### 1.3 计划文档创建
- 使用标准模板：`Templates/Issues任务计划模板.md`
- 存储位置：`./issues/任务名.md`
- 包含：背景、目标、计划、风险预警

### 阶段2：任务执行与反馈

#### 2.1 严格按计划执行
- 按照issues文档中的步骤顺序执行
- 每完成一个步骤更新执行状态
- 记录重要的执行信息和遇到的问题

#### 2.2 关键节点反馈
使用`interactive-feedback`的时机：
- ✅ 完成计划制定后
- ✅ 每个主要步骤完成后
- ✅ 遇到问题需要用户确认时
- ✅ 任务完成时

### 阶段3：任务复盘与总结

#### 3.1 复盘文档创建
- 使用标准模板：`Templates/Rewind任务复盘模板.md`
- 存储位置：`./rewind/任务名.md`
- 及时记录，避免遗忘关键信息

#### 3.2 复盘内容要点
- 🔍 问题分析：详细记录遇到的问题和解决过程
- ✅ 解决方案：总结最终采用的方案
- 📊 方案对比：如有多个方案，进行对比分析
- 🎯 成功因素：分析成功的关键因素
- 📝 经验总结：提炼可复用的经验
- 🔮 优化建议：提出后续改进方向

## 🛠️ MCP服务工具箱

### 核心服务
| 服务名称 | 功能描述 | 使用场景 |
|---------|---------|---------|
| `interactive_feedback` | 用户反馈交互 | 关键节点确认、问题咨询 |
| `Context7` | 查询最新库文档/示例 | 技术文档查询、API参考 |
| `sequential-thinking` | 复杂任务分解与思考 | 复杂问题分析、方案设计 |
| `Playwright` | 浏览器自动化操作 | 网页测试、数据抓取 |
| `together-image-gen` | 图像生成 | 免费图像创建 |
| `replicate-flux-mcp` | 高质量图像生成 | 付费高质量图像 |

### 使用原则
1. **优先使用MCP服务**：充分利用可用工具
2. **合理选择服务**：根据任务需求选择合适工具
3. **组合使用**：多个服务配合解决复杂问题

## 📝 文档标准规范

### 通用规范
- **语言**：使用中文文档
- **格式**：遵循Markdown标准
- **结构**：使用emoji图标增强可读性
- **一致性**：与现有知识管理系统保持一致

### Issues文档结构
```markdown
# [任务名称]
## 📋 任务背景
## 🎯 任务目标
## 📊 任务分析
## 📋 详细计划
## 🔄 执行状态
## 📝 执行记录
## ⚠️ 风险预警
```

### Rewind文档结构
```markdown
# [任务名称] 复盘
## 📋 任务目标
## 🔍 遇到的问题及解决过程
## ✅ 最终解决方案
## 🚀 完整执行步骤
## 📊 方案对比
## 🎯 关键成功因素
## 📝 经验总结
## 🎉 最终成果
## 🔮 后续优化建议
```

## 🎯 实践指南

### 开始新任务时
1. **评估复杂度**：判断是否需要创建issues文档
2. **生成任务名**：使用标准命名格式
3. **创建计划**：使用标准模板制定详细计划
4. **确认计划**：使用interactive-feedback与用户确认

### 执行过程中
1. **按计划执行**：严格按照既定步骤进行
2. **及时反馈**：在关键节点主动反馈
3. **记录问题**：详细记录遇到的问题和解决方法
4. **更新状态**：及时更新执行状态

### 任务完成后
1. **创建复盘**：使用标准模板创建复盘文档
2. **总结经验**：提炼可复用的经验和教训
3. **提出建议**：为后续类似任务提供优化建议
4. **确认完成**：使用interactive-feedback确认任务完成

## 📋 检查清单

### 任务开始前 ✅
- [ ] 分析任务复杂度
- [ ] 确定是否需要创建issues文档
- [ ] 生成合适的任务名称
- [ ] 使用标准模板创建计划文档
- [ ] 与用户确认计划

### 执行过程中 ✅
- [ ] 严格按照计划执行
- [ ] 在关键节点使用interactive-feedback
- [ ] 记录重要的执行信息
- [ ] 及时更新执行状态
- [ ] 遇到问题及时记录和解决

### 任务完成后 ✅
- [ ] 创建复盘文档
- [ ] 总结经验和教训
- [ ] 提出后续优化建议
- [ ] 使用interactive-feedback确认完成
- [ ] 归档相关文档

## 🔧 常见问题解答

### Q1：什么情况下不需要创建issues文档？
**A**：简单任务（≤3个步骤）、重复性操作、单一目标任务可以直接执行。

### Q2：如何判断任务的复杂度？
**A**：考虑步骤数量、涉及技术栈、验证需求、潜在问题点等因素。

### Q3：interactive-feedback使用频率如何控制？
**A**：在计划确认、主要步骤完成、遇到问题、任务完成这四个关键节点使用。

### Q4：复盘文档什么时候创建？
**A**：任务完成后立即创建，趁记忆清晰时记录详细信息。

## 🚀 进阶技巧

### 1. 任务模板化
- 为常见任务类型创建专用模板
- 建立任务模式库，提高效率
- 定期更新和优化模板

### 2. 经验知识库
- 将复盘文档整理成知识库
- 建立问题解决方案索引
- 形成团队共享的最佳实践

### 3. 工具链优化
- 熟练掌握各种MCP服务
- 建立工具使用的最佳实践
- 探索新工具和服务的集成

## 📚 实际案例分析

### 案例1：MCP服务配置任务
**任务描述**：配置FLUX图像生成MCP服务器

**复杂度评估**：
- 步骤数：6个主要步骤
- 涉及技术：Node.js、API配置、JSON配置
- 潜在问题：包名错误、API付费、配置路径

**执行流程**：
1. 创建issues文档：`FLUX图像生成MCP配置-20241201.md`
2. 按计划执行：环境检查→API获取→配置→测试
3. 关键节点反馈：计划确认、遇到付费问题、找到免费方案、配置完成
4. 创建复盘：记录问题解决过程、方案对比、经验总结

**关键收获**：
- 多方案对比的重要性
- 免费服务优先原则
- 完整测试验证的必要性

### 案例2：简单文档修改任务
**任务描述**：修改README文件格式

**复杂度评估**：
- 步骤数：1个主要步骤
- 涉及技术：Markdown
- 潜在问题：无

**执行流程**：
1. 直接执行：不创建issues文档
2. 完成后反馈：确认修改结果
3. 无需复盘：简单任务直接完成

## 🔄 工作流程图

```mermaid
graph TD
    A[接收任务] --> B{评估复杂度}
    B -->|>3步骤| C[创建Issues文档]
    B -->|≤3步骤| D[直接执行]

    C --> E[制定详细计划]
    E --> F[用户确认计划]
    F --> G[按计划执行]

    D --> H[简单执行]

    G --> I{步骤完成?}
    I -->|否| J[继续执行]
    I -->|是| K[关键节点反馈]
    J --> I
    K --> L{任务完成?}
    L -->|否| I
    L -->|是| M[创建复盘文档]

    H --> N[完成反馈]

    M --> O[任务结束]
    N --> O
```

## 📖 模板使用指南

### Issues模板使用步骤
1. **复制模板**：从`Templates/Issues任务计划模板.md`复制
2. **填写信息**：根据实际任务填写各个部分
3. **保存文档**：保存到`./issues/任务名.md`
4. **执行跟踪**：执行过程中更新状态

### Rewind模板使用步骤
1. **复制模板**：从`Templates/Rewind任务复盘模板.md`复制
2. **记录问题**：详细记录遇到的问题和解决过程
3. **总结经验**：提炼可复用的经验和教训
4. **保存文档**：保存到`./rewind/任务名.md`

## 🎨 文档美化技巧

### Emoji使用规范
- 📋 任务相关
- 🎯 目标相关
- 📊 分析相关
- 🔄 流程相关
- ✅ 完成相关
- ⚠️ 警告相关
- 🛠️ 工具相关
- 📝 记录相关

### Markdown格式技巧
```markdown
# 一级标题（任务名称）
## 二级标题（主要部分）
### 三级标题（子部分）

**粗体**：重要信息
*斜体*：强调信息
`代码`：命令或代码片段

> 引用：重要提示

- [ ] 待办事项
- [x] 已完成事项

| 表格 | 对比 | 分析 |
|------|------|------|
| 内容 | 内容 | 内容 |
```

## 🔧 故障排除指南

### 常见问题及解决方案

#### 问题1：interactive-feedback无响应
**症状**：调用interactive-feedback后无反应
**原因**：MCP服务未正确配置
**解决**：检查claude_desktop_config.json配置

#### 问题2：文档保存失败
**症状**：无法保存到指定目录
**原因**：路径不存在或权限不足
**解决**：检查目录结构，确保路径正确

#### 问题3：任务计划不够详细
**症状**：执行过程中频繁遇到未预料的问题
**原因**：计划阶段分析不够充分
**解决**：使用sequential-thinking工具深入分析

## 📈 效率提升建议

### 1. 建立个人模板库
- 根据常见任务类型创建专用模板
- 定期更新和优化模板内容
- 建立模板使用的最佳实践

### 2. 构建知识库
- 将复盘文档整理成可搜索的知识库
- 建立问题-解决方案索引
- 定期回顾和更新知识库内容

### 3. 优化工具使用
- 熟练掌握各种MCP服务的特点
- 建立工具选择的决策树
- 探索工具组合使用的可能性

### 4. 团队协作优化
- 统一团队的文档标准
- 建立共享的模板库
- 定期进行经验分享和总结

## 🎓 学习路径建议

### 初级阶段
1. 熟悉基本的Rules和流程
2. 练习使用标准模板
3. 掌握interactive-feedback的使用

### 中级阶段
1. 学习复杂任务的分解方法
2. 掌握各种MCP服务的使用
3. 建立个人的最佳实践

### 高级阶段
1. 优化个人工作流程
2. 建立知识管理体系
3. 指导他人使用系统

## 📞 支持与反馈

### 获取帮助
- 查阅本文档的相关章节
- 使用interactive-feedback寻求实时帮助
- 参考issues和rewind文件夹中的历史案例

### 提供反馈
- 通过interactive-feedback提供使用反馈
- 记录使用过程中的问题和建议
- 参与文档和模板的持续改进

---

*文档版本：v1.0*
*创建时间：2024-12-01*
*最后更新：2024-12-01*
*适用范围：Augment Agent用户*
*维护者：Augment Agent*
