# Replicate FLUX MCP 配置指南

## ✅ 当前状态

- **包验证**: `replicate-flux-mcp@0.1.2` 安装成功 ✅
- **配置文件**: 已更新为正确的包名 ✅
- **需要**: Replicate API Token

## 🔑 获取 Replicate API Token

### 步骤 1: 注册 Replicate 账户
1. 访问 [Replicate.com](https://replicate.com/)
2. 点击 "Sign up" 注册账户
3. 使用邮箱完成注册和验证

### 步骤 2: 获取 API Token
1. 登录 Replicate 账户
2. 点击右上角用户头像
3. 选择 "Account" 或 "Settings"
4. 找到 "API Tokens" 部分
5. 点击 "Create token" 或 "New token"
6. 复制生成的 API Token（格式：`r8_xxxxxxxxxx`）

## 📝 当前配置文件

```json
{
  "mcpServers": {
    "replicate-flux-mcp": {
      "command": "npx",
      "args": [
        "-y",
        "replicate-flux-mcp"
      ],
      "env": {
        "REPLICATE_API_TOKEN": "your_replicate_token_here"
      },
      "timeout": 600
    }
  }
}
```

## 🔧 完成配置

获取到 Replicate API Token 后：

1. **替换 Token**: 将配置中的 `your_replicate_token_here` 替换为您的实际 Token
2. **保存配置**: 保存 `claude_desktop_config.json` 文件
3. **重启 Augment**: 重新启动以加载新配置

## 🎨 可用功能

### 基础图像生成
```
请生成一张美丽的风景照片
```

### 高级功能
- **批量生成**: 一次生成多张不同的图片
- **变体生成**: 同一主题的不同风格变化
- **SVG 生成**: 矢量图形生成
- **参数控制**: 尺寸、质量、风格等

### 示例命令
```
生成一张现代办公室的照片，16:9比例
创建一个简约风格的 logo SVG
生成 3 张不同风格的猫咪图片
```

## 🔍 功能特点

- **高质量图像**: 使用 FLUX Schnell 模型
- **SVG 支持**: Recraft V3 SVG 模型
- **批量处理**: 一次生成多张图片
- **历史管理**: 查看生成历史
- **参数控制**: 精细调节生成参数

## 🚀 下一步

1. 获取 Replicate API Token
2. 更新配置文件
3. 重启 Augment
4. 开始生成图像！

## 💡 提示

- Replicate 是按使用量计费的服务
- 建议先小量测试，了解费用
- 生成的图像会保存在 Replicate 账户中
- 支持多种图像格式和尺寸

## 🆘 如需帮助

获取到 API Token 后，请告诉我，我将帮您完成最后的配置步骤！
