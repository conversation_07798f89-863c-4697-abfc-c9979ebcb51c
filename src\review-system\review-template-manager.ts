/**
 * 智能复盘系统 - 复盘模板管理器
 * 
 * 负责管理复盘模板的生成、配置和历史记录
 */

import {
  ReviewType,
  TemplateConfig,
  TemplateHistory,
  DateRange,
  Period,
  DailyData,
  TemplateGenerationOptions,
  SystemError,
  ErrorType,
  CustomField
} from './types.js';

/**
 * 复盘模板管理器类
 * 
 * 提供复盘模板的生成、配置管理和历史追踪功能
 */
export class ReviewTemplateManager {
  private configCache: Map<ReviewType, TemplateConfig> = new Map();
  private templateCache: Map<string, string> = new Map();
  private readonly configBasePath: string;
  private readonly templateBasePath: string;

  constructor(
    configBasePath: string = 'obsidian-vault/复盘系统/配置',
    templateBasePath: string = 'obsidian-vault/Templates/复盘模板'
  ) {
    this.configBasePath = configBasePath;
    this.templateBasePath = templateBasePath;
    this.initializeDefaultConfigs();
  }

  /**
   * 生成复盘模板
   * 
   * @param type 复盘类型
   * @param date 目标日期
   * @param config 模板配置
   * @param options 生成选项
   * @returns 生成的模板内容
   */
  async generateTemplate(
    type: ReviewType,
    date: Date,
    config?: TemplateConfig,
    options?: TemplateGenerationOptions
  ): Promise<string> {
    try {
      // 获取配置
      const templateConfig = config || await this.getTemplateConfig(type);
      const genOptions = this.mergeDefaultOptions(options);

      // 生成基础模板结构
      const templateContent = await this.buildTemplateContent(
        type,
        date,
        templateConfig,
        genOptions
      );

      // 如果启用自动填充，添加数据
      if (templateConfig.autoFillData) {
        return await this.fillTemplateWithData(templateContent, type, date);
      }

      return templateContent;
    } catch (error) {
      throw this.createSystemError(
        ErrorType.SYSTEM_ERROR,
        `Failed to generate ${type} template: ${error.message}`,
        'generateTemplate'
      );
    }
  }

  /**
   * 更新模板配置
   * 
   * @param type 复盘类型
   * @param config 部分配置更新
   */
  async updateTemplateConfig(
    type: ReviewType,
    config: Partial<TemplateConfig>
  ): Promise<void> {
    try {
      const currentConfig = await this.getTemplateConfig(type);
      const updatedConfig = { ...currentConfig, ...config };

      // 验证配置
      this.validateTemplateConfig(updatedConfig);

      // 保存配置
      await this.saveTemplateConfig(type, updatedConfig);

      // 更新缓存
      this.configCache.set(type, updatedConfig);

      console.log(`Template config updated for ${type}`);
    } catch (error) {
      throw this.createSystemError(
        ErrorType.VALIDATION_ERROR,
        `Failed to update template config: ${error.message}`,
        'updateTemplateConfig'
      );
    }
  }

  /**
   * 获取模板历史记录
   * 
   * @param type 复盘类型
   * @param dateRange 日期范围
   * @returns 模板历史记录数组
   */
  async getTemplateHistory(
    type: ReviewType,
    dateRange: DateRange
  ): Promise<TemplateHistory[]> {
    try {
      // 这里应该从实际的存储中读取历史记录
      // 目前返回模拟数据
      const history: TemplateHistory[] = [];
      
      // TODO: 实现从文件系统或数据库读取历史记录的逻辑
      
      return history.filter(record => 
        record.date >= dateRange.start && record.date <= dateRange.end
      );
    } catch (error) {
      throw this.createSystemError(
        ErrorType.SYSTEM_ERROR,
        `Failed to get template history: ${error.message}`,
        'getTemplateHistory'
      );
    }
  }

  /**
   * 获取模板配置
   * 
   * @param type 复盘类型
   * @returns 模板配置
   */
  async getTemplateConfig(type: ReviewType): Promise<TemplateConfig> {
    // 先检查缓存
    if (this.configCache.has(type)) {
      return this.configCache.get(type)!;
    }

    try {
      // 从文件系统加载配置
      const config = await this.loadTemplateConfig(type);
      this.configCache.set(type, config);
      return config;
    } catch (error) {
      // 如果加载失败，返回默认配置
      console.warn(`Failed to load config for ${type}, using default`);
      return this.getDefaultTemplateConfig(type);
    }
  }

  /**
   * 构建模板内容
   * 
   * @private
   */
  private async buildTemplateContent(
    type: ReviewType,
    date: Date,
    config: TemplateConfig,
    options: TemplateGenerationOptions
  ): Promise<string> {
    const period = this.calculatePeriod(type, date);
    
    let content = this.generateYamlFrontMatter(type, date, period);
    content += this.generateTitle(type, period);
    content += this.generateDimensions(config.dimensions);
    
    if (options.includeCustomFields) {
      content += this.generateCustomFields(config.customFields);
    }
    
    if (options.includeDataview) {
      content += this.generateDataviewQueries(type, period);
    }
    
    return content;
  }

  /**
   * 生成YAML前置元数据
   * 
   * @private
   */
  private generateYamlFrontMatter(
    type: ReviewType,
    date: Date,
    period: Period
  ): string {
    const yaml = [
      '---',
      `review_type: ${type}`,
      `review_date: ${date.toISOString().split('T')[0]}`,
      `period_start: ${period.start.toISOString().split('T')[0]}`,
      `period_end: ${period.end.toISOString().split('T')[0]}`,
      `created: ${new Date().toISOString()}`,
      'auto_generated: true',
      'analysis_version: "1.0"',
      '---',
      ''
    ];
    
    return yaml.join('\n');
  }

  /**
   * 生成标题
   * 
   * @private
   */
  private generateTitle(type: ReviewType, period: Period): string {
    const typeNames = {
      [ReviewType.DAILY]: '日复盘',
      [ReviewType.WEEKLY]: '周复盘',
      [ReviewType.MONTHLY]: '月复盘',
      [ReviewType.YEARLY]: '年复盘'
    };
    
    return `# ${period.label} ${typeNames[type]}\n\n`;
  }

  /**
   * 生成复盘维度内容
   * 
   * @private
   */
  private generateDimensions(dimensions: string[]): string {
    let content = '';
    
    for (const dimension of dimensions) {
      content += `## ${dimension}\n\n`;
      content += '<!-- 请在此处填写相关内容 -->\n\n';
    }
    
    return content;
  }

  /**
   * 生成自定义字段
   * 
   * @private
   */
  private generateCustomFields(customFields: CustomField[]): string {
    if (customFields.length === 0) return '';
    
    let content = '## 自定义字段\n\n';
    
    for (const field of customFields) {
      content += `### ${field.name}\n`;
      if (field.description) {
        content += `*${field.description}*\n\n`;
      }
      
      switch (field.type) {
        case 'select':
        case 'multiselect':
          if (field.options) {
            content += field.options.map(option => `- [ ] ${option}`).join('\n') + '\n\n';
          }
          break;
        case 'boolean':
          content += '- [ ] 是\n- [ ] 否\n\n';
          break;
        default:
          content += '<!-- 请填写内容 -->\n\n';
      }
    }
    
    return content;
  }

  /**
   * 生成Dataview查询
   * 
   * @private
   */
  private generateDataviewQueries(type: ReviewType, period: Period): string {
    let content = '## 数据统计\n\n';
    
    switch (type) {
      case ReviewType.DAILY:
        content += this.generateDailyDataviewQueries(period);
        break;
      case ReviewType.WEEKLY:
        content += this.generateWeeklyDataviewQueries(period);
        break;
      case ReviewType.MONTHLY:
        content += this.generateMonthlyDataviewQueries(period);
        break;
      case ReviewType.YEARLY:
        content += this.generateYearlyDataviewQueries(period);
        break;
    }
    
    return content;
  }

  /**
   * 生成日复盘的Dataview查询
   * 
   * @private
   */
  private generateDailyDataviewQueries(period: Period): string {
    const dateStr = period.start.toISOString().split('T')[0];
    
    return `
### 今日任务完成情况
\`\`\`tasks
done on ${dateStr}
sort by done reverse
short
\`\`\`

### 今日创建的文件
\`\`\`dataview
TABLE without id file.ctime AS Time, file.link AS Name, file.folder AS Folder
WHERE file.ctime >= date("${dateStr}") AND file.ctime < date("${dateStr}") + dur(1 day)
SORT file.ctime DESC
\`\`\`

`;
  }

  // 其他私有方法的实现将在后续添加...
  
  /**
   * 初始化默认配置
   * 
   * @private
   */
  private initializeDefaultConfigs(): void {
    for (const type of Object.values(ReviewType)) {
      this.configCache.set(type, this.getDefaultTemplateConfig(type));
    }
  }

  /**
   * 获取默认模板配置
   * 
   * @private
   */
  private getDefaultTemplateConfig(type: ReviewType): TemplateConfig {
    const baseDimensions = ['今日成就', '遇到挑战', '学习收获', '反思总结'];
    
    return {
      dimensions: baseDimensions,
      customFields: [],
      autoFillData: true,
      reminderSettings: {
        enabled: true,
        frequency: type === ReviewType.DAILY ? 'daily' : 'weekly',
        time: '21:00'
      }
    };
  }

  /**
   * 计算周期信息
   * 
   * @private
   */
  private calculatePeriod(type: ReviewType, date: Date): Period {
    // 简化实现，实际应该根据类型计算准确的周期
    return {
      type,
      start: date,
      end: date,
      label: date.toISOString().split('T')[0]
    };
  }

  /**
   * 创建系统错误
   * 
   * @private
   */
  private createSystemError(
    type: ErrorType,
    message: string,
    operation: string
  ): SystemError {
    return {
      type,
      message,
      severity: 'medium',
      context: {
        operation,
        component: 'ReviewTemplateManager'
      },
      timestamp: new Date(),
      recoverable: true
    };
  }

  /**
   * 合并默认选项
   * 
   * @private
   */
  private mergeDefaultOptions(options?: TemplateGenerationOptions): TemplateGenerationOptions {
    return {
      includeDataview: true,
      includeCustomFields: true,
      autoFillData: true,
      templateStyle: 'standard',
      ...options
    };
  }

  /**
   * 验证模板配置
   * 
   * @private
   */
  private validateTemplateConfig(config: TemplateConfig): void {
    if (!config.dimensions || config.dimensions.length === 0) {
      throw new Error('Template config must have at least one dimension');
    }
    
    if (!config.reminderSettings) {
      throw new Error('Template config must have reminder settings');
    }
  }

  // 占位符方法，将在后续实现
  private async loadTemplateConfig(type: ReviewType): Promise<TemplateConfig> {
    throw new Error('Not implemented yet');
  }

  private async saveTemplateConfig(type: ReviewType, config: TemplateConfig): Promise<void> {
    throw new Error('Not implemented yet');
  }

  private async fillTemplateWithData(content: string, type: ReviewType, date: Date): Promise<string> {
    // 占位符实现
    return content;
  }

  private generateWeeklyDataviewQueries(period: Period): string {
    return '<!-- Weekly queries to be implemented -->\n';
  }

  private generateMonthlyDataviewQueries(period: Period): string {
    return '<!-- Monthly queries to be implemented -->\n';
  }

  private generateYearlyDataviewQueries(period: Period): string {
    return '<!-- Yearly queries to be implemented -->\n';
  }
}
