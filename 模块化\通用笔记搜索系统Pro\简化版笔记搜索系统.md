---
tags:
  - type/dashboard
  - dashboard/notes-search
  - system/knowledge-management
created: 2025-07-11T11:00
updated: 2025-07-11T11:00
---

# 🔍 简化版笔记搜索系统

## 📝 基础搜索功能

```dataviewjs
// ===== 简化版笔记搜索系统 =====
const container = this.container;

// 创建主容器
const mainDiv = document.createElement('div');
mainDiv.style.cssText = `
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
`;

// 搜索区域
const searchSection = document.createElement('div');
searchSection.style.cssText = `
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
`;

// 创建标题
const title = document.createElement('h3');
title.textContent = '🔍 笔记搜索';
title.style.cssText = 'margin: 0 0 15px 0; color: #495057;';

// 创建输入容器
const inputContainer = document.createElement('div');
inputContainer.style.cssText = 'display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;';

// 创建搜索输入框
const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.id = 'search-input';
searchInput.placeholder = '输入搜索关键词...';
searchInput.style.cssText = 'flex: 1; min-width: 300px; padding: 10px; border: 1px solid #ddd; border-radius: 4px;';

// 创建模式选择器
const modeSelect = document.createElement('select');
modeSelect.id = 'search-mode';
modeSelect.style.cssText = 'padding: 10px; border: 1px solid #ddd; border-radius: 4px;';

const modes = [
    { value: 'AND', text: 'AND (所有)' },
    { value: 'OR', text: 'OR (任一)' },
    { value: 'EXACT', text: '精确匹配' }
];

modes.forEach(mode => {
    const option = document.createElement('option');
    option.value = mode.value;
    option.textContent = mode.text;
    modeSelect.appendChild(option);
});

inputContainer.appendChild(searchInput);
inputContainer.appendChild(modeSelect);

// 创建按钮容器
const buttonContainer = document.createElement('div');
buttonContainer.style.cssText = 'text-align: center;';

// 创建搜索按钮
const searchBtn = document.createElement('button');
searchBtn.id = 'search-btn';
searchBtn.textContent = '🔍 开始搜索';
searchBtn.style.cssText = 'background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer; margin-right: 10px;';

// 创建清空按钮
const clearBtn = document.createElement('button');
clearBtn.id = 'clear-btn';
clearBtn.textContent = '🗑️ 清空';
clearBtn.style.cssText = 'background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 6px; cursor: pointer;';

buttonContainer.appendChild(searchBtn);
buttonContainer.appendChild(clearBtn);

searchSection.appendChild(title);
searchSection.appendChild(inputContainer);
searchSection.appendChild(buttonContainer);

// 结果区域
const resultSection = document.createElement('div');
resultSection.innerHTML = `
    <h3 style="margin: 0 0 15px 0; color: #495057;">📋 搜索结果</h3>
    <div id="search-results" style="min-height: 200px; border: 1px solid #e9ecef; border-radius: 6px; padding: 20px; background: white;">
        <div style="text-align: center; color: #6c757d; padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 10px;">🔍</div>
            <div style="font-size: 18px;">准备开始搜索</div>
        </div>
    </div>
`;

mainDiv.appendChild(searchSection);
mainDiv.appendChild(resultSection);
container.appendChild(mainDiv);

// ===== 搜索功能实现 =====
async function performSearch() {
    console.log('开始搜索...');
    
    const searchInput = document.getElementById('search-input');
    const searchMode = document.getElementById('search-mode');
    const resultsDiv = document.getElementById('search-results');
    
    if (!searchInput || !searchMode || !resultsDiv) {
        console.error('找不到必要的元素');
        return;
    }
    
    const keywords = searchInput.value.trim().split(/\s+/).filter(k => k);
    
    if (keywords.length === 0) {
        alert('请输入搜索关键词');
        return;
    }
    
    // 显示加载状态
    resultsDiv.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #6c757d;">
            <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
            <div>正在搜索中...</div>
        </div>
    `;
    
    try {
        // 获取 notes 目录下的所有页面
        const allPages = dv.pages().where(p => p.file.path.includes('notes/'));
        const results = [];
        
        for (const page of allPages) {
            try {
                const content = await dv.io.load(page.file.path);
                const fileName = page.file.name.replace('.md', '');

                let matched = false;
                let matchType = '';
                let snippet = '';

                // 检查文件名匹配
                if (checkMatch(fileName, keywords, searchMode.value)) {
                    matched = true;
                    matchType = '文件名';
                    snippet = fileName;
                }

                // 检查内容匹配
                if (!matched && checkMatch(content, keywords, searchMode.value)) {
                    matched = true;
                    matchType = '文件内容';
                    snippet = extractSnippet(content, keywords);
                }

                if (matched) {
                    results.push({
                        name: fileName,
                        link: page.file.link,
                        matchType: matchType,
                        snippet: snippet,
                        mtime: page.file.mtime
                    });
                }
            } catch (error) {
                // 忽略读取失败的文件
            }
        }
        
        displayResults(results, keywords);
        
    } catch (error) {
        console.error('搜索失败:', error);
        resultsDiv.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div>搜索失败: ${error.message}</div>
            </div>
        `;
    }
}

function checkMatch(text, keywords, mode) {
    if (!text || !keywords || keywords.length === 0) {
        return false;
    }

    const lowerText = text.toLowerCase();
    const lowerKeywords = keywords.map(k => k.toLowerCase()).filter(k => k.length > 0);

    if (lowerKeywords.length === 0) {
        return false;
    }

    switch (mode) {
        case 'AND':
            return lowerKeywords.every(keyword => lowerText.includes(keyword));
        case 'OR':
            return lowerKeywords.some(keyword => lowerText.includes(keyword));
        case 'EXACT':
            const exactPhrase = keywords.join(' ').toLowerCase();
            return lowerText.includes(exactPhrase);
        default:
            return lowerKeywords.some(keyword => lowerText.includes(keyword));
    }
}

function extractSnippet(content, keywords, maxLength = 150) {
    const lines = content.split('\n');
    const lowerKeywords = keywords.map(k => k.toLowerCase());
    
    for (const line of lines) {
        const lowerLine = line.toLowerCase();
        if (lowerKeywords.some(keyword => lowerLine.includes(keyword))) {
            if (line.length <= maxLength) {
                return line.trim();
            } else {
                return line.substring(0, maxLength).trim() + '...';
            }
        }
    }
    
    return content.substring(0, maxLength).trim() + '...';
}

function displayResults(results, keywords) {
    const resultsDiv = document.getElementById('search-results');
    
    if (results.length === 0) {
        resultsDiv.innerHTML = `
            <div style="text-align: center; color: #6c757d; padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">未找到匹配的笔记</div>
                <div style="font-size: 14px;">尝试使用不同的关键词或搜索模式</div>
            </div>
        `;
        return;
    }
    
    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: #e3f2fd; border-radius: 6px; border-left: 4px solid #2196f3;">
            <strong>🎯 找到 ${results.length} 个匹配结果</strong>
            <span style="margin-left: 10px; color: #666;">关键词: ${keywords.join(', ')}</span>
        </div>
    `;
    
    results.forEach(result => {
        const modifiedDate = new Date(result.mtime).toLocaleDateString('zh-CN');
        
        html += `
            <div style="border: 1px solid #e9ecef; border-radius: 8px; margin-bottom: 15px; padding: 15px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                <h4 style="margin: 0 0 8px 0; color: #2c3e50;">
                    <a href="${result.link}" style="text-decoration: none; color: #007bff; font-weight: bold;">
                        📄 ${result.name}
                    </a>
                </h4>
                <div style="font-size: 12px; color: #6c757d; margin-bottom: 8px;">
                    <span style="margin-right: 15px;">📅 ${modifiedDate}</span>
                    <span>🔍 匹配: ${result.matchType}</span>
                </div>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; border-left: 3px solid #28a745;">
                    <div style="font-size: 13px; color: #495057; line-height: 1.4;">
                        ${highlightKeywords(result.snippet, keywords)}
                    </div>
                </div>
            </div>
        `;
    });
    
    resultsDiv.innerHTML = html;
}

function highlightKeywords(text, keywords) {
    let highlightedText = text;
    keywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        highlightedText = highlightedText.replace(regex, '<mark style="background: #fff3cd; padding: 1px 2px;">$1</mark>');
    });
    return highlightedText;
}

function clearResults() {
    document.getElementById('search-input').value = '';
    document.getElementById('search-results').innerHTML = `
        <div style="text-align: center; color: #6c757d; padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 10px;">🔍</div>
            <div style="font-size: 18px;">准备开始搜索</div>
        </div>
    `;
}

// ===== 事件监听器 =====
setTimeout(() => {
    const searchBtn = document.getElementById('search-btn');
    const clearBtn = document.getElementById('clear-btn');
    const searchInput = document.getElementById('search-input');
    
    if (searchBtn) {
        searchBtn.addEventListener('click', performSearch);
        console.log('搜索按钮事件已绑定');
    }
    
    if (clearBtn) {
        clearBtn.addEventListener('click', clearResults);
        console.log('清空按钮事件已绑定');
    }
    
    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        console.log('回车键事件已绑定');
    }
    
    console.log('简化版搜索系统初始化完成');
}, 100);
```

## 📖 使用说明

### 🎯 功能特点
1. **简化界面**：去除复杂功能，专注核心搜索
2. **基础搜索**：支持关键词搜索文件名和内容
3. **多种模式**：AND/OR/精确匹配三种搜索模式
4. **直接跳转**：点击文件名直接跳转到原始笔记
5. **调试信息**：在浏览器控制台显示详细调试信息

### 🔍 使用方法
1. 在搜索框中输入关键词
2. 选择搜索模式（AND/OR/精确匹配）
3. 点击"🔍 开始搜索"按钮或按回车键
4. 查看搜索结果，点击文件名跳转

### 🐛 故障排除
如果搜索没有反应：
1. 按 F12 打开浏览器开发者工具
2. 查看 Console 标签页的错误信息
3. 确认 Dataview 插件已启用
4. 检查是否有 JavaScript 错误

---

*💡 这是一个简化版本，专门用于调试和确保基本功能正常工作。*
