---
tags:
  - config
  - review-guide
  - progressive-system
created: 2025-08-07
updated: 2025-08-07
---
# 🎯 渐进式复盘引导系统配置

> 帮助用户从简单开始，逐步建立完整的复盘习惯

## 📊 复盘难度分级系统

### 🌱 入门级 (Level 1) - 新手友好
**适用对象**：刚开始复盘的用户，复盘经验 < 7天
**核心理念**：简单易行，建立习惯
**问题数量**：3个核心问题
**预计时间**：2-3分钟

#### 入门级问题集
```yaml
beginner_questions:
  daily:
    - question: "今天最开心的一件事是什么？"
      emoji: "😊"
      placeholder: "可以是很小的事情，比如喝到好喝的咖啡"
      
    - question: "今天学到了什么新东西？"
      emoji: "💡"
      placeholder: "知识、技能、经验或感悟都可以"
      
    - question: "明天想要做得更好的一件事？"
      emoji: "🎯"
      placeholder: "一个具体的小改进就够了"

  weekly:
    - question: "这周最大的收获是什么？"
      emoji: "🏆"
      
    - question: "这周遇到的最大挑战？"
      emoji: "🚧"
      
    - question: "下周最想完成的一件事？"
      emoji: "🎯"
```

### 🌿 标准级 (Level 2) - 进阶发展
**适用对象**：有一定复盘经验的用户，复盘经验 7-30天
**核心理念**：深入思考，全面回顾
**问题数量**：5个核心问题
**预计时间**：5-8分钟

#### 标准级问题集
```yaml
standard_questions:
  daily:
    - question: "今天最大的成就是什么？"
      emoji: "🎯"
      category: "achievement"
      
    - question: "今天遇到了什么挑战，如何解决的？"
      emoji: "🚧"
      category: "challenge"
      
    - question: "今天有什么新的学习和收获？"
      emoji: "💡"
      category: "learning"
      
    - question: "今天的情绪状态如何，什么影响了你？"
      emoji: "😊"
      category: "emotion"
      
    - question: "明天可以在哪些方面做得更好？"
      emoji: "🔧"
      category: "improvement"
```

### 🌳 深度级 (Level 3) - 专家模式
**适用对象**：复盘习惯稳定的用户，复盘经验 > 30天
**核心理念**：深度反思，系统分析
**问题数量**：完整问题集（当前模板的所有问题）
**预计时间**：10-15分钟

## 🎮 用户经验跟踪系统

### 经验值计算规则
```yaml
experience_rules:
  daily_review_complete: 10  # 完成日复盘
  weekly_review_complete: 50  # 完成周复盘
  monthly_review_complete: 200  # 完成月复盘
  consecutive_days_bonus: 5  # 连续复盘奖励（每天）
  quality_bonus: 20  # 高质量复盘奖励（内容丰富）
  
level_thresholds:
  beginner: 0      # 0-100 经验值
  standard: 100    # 100-500 经验值
  advanced: 500    # 500+ 经验值
```

### 成就系统
```yaml
achievements:
  first_review: 
    name: "初次复盘"
    description: "完成第一次复盘"
    reward: "解锁标准模式"
    
  week_streak:
    name: "坚持一周"
    description: "连续复盘7天"
    reward: "经验值翻倍一天"
    
  month_master:
    name: "复盘达人"
    description: "连续复盘30天"
    reward: "解锁深度模式"
    
  quality_writer:
    name: "深度思考者"
    description: "完成10次高质量复盘"
    reward: "自定义问题权限"
```

## 🚀 自动升级机制

### 升级条件
```yaml
upgrade_conditions:
  to_standard:
    min_experience: 100
    min_reviews: 7
    consecutive_days: 3
    message: "🎉 恭喜！您已经建立了基本的复盘习惯，可以尝试标准模式了！"
    
  to_advanced:
    min_experience: 500
    min_reviews: 30
    consecutive_days: 7
    quality_reviews: 5
    message: "🌟 太棒了！您已经是复盘专家，解锁深度模式！"
```

### 降级保护
```yaml
downgrade_protection:
  enabled: true
  grace_period: 7  # 7天内不活跃不会降级
  gentle_reminder: true  # 发送友好提醒而不是强制降级
```

## 📚 复盘指导和提示

### 新手指导
```yaml
beginner_tips:
  - "💡 复盘不需要完美，诚实记录就好"
  - "⏰ 每天固定时间复盘，比如睡前10分钟"
  - "🎯 从小事开始，比如'今天喝水够了吗'"
  - "📝 用自己的话写，不用太正式"
  - "🌱 坚持比完美更重要"
```

### 进阶指导
```yaml
standard_tips:
  - "🔍 尝试找出事件背后的原因"
  - "📊 可以给自己的表现打分"
  - "🔗 思考今天的事情与长期目标的关系"
  - "💭 记录情绪变化和影响因素"
  - "🎯 设定明天的具体改进目标"
```

### 专家指导
```yaml
advanced_tips:
  - "🧠 进行深层次的自我分析"
  - "📈 寻找行为模式和趋势"
  - "🎭 从不同角度审视同一件事"
  - "🔄 建立反馈循环，持续改进"
  - "📚 将复盘与学习和成长联系起来"
```

## 🏃‍♂️ 复盘习惯养成计划

### 21天习惯养成计划
```yaml
habit_formation_plan:
  phase_1: # 第1-7天：建立基础
    name: "起步阶段"
    goal: "建立复盘意识"
    daily_target: "完成3个简单问题"
    tips: "设置提醒，选择固定时间"
    
  phase_2: # 第8-14天：稳定习惯
    name: "稳定阶段"
    goal: "形成复盘节奏"
    daily_target: "提高复盘质量"
    tips: "尝试更深入的思考"
    
  phase_3: # 第15-21天：深化习惯
    name: "深化阶段"
    goal: "享受复盘过程"
    daily_target: "个性化复盘内容"
    tips: "根据自己的需求调整问题"
```

### 激励机制
```yaml
motivation_system:
  daily_encouragement:
    - "🌟 又完成了一天的成长记录！"
    - "💪 坚持就是胜利，你做得很棒！"
    - "🎯 每一次复盘都是进步的阶梯！"
    
  milestone_rewards:
    day_7: "🏆 一周坚持奖章"
    day_21: "💎 习惯养成大师"
    day_100: "🌟 复盘传奇"
```

## ⚙️ 系统配置

### 默认设置
```yaml
default_settings:
  new_user_level: "beginner"
  auto_upgrade: true
  show_progress: true
  daily_tips: true
  achievement_notifications: true
  
personalization:
  allow_custom_questions: false  # 入门用户不允许
  allow_skip_questions: true     # 允许跳过问题
  show_examples: true           # 显示示例答案
```

---
*配置版本：v1.0 | 最后更新：2025-08-07*
