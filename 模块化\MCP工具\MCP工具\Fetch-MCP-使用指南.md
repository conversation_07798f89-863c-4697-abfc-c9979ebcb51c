# Fetch MCP 使用指南

## 安装状态确认

✅ **fetch MCP已成功安装并配置**

- 使用uvx方式：`uvx mcp-server-fetch`
- 配置文件已更新：`Augment-包含Fetch-MCP配置.json`
- MCP检查器测试通过

## 功能说明

fetch MCP提供以下核心功能：

### 1. 网页内容获取
- 获取任何可访问的网页内容
- 自动转换HTML为markdown格式
- 支持分块读取长内容

### 2. 可用工具

#### fetch工具
```
fetch(url, max_length=5000, start_index=0, raw=false)
```

**参数说明：**
- `url` (必需): 要获取的网页URL
- `max_length` (可选): 返回内容的最大字符数，默认5000
- `start_index` (可选): 开始读取的字符位置，默认0
- `raw` (可选): 是否返回原始HTML，默认false（返回markdown）

## 使用示例

### 基础用法

1. **获取网页内容**
   ```
   请帮我获取 https://example.com 的内容
   ```

2. **获取特定长度内容**
   ```
   请获取 https://news.example.com 的前3000字符内容
   ```

3. **分块读取长文章**
   ```
   请从第2000字符开始获取 https://blog.example.com/article 的内容
   ```

### 高级用法

1. **获取原始HTML**
   ```
   请获取 https://example.com 的原始HTML代码
   ```

2. **内容分析**
   ```
   请获取 https://tech.example.com 的内容并总结主要观点
   ```

3. **多页面对比**
   ```
   请分别获取以下两个页面的内容并对比差异：
   - https://site1.com/article
   - https://site2.com/article
   ```

## 配置选项

### 自定义用户代理
如需自定义用户代理，可在配置中添加：
```json
{
  "fetch": {
    "command": "uvx",
    "args": [
      "mcp-server-fetch",
      "--user-agent=MyCustomAgent/1.0"
    ]
  }
}
```

### 忽略robots.txt
如需忽略网站的robots.txt限制：
```json
{
  "fetch": {
    "command": "uvx",
    "args": [
      "mcp-server-fetch",
      "--ignore-robots-txt"
    ]
  }
}
```

### 使用代理
如需通过代理访问：
```json
{
  "fetch": {
    "command": "uvx",
    "args": [
      "mcp-server-fetch",
      "--proxy-url=http://proxy.example.com:8080"
    ]
  }
}
```

## 实际应用场景

### 1. 内容研究
- 获取竞品网站信息
- 收集行业资讯
- 分析市场趋势

### 2. 技术文档
- 获取API文档
- 查看技术博客
- 收集开发资源

### 3. 新闻监控
- 跟踪新闻动态
- 监控特定话题
- 收集媒体报道

### 4. 学术研究
- 获取论文摘要
- 收集研究资料
- 分析学术观点

## 注意事项

### 1. 网络访问
- 确保网络连接正常
- 某些网站可能有访问限制
- 注意防火墙设置

### 2. 内容限制
- 默认最大5000字符
- 可通过参数调整
- 大文件建议分块读取

### 3. 法律合规
- 遵守网站robots.txt
- 尊重版权和使用条款
- 避免过度频繁请求

### 4. 性能优化
- 建议安装node.js以获得更好的HTML处理
- 合理设置max_length避免超时
- 使用start_index进行分页读取

## 故障排除

### 常见问题

1. **连接超时**
   - 检查网络连接
   - 尝试使用代理
   - 确认目标网站可访问

2. **内容为空**
   - 检查URL是否正确
   - 确认网站是否需要登录
   - 尝试使用不同的用户代理

3. **格式问题**
   - 使用raw=true获取原始HTML
   - 检查网站编码格式
   - 尝试调整max_length参数

### 调试方法

1. **使用MCP检查器**
   ```bash
   npx @modelcontextprotocol/inspector uvx mcp-server-fetch
   ```

2. **查看详细日志**
   - 检查Augment的MCP服务器日志
   - 使用--verbose参数（如果支持）

3. **测试连接**
   ```bash
   uvx mcp-server-fetch --help
   ```

## 更新维护

### 检查版本
```bash
uvx mcp-server-fetch --version
```

### 更新到最新版本
```bash
uvx --force mcp-server-fetch
```

### 清理缓存
```bash
uv cache clean
```

## 技术支持

如遇到问题，可以：

1. 查看官方文档：https://github.com/modelcontextprotocol/servers
2. 检查PyPI页面：https://pypi.org/project/mcp-server-fetch/
3. 使用MCP检查器进行调试
4. 查看Augment的MCP配置文档

---

**配置完成！** 您现在可以在Augment中使用fetch MCP来获取网页内容了。
