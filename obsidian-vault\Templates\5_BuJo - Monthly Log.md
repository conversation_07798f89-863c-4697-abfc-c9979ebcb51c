---
tags:
  - type/structure
  - structure/bujo/monthly
start_date: ""
end_date: ""
template: "[[5_BuJo - Monthly Log]]"
created: 2025-03-31T10:00:00
updated: 2025-04-28T13:31
---
# 📅 月度回顾 MONTHLY REVIEW - 2025年3月

## 📊 月度精华内容

```dataviewjs
// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 创建表格来显示精华内容
const rows = [];

for (const page of pages) {
    // 获取文件内容
    const content = await dv.io.load(page.file.path);
    
    // 查找包含 #精华 标签的行
    const lines = content.split("\n");
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes("#精华")) {
            // 提取文件名部分
            const fileName = page.file.name.replace(".md", ""); // 移除.md扩展名
            
            // 提取内容（去除时间戳和标签）
            let highlightContent = lines[i];
            
            // 移除时间戳部分 (start::XX:XX)
            highlightContent = highlightContent.replace(/\(start::\d+:\d+\)/g, "");
            
            // 移除 #精华 标签
            highlightContent = highlightContent.replace(/#精华/g, "");
            
            // 清理多余空格
            highlightContent = highlightContent.trim();
            
            // 添加到结果数组
            rows.push({
                fileName: fileName,
                content: highlightContent,
                link: page.file.link
            });
        }
    }
}

// 显示结果
if (rows.length > 0) {
    dv.table(["日期", "内容"], 
        rows.map(row => [
            `[[${row.fileName}|${row.fileName}]]`, 
            row.content
        ])
    );
} else {
    dv.paragraph("本月暂无精华内容");
}
```

## 📈 月度习惯统计

```dataviewjs
// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 定义要跟踪的习惯
const habits = [
  { key: 'stretch', label: '🧘 晨起拉伸' },
  { key: 'journal', label: '📓 晨间日记' },
  { key: 'notes', label: '💻 整理笔记' },
  { key: 'running', label: '🏃 有氧慢跑' },
  { key: 'reading', label: '📖 睡前阅读' }
];

// 初始化统计数据
const habitStats = {};
habits.forEach(habit => {
    habitStats[habit.key] = {
        completed: 0,
        total: pages.length,
        percentage: 0
    };
});

// 统计每个习惯的完成情况
pages.forEach(page => {
    habits.forEach(habit => {
        if (page[habit.key]) {
            habitStats[habit.key].completed++;
        }
    });
});

// 计算完成百分比
habits.forEach(habit => {
    habitStats[habit.key].percentage = Math.round((habitStats[habit.key].completed / habitStats[habit.key].total) * 100);
});

// 显示结果
dv.table(["习惯", "完成次数", "总天数", "完成率"], 
    habits.map(habit => [
        habit.label, 
        habitStats[habit.key].completed,
        habitStats[habit.key].total,
        `${habitStats[habit.key].percentage}%`
    ])
);
```

## 🏃 运动数据统计

```dataviewjs
// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 初始化运动数据
let totalDistance = 0;
let runningDays = 0;
let runningData = [];

// 收集运动数据
pages.forEach(page => {
    if (page.running && page.Distance_km) {
        runningDays++;
        totalDistance += parseFloat(page.Distance_km);
        
        // 提取日期用于显示
        const dateStr = page.file.name.split(" ")[0];
        
        // 存储每天的数据用于表格显示
        runningData.push({
            date: dateStr,
            distance: page.Distance_km,
            pace: page.Pace_per_km || "-",
            heartRate: page.Heart_Rate_BPM || "-"
        });
    }
});

// 显示汇总数据
dv.header(3, "运动汇总");
dv.paragraph(`- **总跑步天数**: ${runningDays}天`);
dv.paragraph(`- **总跑步距离**: ${totalDistance.toFixed(2)}公里`);
dv.paragraph(`- **平均每次距离**: ${runningDays > 0 ? (totalDistance / runningDays).toFixed(2) : 0}公里`);

// 显示每日数据表格
if (runningData.length > 0) {
    dv.header(3, "每日运动数据");
    dv.table(["日期", "距离(km)", "配速(/km)", "心率(BPM)"], 
        runningData.map(d => [
            d.date,
            d.distance,
            d.pace,
            d.heartRate
        ])
    );
} else {
    dv.paragraph("本月暂无运动数据");
}
```

## 😴 睡眠数据统计

```dataviewjs
// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 准备数据
let totalSleepTime = 0;
let totalTimeInBed = 0;
let totalSleepQuality = 0;
let daysWithSleepData = 0;

// 处理每一天的数据
for (let page of pages) {
    if (page.sleep_bedtime && page.sleep_fallasleep && page.sleep_wakeup && page.sleep_outofbed) {
        daysWithSleepData++;
        
        // 转换时间字符串为分钟数
        function timeToMinutes(timeStr) {
            const [hours, minutes] = timeStr.split(':').map(Number);
            return hours * 60 + minutes;
        }
        
        // 处理跨天的情况
        function calculateDuration(start, end) {
            let startMin = timeToMinutes(start);
            let endMin = timeToMinutes(end);
            
            // 如果结束时间小于开始时间，假设跨天
            if (endMin < startMin) {
                endMin += 24 * 60;
            }
            
            return endMin - startMin;
        }
        
        // 计算各项指标
        const totalSleepTimeToday = calculateDuration(page.sleep_fallasleep, page.sleep_wakeup);
        const timeInBedToday = calculateDuration(page.sleep_bedtime, page.sleep_outofbed);
        
        totalSleepTime += totalSleepTimeToday;
        totalTimeInBed += timeInBedToday;
        totalSleepQuality += page.sleep_quality || 3;
    }
}

// 计算平均值
const avgSleepTime = daysWithSleepData ? totalSleepTime / daysWithSleepData : 0;
const avgTimeInBed = daysWithSleepData ? totalTimeInBed / daysWithSleepData : 0;
const avgSleepQuality = daysWithSleepData ? totalSleepQuality / daysWithSleepData : 0;
const avgSleepEfficiency = avgTimeInBed ? Math.round((avgSleepTime / avgTimeInBed) * 100) : 0;

// 显示汇总数据
dv.header(3, "睡眠汇总");
dv.paragraph(`- **记录天数**: ${daysWithSleepData}天`);
dv.paragraph(`- **平均睡眠时长**: ${Math.floor(avgSleepTime/60)}小时${Math.round(avgSleepTime%60)}分钟`);
dv.paragraph(`- **平均卧床时长**: ${Math.floor(avgTimeInBed/60)}小时${Math.round(avgTimeInBed%60)}分钟`);
dv.paragraph(`- **平均睡眠质量**: ${avgSleepQuality.toFixed(1)}/5`);
dv.paragraph(`- **平均睡眠效率**: ${avgSleepEfficiency}%`);
```

## 📚 阅读统计

```dataviewjs
// 获取当前月记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前月内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    });

// 按书籍分组统计阅读时间
let bookStats = {};
let totalReadingDays = 0;
let totalReadingTime = 0;

pages.forEach(page => {
    if (page.reading && page.Reading_min && page.book) {
        totalReadingDays++;
        totalReadingTime += parseInt(page.Reading_min);
        
        if (!bookStats[page.book]) {
            bookStats[page.book] = 0;
        }
        bookStats[page.book] += parseInt(page.Reading_min);
    }
});

// 显示汇总数据
dv.header(3, "阅读汇总");
dv.paragraph(`- **阅读天数**: ${totalReadingDays}天`);
dv.paragraph(`- **总阅读时间**: ${totalReadingTime}分钟 (约${(totalReadingTime/60).toFixed(1)}小时)`);
dv.paragraph(`- **平均每天阅读**: ${totalReadingDays > 0 ? Math.round(totalReadingTime/totalReadingDays) : 0}分钟`);

// 显示每本书的阅读时间
if (Object.keys(bookStats).length > 0) {
    dv.header(3, "书籍阅读时间");
    dv.table(["书名", "阅读时间(分钟)"], 
        Object.entries(bookStats).map(([book, time]) => [
            book,
            time
        ]).sort((a, b) => b[1] - a[1]) // 按阅读时间降序排序
    );
} else {
    dv.paragraph("本月暂无阅读数据");
}
```

## 🍚 精神食粮 Spiritual Nourishment

### 🤔 每日斯多葛 Stoic
```dataview
LIST without id Stoic
FROM "0_Bullet Journal/Daily Notes"
WHERE date(split(file.name, " ")[0]) >= date(this.start_date)
    AND date(split(file.name, " ")[0]) <= date(this.end_date)
    AND Stoic
```

### 📖 书籍 Book
```dataview
LIST without id "《" + book + "》" + " 本月共计📖时长" + sum(rows.Reading_min) + "min " 
FROM "0_Bullet Journal/Daily Notes"
WHERE date(split(file.name, " ")[0]) >= date(this.start_date)
    AND date(split(file.name, " ")[0]) <= date(this.end_date)
    AND book
GROUP BY book
```

```dataview
LIST 
FROM "2_Literature notes/2_2_Book"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

### 👂播客 Podcast
```dataview
LIST without id podcast
FROM "0_Bullet Journal/Daily Notes"
WHERE date(split(file.name, " ")[0]) >= date(this.start_date)
    AND date(split(file.name, " ")[0]) <= date(this.end_date)
    AND podcast != null
```

### 📑 文章 Article
```dataview
LIST 
FROM "2_Literature notes/2_1_Article"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

### 🎬 电影电视 Movie & TV
```dataview
LIST
FROM "2_Literature notes/2_4_Movie TV"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

### 📱 视频 Video
```dataview
LIST 
FROM "2_Literature notes/2_5_Vedio"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

### 🎵 音乐 Music
```dataview
LIST 
FROM "2_Literature notes/2_6_Music"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

### 📃小说 Fiction
```dataview
LIST 
FROM "2_Literature notes/2_7_Fiction"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```
## ❓ 思考与发问 Thinking & Question
```dataview
LIST 
FROM "3_Permanent notes/3_3_Questions问题"
WHERE file.ctime >= date(this.start_date) AND file.ctime <= date(this.end_date)
```

## 🧰 任务完成情况 Tasks Completion

### ✅ 本月已完成项 Done
```tasks
done after <% moment(tp.file.title, "YYYY-MM").format("YYYY-MM-01") %>
done before <% moment(tp.file.title, "YYYY-MM").add(1, 'month').format("YYYY-MM-01") %>
sort by done reverse
short
```

## 📅 周记汇总 Weekly Reviews
```dataview
LIST
FROM "0_Bullet Journal/Weekly Notes"
WHERE contains(file.name, "W") 
AND date(start_date) >= date(this.start_date)
AND date(start_date) <= date(this.end_date)
SORT file.name ASC
``` 
## 🤔 月度回顾与思考 Monthly Review & Thinking

### 🏆 本月亮点 Monthly Highlights
<!-- 本月完成的重要任务？值得庆祝的进展？ -->
- 

### 🧗 克服的挑战 Challenges Overcome
<!-- 遇到了哪些困难或障碍？如何应对这些挑战？ -->
- 

### 💡 关键洞察 Key Insights
<!-- 学到了什么新知识或技能？有什么新的灵感或想法？ -->
- 

### 🛠️ 改进空间 Areas for Improvement
<!-- 哪些方面可以改进？如何在下个月做得更好？ -->
- 

### 🌱 成长轨迹 Growth Trajectory
<!-- 本月的进展与长期目标的关系 -->
- 

## 🎯 下月计划与目标 Plans & Goals for Next Month

- 

## 💻 知识库维护 Knowledge System

### ➕ 本月创建的笔记 Created
```dataview
TABLE without id file.cday AS Date, file.link AS Name, file.folder AS Folder
WHERE file.cday >= date(this.start_date) 
    AND file.cday <= date(this.end_date)
    AND contains(file.folder,"Templates") = False 
    AND contains(file.folder,"5") = False
SORT file.cday DESC,file.folder ASC
```

### 🔧 本月修改的笔记 Updated
```dataview
TABLE without id file.mday AS Date, file.link AS Name, file.folder AS Folder
WHERE file.mday >= date(this.start_date)
    AND file.mday <= date(this.end_date)
    AND contains(file.folder,"Templates") = False
    AND contains(file.folder,"5") = False
SORT file.mday DESC,file.folder ASC
```

---
## 📈 月复盘与规划 Monthly Review & Planning
> 基于复盘.txt框架，深度回顾每个月的成长轨迹

### 🎯 核心复盘 Core Review
<!-- 快速模式：只需填写这5个核心问题即可完成月复盘 -->

**1. 本月最大的成就和突破？**
-

**2. 本月满意度评分（1-10分）及原因？**
- 工作：/10 -
- 生活：/10 -
- 健康：/10 -

**3. 本月最重要的经验教训？**
-

**4. 本月情绪状态总结（高兴/沮丧的主要原因）？**
-

**5. 下月最重要的3个目标？**
-
-
-

---
### 📋 深度复盘 Deep Reflection
<!-- 完整模式：有时间时可以展开全面分析 -->

<details>
<summary>💡 点击展开深度复盘问题</summary>

#### 🏆 成就与进展详细分析
**本月家庭生活状况，陪伴家人的时间和效果？**
-

**本月工作进展，开启或完成了什么重要项目？**
-

#### 📋 重要事件与数据回顾
**本月收支情况分析**
- 收入：
- 支出：
- 非必要支出：
- 财务状况评价：

**本月健康状况和锻炼坚持情况？**
-

#### 🎯 目标完成情况深度分析
**本月面临的最大挑战及应对方式？**
- 最大挑战：
- 应对方式：
- 效果评价：

**本月目标完成情况详细分析**
- 已完成目标：
- 未完成目标：
- 完成率：%

#### 😊 情绪与心理状态分析
**本月情绪管理详细回顾**
- 高兴的事：
- 沮丧的事：
- 情绪管理状态：
- 改进方向：

#### 🔍 自我认知与成长
**对自己有什么新发现，挖掘了什么潜力？**
-

**本月在各个维度的详细满意度评价**
- 工作：/10 -
- 家庭：/10 -
- 健康：/10 -
- 财务：/10 -
- 人际：/10 -
- 成长：/10 -

#### 🎯 下月详细规划
**下月各方面的具体目标和计划**
- 个人目标：
- 事业目标：
- 家庭目标：
- 社会目标：

</details>
