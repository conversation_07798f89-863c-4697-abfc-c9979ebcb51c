# Fetch MCP 安装配置指南

## 概述

Fetch MCP Server 是一个模型上下文协议服务器，提供网页内容获取功能。它能够让LLM检索和处理网页内容，将HTML转换为markdown格式以便更好地使用。

## 功能特性

### 可用工具

- **fetch** - 从互联网获取URL并提取其内容为markdown格式
  - `url` (字符串，必需): 要获取的URL
  - `max_length` (整数，可选): 返回的最大字符数 (默认: 5000)
  - `start_index` (整数，可选): 从此字符索引开始内容提取 (默认: 0)
  - `raw` (布尔值，可选): 获取原始内容而不进行markdown转换 (默认: false)

### 提示功能

- **fetch** - 获取URL并提取其内容为markdown格式

## 安装方式

### 方式1：使用uvx（推荐）

使用 [`uv`](https://docs.astral.sh/uv/) 时无需特定安装。我们将使用 [`uvx`](https://docs.astral.sh/uv/guides/tools/) 直接运行 _mcp-server-fetch_。

```bash
# 无需预安装，uvx会自动处理
```

### 方式2：使用pip安装

```bash
pip install mcp-server-fetch
```

安装后，可以作为脚本运行：

```bash
python -m mcp_server_fetch
```

### 方式3：使用docker

```bash
docker run -i --rm mcp/fetch
```

## 配置方法

### Augment IDE 配置

将以下配置添加到您的Augment MCP设置中：

#### 使用uvx（推荐）

```json
{
  "mcpServers": {
    "fetch": {
      "command": "uvx",
      "args": ["mcp-server-fetch"]
    }
  }
}
```

#### 使用pip安装方式

```json
{
  "mcpServers": {
    "fetch": {
      "command": "python",
      "args": ["-m", "mcp_server_fetch"]
    }
  }
}
```

#### 使用docker方式

```json
{
  "mcpServers": {
    "fetch": {
      "command": "docker",
      "args": ["run", "-i", "--rm", "mcp/fetch"]
    }
  }
}
```

## 自定义配置

### robots.txt 处理

默认情况下，如果请求来自模型（通过工具），服务器会遵守网站的robots.txt文件，但如果请求是用户发起的（通过提示），则不会。可以通过在配置的`args`列表中添加`--ignore-robots-txt`参数来禁用此功能。

### 用户代理自定义

默认情况下，根据请求是来自模型（通过工具）还是用户发起的（通过提示），服务器将使用不同的用户代理：

- 模型请求：`ModelContextProtocol/1.0 (Autonomous; +https://github.com/modelcontextprotocol/servers)`
- 用户请求：`ModelContextProtocol/1.0 (User-Specified; +https://github.com/modelcontextprotocol/servers)`

可以通过在配置的`args`列表中添加`--user-agent=YourUserAgent`参数来自定义。

### 代理配置

可以通过使用`--proxy-url`参数来配置服务器使用代理。

## 调试方法

可以使用MCP检查器来调试服务器：

### uvx安装的调试

```bash
npx @modelcontextprotocol/inspector uvx mcp-server-fetch
```

### 本地开发调试

```bash
cd path/to/servers/src/fetch
npx @modelcontextprotocol/inspector uv run mcp-server-fetch
```

## 完整配置示例

以下是包含fetch MCP的完整Augment配置示例：

```json
{
  "mcpServers": {
    "mcp-obsidian": {
      "command": "uvx",
      "args": ["mcp-obsidian"],
      "env": {
        "OBSIDIAN_API_KEY": "your-api-key",
        "OBSIDIAN_HOST": "https://127.0.0.1:27124/"
      }
    },
    "fetch": {
      "command": "uvx",
      "args": ["mcp-server-fetch"]
    },
    "context7": {
      "command": "npx",
      "args": ["context7"]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["sequential-thinking"]
    }
  }
}
```

## 使用示例

安装配置完成后，您可以在Augment中使用以下功能：

1. **获取网页内容**：让AI助手获取任何网页的内容并转换为markdown格式
2. **分块读取**：对于长网页，可以使用`start_index`参数分块读取
3. **原始内容**：需要时可以获取未转换的原始HTML内容

## 注意事项

1. **Python版本要求**：需要Python >=3.10
2. **网络访问**：确保网络连接正常，能够访问目标网站
3. **robots.txt**：默认会遵守网站的robots.txt规则
4. **性能**：建议安装node.js以获得更强大的HTML简化器

## 故障排除

如果遇到问题：

1. 检查Python版本是否满足要求
2. 确认网络连接正常
3. 使用MCP检查器进行调试
4. 查看Augment的MCP服务器日志

## 许可证

mcp-server-fetch 使用MIT许可证。您可以自由使用、修改和分发软件。
