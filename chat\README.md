# Augment Agent 对话记录存储

> 📅 创建时间：2025-07-08 星期二
> 🎯 目标：记录和管理Augment Agent重要对话历史

## 📁 文件夹结构

```
chat/
├── README.md                    # 本说明文件
├── templates/                   # 对话记录模板
│   ├── conversation-template.md # 标准对话模板
│   └── quick-template.md       # 快速记录模板
├── daily/                      # 日常对话记录
├── projects/                   # 项目相关对话
├── learning/                   # 学习探索记录
└── important/                  # 重要决策对话
```

## 🎯 使用方法

### 触发导出
在对话中输入以下指令之一：
- `导出对话` - 导出当前完整对话
- `导出对话：[主题关键词]` - 导出并指定主题
- `保存重要对话` - 标记为重要对话并导出

### 文件命名规范
- 格式：`YYYYMMDD_对话主题关键词.md`
- 示例：`20250708_MCP工具配置.md`

## 📋 记录标准

### 必须记录的对话类型
- 重要技术决策和方案选择
- 复杂问题的解决过程
- 可复用的代码模式和最佳实践
- 工具配置和环境设置
- 学习心得和经验总结

### 可选记录的对话类型
- 日常咨询和简单问答
- 文档查询和信息检索
- 常规代码修改

## 🔄 维护规则

1. **每周整理** - 回顾本周对话记录，提取重要经验
2. **月度归档** - 将过期的日常对话移至归档文件夹
3. **知识提取** - 将重要经验更新到项目文档中
4. **质量检查** - 确保记录内容完整和准确

## 📊 统计信息

- 总对话记录：待统计
- 重要对话：待统计
- 最近更新：2025-07-08

---

*📝 备注：此文件夹用于存储Augment Agent的重要对话记录，配合手动导出工作流程使用。*
