# Augment对话记录 - {主题关键词}

> 📅 对话时间：{YYYY-MM-DD 星期X}
> 🎯 对话主题：{主题描述}
> 📂 项目路径：{项目相对路径}
> ⭐ 重要程度：{高/中/低}

## 📋 对话概要

### 背景信息
- **触发原因**：{为什么开始这次对话}
- **预期目标**：{希望解决什么问题}
- **相关文件**：{涉及的主要文件和路径}

### 关键问题
1. {主要问题1}
2. {主要问题2}
3. {主要问题3}

## 💬 详细对话记录

### 用户提问 1
```
{用户的具体问题或需求}
```

### Augment回复 1
```
{AI的详细回复内容}
```

### 代码修改 1
**文件路径**：`{相对路径/文件名}`
**修改类型**：{新建/修改/删除}
**修改内容**：
```{语言}
{具体的代码修改内容}
```

---

### 用户提问 2
```
{后续问题}
```

### Augment回复 2
```
{AI回复}
```

### 代码修改 2
**文件路径**：`{路径}`
**修改类型**：{类型}
**修改内容**：
```{语言}
{代码内容}
```

---

## 🎯 解决方案总结

### 采用的方案
- **最终选择**：{选择的解决方案}
- **选择原因**：{为什么选择这个方案}
- **实施步骤**：
  1. {步骤1}
  2. {步骤2}
  3. {步骤3}

### 关键决策
1. **决策点1**：{决策内容} → {选择理由}
2. **决策点2**：{决策内容} → {选择理由}
3. **决策点3**：{决策内容} → {选择理由}

## 📚 经验总结

### 学到的知识
- {重要知识点1}
- {重要知识点2}
- {重要知识点3}

### 最佳实践
- {实践经验1}
- {实践经验2}
- {实践经验3}

### 避免的陷阱
- {需要避免的问题1}
- {需要避免的问题2}

## 🔄 后续行动

### 立即行动
- [ ] {需要立即执行的任务1}
- [ ] {需要立即执行的任务2}

### 计划行动
- [ ] {计划中的任务1}
- [ ] {计划中的任务2}

### 长期关注
- [ ] {需要长期关注的事项1}
- [ ] {需要长期关注的事项2}

## 🔗 相关链接

- **相关文档**：{链接到相关的项目文档}
- **参考资料**：{外部参考链接}
- **相关对话**：{其他相关的对话记录}

---

**📊 对话统计**
- 对话轮次：{总轮次}
- 代码修改：{修改文件数}
- 解决问题：{解决的问题数}
- 用时估计：{大约用时}

**🏷️ 标签**：#{主题标签1} #{主题标签2} #{主题标签3}
