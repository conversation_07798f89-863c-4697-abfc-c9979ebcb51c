#!/usr/bin/env node
/**
 * Memory MCP 基础功能测试
 * 测试 memory MCP 的基本工具是否可用
 */

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// 测试配置
const CONFIG_PATH = 'config/mcp/augment/Augment-包含Memory-MCP配置.json';
const MEMORY_FILE = 'memory.json';

function log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
        'info': '📋',
        'success': '✅',
        'error': '❌',
        'warning': '⚠️'
    }[type] || '📋';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
}

function testConfigFile() {
    log('测试配置文件...', 'info');
    
    try {
        if (!fs.existsSync(CONFIG_PATH)) {
            log(`配置文件不存在: ${CONFIG_PATH}`, 'error');
            return false;
        }
        
        const config = JSON.parse(fs.readFileSync(CONFIG_PATH, 'utf8'));
        
        if (!config.mcpServers || !config.mcpServers.memory) {
            log('配置文件中缺少 memory 服务器配置', 'error');
            return false;
        }
        
        const memoryConfig = config.mcpServers.memory;
        log(`Memory 配置: ${JSON.stringify(memoryConfig, null, 2)}`, 'info');
        
        log('配置文件验证成功', 'success');
        return true;
        
    } catch (error) {
        log(`配置文件验证失败: ${error.message}`, 'error');
        return false;
    }
}

function testMemoryFile() {
    log('测试内存文件访问...', 'info');
    
    try {
        // 检查内存文件是否存在
        if (fs.existsSync(MEMORY_FILE)) {
            log(`内存文件已存在: ${path.resolve(MEMORY_FILE)}`, 'info');
            
            // 尝试读取现有内容
            const content = fs.readFileSync(MEMORY_FILE, 'utf8');
            const data = JSON.parse(content);
            log(`现有内存数据: ${JSON.stringify(data, null, 2)}`, 'info');
        } else {
            log(`内存文件将创建在: ${path.resolve(MEMORY_FILE)}`, 'info');
        }
        
        // 测试写权限
        const testData = {
            entities: [],
            relations: [],
            test: true,
            timestamp: new Date().toISOString()
        };
        
        fs.writeFileSync(MEMORY_FILE, JSON.stringify(testData, null, 2));
        log('内存文件写入测试成功', 'success');
        
        // 读取验证
        const readData = JSON.parse(fs.readFileSync(MEMORY_FILE, 'utf8'));
        if (readData.test === true) {
            log('内存文件读取验证成功', 'success');
            return true;
        } else {
            log('内存文件读取验证失败', 'error');
            return false;
        }
        
    } catch (error) {
        log(`内存文件测试失败: ${error.message}`, 'error');
        return false;
    }
}

function testNpxCommand() {
    log('测试 npx 命令可用性...', 'info');
    
    return new Promise((resolve) => {
        const child = spawn('npx', ['--version'], { 
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true 
        });
        
        let output = '';
        
        child.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                log(`npx 版本: ${output.trim()}`, 'success');
                resolve(true);
            } else {
                log('npx 命令不可用', 'error');
                resolve(false);
            }
        });
        
        child.on('error', (error) => {
            log(`npx 测试失败: ${error.message}`, 'error');
            resolve(false);
        });
    });
}

async function testMemoryMcpPackage() {
    log('测试 Memory MCP 包可用性...', 'info');
    
    return new Promise((resolve) => {
        // 使用 npm list 检查包是否已安装
        const child = spawn('npm', ['list', '-g', '@modelcontextprotocol/server-memory'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            shell: true
        });
        
        let output = '';
        let errorOutput = '';
        
        child.stdout.on('data', (data) => {
            output += data.toString();
        });
        
        child.stderr.on('data', (data) => {
            errorOutput += data.toString();
        });
        
        child.on('close', (code) => {
            if (code === 0 && output.includes('@modelcontextprotocol/server-memory')) {
                log('Memory MCP 包已正确安装', 'success');
                resolve(true);
            } else {
                log(`Memory MCP 包检查失败: ${errorOutput}`, 'error');
                resolve(false);
            }
        });
        
        child.on('error', (error) => {
            log(`Memory MCP 包测试失败: ${error.message}`, 'error');
            resolve(false);
        });
    });
}

async function main() {
    log('开始 Memory MCP 基础功能测试', 'info');
    log('='.repeat(50), 'info');
    
    const tests = [
        { name: '配置文件验证', func: testConfigFile },
        { name: '内存文件访问', func: testMemoryFile },
        { name: 'npx 命令可用性', func: testNpxCommand },
        { name: 'Memory MCP 包检查', func: testMemoryMcpPackage }
    ];
    
    const results = [];
    
    for (const test of tests) {
        log(`\n执行测试: ${test.name}`, 'info');
        log('-'.repeat(30), 'info');
        
        try {
            const result = await test.func();
            results.push({ name: test.name, success: result });
        } catch (error) {
            log(`测试异常: ${error.message}`, 'error');
            results.push({ name: test.name, success: false });
        }
    }
    
    // 汇总结果
    log('\n' + '='.repeat(50), 'info');
    log('测试结果汇总', 'info');
    log('='.repeat(50), 'info');
    
    let passed = 0;
    const total = results.length;
    
    results.forEach(result => {
        const status = result.success ? '✅ 通过' : '❌ 失败';
        log(`${result.name}: ${status}`, result.success ? 'success' : 'error');
        if (result.success) passed++;
    });
    
    log(`\n总体结果: ${passed}/${total} 测试通过`, passed === total ? 'success' : 'warning');
    
    if (passed === total) {
        log('🎉 所有测试通过！Memory MCP 配置就绪', 'success');
        return true;
    } else {
        log('⚠️ 部分测试失败，请检查配置', 'warning');
        return false;
    }
}

// 运行测试
main().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    log(`测试运行异常: ${error.message}`, 'error');
    process.exit(1);
});
