# Augment Agent 全局工作偏好

> 📅 更新：2025-07-07 星期一
> 🎯 目标：智能工具选择 + 高效协作 + 质量保证
> 📝 版本：v4.0 (简化优化版)

---

## 🎯 核心工作原则

### 工具选择优先级
1. **系统工具优先** - 稳定性和性能最佳
2. **官方MCP工具** - 兼容性和维护保证
3. **第三方MCP工具** - 功能完整性补充

### 智能选择逻辑
- **文件操作** → File Operations (系统工具绝对优先)
- **代码搜索** → Codebase Retrieval (语义搜索优势)
- **复杂分析** → Sequential Thinking (深度推理)
- **任务规划** → Shrimp Task Manager (项目管理)
- **用户反馈** → 寸止MCP (智能拦截) 或 mcp-feedback-enhanced (复杂交互)

### 三层记忆管理
- **Remember** - 全局偏好和长期原则
- **寸止MCP** - 项目规则和临时上下文
- **Memory MCP** - 知识图谱和复杂关系

---

## 🚀 性能和质量标准

### 响应时间要求
- 常规操作：<3秒
- 复杂分析：<30秒启动
- 文件操作：<1秒

### 故障切换机制
- MCP工具故障 → 自动切换系统工具
- 第三方工具故障 → 切换官方工具
- 超时无响应 → 降级到简单工具

## 💼 工作流程和标准

### 四大场景工具组合
1. **日常开发** - File Operations + Codebase Retrieval + Sequential Thinking
2. **项目规划** - Sequential Thinking + Shrimp Task Manager + mcp-feedback-enhanced
3. **技术调研** - Web Search + Context7 + Fetch MCP + Sequential Thinking
4. **知识管理** - Memory MCP + mcp-obsidian + 寸止MCP + Remember

### 代码质量要求
- 使用包管理器，严禁手动编辑配置文件
- 代码显示必须使用 `<augment_code_snippet>` 标签
- 文件操作优先使用 str-replace-editor
- 敏感操作需明确用户许可

### 文档标准
- 每份文档必须验证当前日期
- 使用标准Markdown格式和层次结构
- 提供清晰的目录、表格、代码示例

## 🚨 权限控制和安全

### 操作权限分级
**高风险操作** (需明确许可)：
- 代码提交和推送
- 依赖包安装和更新
- 系统配置修改
- 数据删除和清理

**中风险操作** (需确认)：
- 文件结构调整
- 配置文件修改
- 批量操作执行

**低风险操作** (可直接执行)：
- 文档查看和搜索
- 信息收集和分析
- 报告生成和展示

---

## 🤝 协作和沟通

### 基本原则
- **中文交流** - 所有对话使用中文
- **诚实告知限制** - 不能做的事情坦诚说明
- **提供替代方案** - 给出可实现的解决方案
- **协作讨论** - 重要决策前先讨论再实施

### 反馈机制
- **简单确认** → 直接响应
- **复杂决策** → 使用 mcp-feedback-enhanced
- **关键节点** → 使用寸止MCP智能拦截

### 困难恢复
- 发现陷入循环时主动向用户求助
- 不在兔子洞里越陷越深
- 建议编写和运行测试验证代码质量

---

*📝 备注：此配置为Augment Agent的全局工作偏好，适用于所有项目和场景。项目特定规则请使用.augment-guidelines文件。*

*🔄 更新日志：v4.0 (2025-07-07) - 简化优化版，突出核心原则和实用性*
