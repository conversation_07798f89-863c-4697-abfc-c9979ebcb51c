/**
 * 复盘配置应用器
 * 
 * 读取用户配置并动态生成个性化的复盘模板
 * 支持配置验证、模板生成和错误处理
 */

// 默认配置
const DEFAULT_CONFIG = {
    daily_dimensions: [
        {
            name: "今日成就",
            description: "今天做了什么？做这些事情用了多少时间？",
            required: true,
            enabled: true
        },
        {
            name: "今日反思",
            description: "应该做的事、不应该做的事、问题根本原因、时间分配改进",
            required: true,
            enabled: true
        },
        {
            name: "今日收获",
            description: "今天有哪些收获，包括感受和启发？",
            required: true,
            enabled: true
        }
    ],
    display_settings: {
        theme: "default",
        language: "zh-CN",
        date_format: "YYYY-MM-DD",
        time_format: "24h"
    },
    review_style: {
        detailed: true,
        include_prompts: true,
        auto_fill_data: true,
        template_style: "standard"
    }
};

/**
 * 主函数：应用配置生成个性化复盘模板
 */
async function applyConfiguration(params) {
    const quickAdd = params.quickAdd;
    const app = params.app || window.app;
    
    try {
        // 读取用户配置
        const userConfig = await loadUserConfiguration(app);
        
        // 验证配置
        const validationResult = validateConfiguration(userConfig);
        if (!validationResult.isValid) {
            new Notice(`❌ 配置验证失败: ${validationResult.errors.join(", ")}`);
            return;
        }
        
        // 选择复盘类型
        const reviewType = await quickAdd.suggester(
            ["📅 日复盘", "📊 周复盘", "📈 月复盘"],
            ["daily", "weekly", "monthly"],
            false,
            "选择复盘类型"
        );
        
        if (!reviewType) return;
        
        // 生成个性化模板
        const customTemplate = generateCustomTemplate(userConfig, reviewType);
        
        // 创建文件
        await createCustomReviewNote(app, customTemplate, reviewType);
        
        new Notice("✅ 已创建个性化复盘笔记");
        
    } catch (error) {
        console.error("配置应用失败:", error);
        new Notice(`❌ 配置应用失败: ${error.message}`);
    }
}

/**
 * 加载用户配置
 */
async function loadUserConfiguration(app) {
    try {
        // 查找配置管理中心文件
        const configFile = app.vault.getAbstractFileByPath("复盘系统/配置管理中心.md");
        
        if (!configFile) {
            console.warn("配置文件未找到，使用默认配置");
            return DEFAULT_CONFIG;
        }
        
        // 读取配置文件的元数据
        const cache = app.metadataCache.getFileCache(configFile);
        const userConfig = cache?.frontmatter?.user_config;
        
        if (!userConfig) {
            console.warn("用户配置未找到，使用默认配置");
            return DEFAULT_CONFIG;
        }
        
        // 合并用户配置和默认配置
        return mergeConfigurations(DEFAULT_CONFIG, userConfig);
        
    } catch (error) {
        console.error("加载配置失败:", error);
        return DEFAULT_CONFIG;
    }
}

/**
 * 合并配置
 */
function mergeConfigurations(defaultConfig, userConfig) {
    const merged = JSON.parse(JSON.stringify(defaultConfig));
    
    // 合并日复盘维度
    if (userConfig.daily_dimensions) {
        merged.daily_dimensions = userConfig.daily_dimensions;
    }
    
    // 合并周复盘维度
    if (userConfig.weekly_dimensions) {
        merged.weekly_dimensions = userConfig.weekly_dimensions;
    }
    
    // 合并显示设置
    if (userConfig.display_settings) {
        merged.display_settings = { ...merged.display_settings, ...userConfig.display_settings };
    }
    
    // 合并复盘风格
    if (userConfig.review_style) {
        merged.review_style = { ...merged.review_style, ...userConfig.review_style };
    }
    
    return merged;
}

/**
 * 验证配置
 */
function validateConfiguration(config) {
    const errors = [];
    const warnings = [];
    
    // 验证日复盘维度
    if (!config.daily_dimensions || config.daily_dimensions.length === 0) {
        errors.push("日复盘维度配置为空");
    } else {
        const enabledDaily = config.daily_dimensions.filter(d => d.enabled);
        if (enabledDaily.length === 0) {
            warnings.push("所有日复盘维度都已禁用");
        }
        
        // 检查必需维度
        const requiredDaily = config.daily_dimensions.filter(d => d.required && !d.enabled);
        if (requiredDaily.length > 0) {
            errors.push(`必需的日复盘维度被禁用: ${requiredDaily.map(d => d.name).join(", ")}`);
        }
    }
    
    // 验证显示设置
    if (!config.display_settings) {
        warnings.push("显示设置配置缺失，将使用默认值");
    }
    
    return {
        isValid: errors.length === 0,
        errors,
        warnings
    };
}

/**
 * 生成自定义模板
 */
function generateCustomTemplate(config, reviewType) {
    const now = new Date();
    const dateStr = formatDate(now, config.display_settings?.date_format || "YYYY-MM-DD");
    
    let template = "";
    
    // 生成文件头
    template += generateTemplateHeader(config, reviewType, dateStr);
    
    // 生成复盘内容
    if (reviewType === "daily") {
        template += generateDailyReviewContent(config);
    } else if (reviewType === "weekly") {
        template += generateWeeklyReviewContent(config);
    } else if (reviewType === "monthly") {
        template += generateMonthlyReviewContent(config);
    }
    
    // 生成模板尾部
    template += generateTemplateFooter(config);
    
    return template;
}

/**
 * 生成模板头部
 */
function generateTemplateHeader(config, reviewType, dateStr) {
    const typeMap = {
        daily: { name: "日复盘", emoji: "📅", level: "beginner" },
        weekly: { name: "周复盘", emoji: "📊", level: "standard" },
        monthly: { name: "月复盘", emoji: "📈", level: "advanced" }
    };
    
    const typeInfo = typeMap[reviewType];
    
    let header = `---
tags:
  - ${reviewType}-review
  - ${typeInfo.level}-level
  - custom-config
review_level: "${typeInfo.level}"
review_type: "${reviewType}"
created: ${new Date().toISOString().slice(0, 16)}
date: ${dateStr}
---
# ${typeInfo.emoji} ${typeInfo.name} - ${dateStr}

> 🎯 **个性化配置模式** | 基于您的自定义配置生成

`;

    return header;
}

/**
 * 生成日复盘内容
 */
function generateDailyReviewContent(config) {
    let content = "## 📝 今日复盘\n\n";
    
    const enabledDimensions = config.daily_dimensions?.filter(d => d.enabled) || [];
    
    enabledDimensions.forEach((dimension, index) => {
        content += `### ${index + 1}. ${dimension.name}\n`;
        
        if (config.review_style?.include_prompts) {
            content += `**${dimension.description}**\n`;
        }
        
        content += "- \n\n";
        
        // 添加自动填充提示
        if (config.review_style?.auto_fill_data) {
            content += "*💡 提示：系统会自动填充相关数据*\n\n";
        }
    });
    
    return content;
}

/**
 * 生成周复盘内容
 */
function generateWeeklyReviewContent(config) {
    let content = "## 📊 本周复盘\n\n";
    
    const enabledDimensions = config.weekly_dimensions?.filter(d => d.enabled) || [];
    
    enabledDimensions.forEach((dimension, index) => {
        content += `### ${index + 1}. ${dimension.name}\n`;
        
        if (config.review_style?.include_prompts) {
            content += `**${dimension.description}**\n`;
        }
        
        content += "- \n\n";
    });
    
    return content;
}

/**
 * 生成月复盘内容
 */
function generateMonthlyReviewContent(config) {
    let content = "## 📈 本月复盘\n\n";
    
    // 月复盘使用固定的维度结构，但可以根据配置调整
    const monthlyDimensions = [
        { name: "本月成就汇总", description: "本月最大的成就和突破" },
        { name: "目标完成情况", description: "本月目标的完成情况分析" },
        { name: "经验教训总结", description: "本月学到的重要经验和教训" },
        { name: "下月目标规划", description: "下个月的重点目标和计划" }
    ];
    
    monthlyDimensions.forEach((dimension, index) => {
        content += `### ${index + 1}. ${dimension.name}\n`;
        
        if (config.review_style?.include_prompts) {
            content += `**${dimension.description}**\n`;
        }
        
        content += "- \n\n";
    });
    
    return content;
}

/**
 * 生成模板尾部
 */
function generateTemplateFooter(config) {
    let footer = "---\n";
    footer += "## 🔗 相关链接\n\n";
    footer += "- [[复盘系统/配置管理中心|⚙️ 配置管理中心]] - 调整个性化设置\n";
    footer += "- [[复盘系统/复盘数据仪表盘|📊 数据仪表盘]] - 查看复盘统计\n";
    footer += "- [[复盘系统/复盘引导中心|🎯 引导中心]] - 系统导航\n\n";
    footer += `*个性化复盘模板 | 配置版本: ${new Date().toISOString().slice(0, 10)}*\n`;
    
    return footer;
}

/**
 * 创建自定义复盘笔记
 */
async function createCustomReviewNote(app, templateContent, reviewType) {
    const now = new Date();
    const fileName = generateFileName(reviewType, now);
    const folderPath = getFolderPath(reviewType);
    const filePath = `${folderPath}/${fileName}`;
    
    // 检查文件是否已存在
    const existingFile = app.vault.getAbstractFileByPath(filePath);
    if (existingFile) {
        const shouldOverwrite = confirm(`文件已存在：${fileName}\n\n是否覆盖现有文件？`);
        if (!shouldOverwrite) {
            return;
        }
        await app.vault.delete(existingFile);
    }
    
    // 创建新文件
    const newFile = await app.vault.create(filePath, templateContent);
    
    // 打开新创建的文件
    await app.workspace.openLinkText(filePath, "", false);
}

/**
 * 生成文件名
 */
function generateFileName(reviewType, date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const weekday = ['日', '一', '二', '三', '四', '五', '六'][date.getDay()];
    
    switch (reviewType) {
        case 'daily':
            return `${year}-${month}-${day} 星期${weekday} 个性化复盘.md`;
        case 'weekly':
            const weekNum = getWeekNumber(date);
            return `${year}-W${String(weekNum).padStart(2, '0')} 个性化周复盘.md`;
        case 'monthly':
            return `${year}-${month} 个性化月复盘.md`;
        default:
            return `${year}-${month}-${day} 个性化复盘.md`;
    }
}

/**
 * 获取文件夹路径
 */
function getFolderPath(reviewType) {
    const folderMap = {
        daily: "0_Bullet Journal/Daily Notes",
        weekly: "0_Bullet Journal/Weekly Notes",
        monthly: "0_Bullet Journal/Monthly Notes"
    };
    
    return folderMap[reviewType] || "0_Bullet Journal/Daily Notes";
}

/**
 * 格式化日期
 */
function formatDate(date, format) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day);
}

/**
 * 获取周数
 */
function getWeekNumber(date) {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

// 导出主函数供QuickAdd使用
module.exports = applyConfiguration;
