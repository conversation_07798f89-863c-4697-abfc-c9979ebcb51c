# PowerShell 美化配置脚本
# 作者：Augment Agent
# 日期：2025-07-14
# 功能：一键配置漂亮的 PowerShell 环境

Write-Host "🎨 开始配置 PowerShell 美化环境..." -ForegroundColor Green

# 1. 安装 Oh My Posh
Write-Host "📦 安装 Oh My Posh..." -ForegroundColor Yellow
try {
    winget install JanDeDobbeleer.OhMyPosh -s winget
    Write-Host "✅ Oh My Posh 安装成功" -ForegroundColor Green
} catch {
    Write-Host "❌ Oh My Posh 安装失败，尝试使用 PowerShell Gallery..." -ForegroundColor Red
    Install-Module oh-my-posh -Scope CurrentUser -Force
}

# 2. 安装 Terminal-Icons
Write-Host "📦 安装 Terminal-Icons..." -ForegroundColor Yellow
try {
    Install-Module -Name Terminal-Icons -Repository PSGallery -Force -Scope CurrentUser
    Write-Host "✅ Terminal-Icons 安装成功" -ForegroundColor Green
} catch {
    Write-Host "❌ Terminal-Icons 安装失败" -ForegroundColor Red
}

# 3. 安装 PSReadLine (增强命令行编辑)
Write-Host "📦 安装 PSReadLine..." -ForegroundColor Yellow
try {
    Install-Module -Name PSReadLine -AllowPrerelease -Scope CurrentUser -Force -SkipPublisherCheck
    Write-Host "✅ PSReadLine 安装成功" -ForegroundColor Green
} catch {
    Write-Host "❌ PSReadLine 安装失败" -ForegroundColor Red
}

# 4. 安装 z (快速目录跳转)
Write-Host "📦 安装 z..." -ForegroundColor Yellow
try {
    Install-Module -Name z -Force -Scope CurrentUser
    Write-Host "✅ z 安装成功" -ForegroundColor Green
} catch {
    Write-Host "❌ z 安装失败" -ForegroundColor Red
}

# 5. 创建 PowerShell 配置文件
Write-Host "📝 创建 PowerShell 配置文件..." -ForegroundColor Yellow

$profilePath = $PROFILE
$profileDir = Split-Path $profilePath -Parent

# 确保配置文件目录存在
if (!(Test-Path $profileDir)) {
    New-Item -ItemType Directory -Path $profileDir -Force
}

# 创建配置文件内容
$profileContent = @"
# PowerShell 美化配置
# 自动生成于 $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')

# 导入模块
Import-Module Terminal-Icons
Import-Module z

# 设置 PSReadLine
Set-PSReadLineOption -PredictionSource History
Set-PSReadLineOption -PredictionViewStyle ListView
Set-PSReadLineOption -EditMode Windows
Set-PSReadLineKeyHandler -Key Tab -Function Complete
Set-PSReadLineKeyHandler -Key "Ctrl+d" -Function MenuComplete
Set-PSReadLineKeyHandler -Key "Ctrl+z" -Function Undo
Set-PSReadLineKeyHandler -Key UpArrow -Function HistorySearchBackward
Set-PSReadLineKeyHandler -Key DownArrow -Function HistorySearchForward

# 设置 Oh My Posh 主题
oh-my-posh init pwsh --config "$env:POSH_THEMES_PATH\paradox.omp.json" | Invoke-Expression

# 自定义别名
Set-Alias -Name ll -Value Get-ChildItem
Set-Alias -Name la -Value Get-ChildItem
Set-Alias -Name cls -Value Clear-Host
Set-Alias -Name grep -Value Select-String

# 自定义函数
function which(`$name) {
    Get-Command `$name | Select-Object -ExpandProperty Definition
}

function touch(`$file) {
    "" | Out-File `$file -Encoding UTF8
}

function mkcd(`$dir) {
    mkdir `$dir -Force
    Set-Location `$dir
}

# 欢迎信息
Write-Host "🎉 PowerShell 美化环境已加载！" -ForegroundColor Green
Write-Host "💡 使用 'z <目录名>' 快速跳转目录" -ForegroundColor Cyan
Write-Host "🔍 使用上下箭头搜索历史命令" -ForegroundColor Cyan
"@

# 写入配置文件
$profileContent | Out-File -FilePath $profilePath -Encoding UTF8 -Force

Write-Host "✅ PowerShell 配置文件已创建：$profilePath" -ForegroundColor Green

# 6. 设置字体建议
Write-Host "🔤 字体建议..." -ForegroundColor Yellow
Write-Host "建议安装 Nerd Font 字体以获得最佳显示效果：" -ForegroundColor Cyan
Write-Host "- CascadiaCode Nerd Font" -ForegroundColor White
Write-Host "- FiraCode Nerd Font" -ForegroundColor White
Write-Host "- JetBrainsMono Nerd Font" -ForegroundColor White
Write-Host "下载地址：https://www.nerdfonts.com/font-downloads" -ForegroundColor Blue

Write-Host "`n🎨 PowerShell 美化配置完成！" -ForegroundColor Green
Write-Host "🔄 请重启 PowerShell 或运行以下命令来应用配置：" -ForegroundColor Yellow
Write-Host ". `$PROFILE" -ForegroundColor White

Write-Host "`n📋 配置内容包括：" -ForegroundColor Cyan
Write-Host "✅ Oh My Posh 主题" -ForegroundColor White
Write-Host "✅ Terminal Icons 图标" -ForegroundColor White
Write-Host "✅ PSReadLine 增强编辑" -ForegroundColor White
Write-Host "✅ z 快速目录跳转" -ForegroundColor White
Write-Host "✅ 实用别名和函数" -ForegroundColor White
