---
tags:
  - review-guide
  - navigation
  - progressive-system
created: 2025-08-07
updated: 2025-08-07
---
# 🎯 复盘引导中心

> 智能复盘系统的导航中心，帮助您选择最适合的复盘方式

## 🌟 欢迎使用渐进式复盘系统

### 🤔 我应该选择哪个级别？

```dataviewjs
// 智能推荐复盘级别
const beginnerReviews = dv.pages('#beginner-level').where(p => p.review_type === 'daily').length;
const standardReviews = dv.pages('#standard-level').where(p => p.review_type === 'daily').length;
const totalReviews = beginnerReviews + standardReviews;
const totalExperience = beginnerReviews * 10 + standardReviews * 15;

let recommendedLevel = "beginner";
let recommendation = "";

if (totalExperience >= 500 && standardReviews >= 20) {
    recommendedLevel = "advanced";
    recommendation = "🌳 **推荐：深度复盘模式** - 您已经是复盘专家，可以使用完整的复盘功能！";
} else if (totalExperience >= 100 && beginnerReviews >= 7) {
    recommendedLevel = "standard";
    recommendation = "🌿 **推荐：标准级复盘** - 您已经建立了基本习惯，可以尝试更深入的思考！";
} else {
    recommendedLevel = "beginner";
    recommendation = "🌱 **推荐：入门级复盘** - 从简单开始，建立复盘习惯是最重要的！";
}

dv.paragraph("## 🎯 个性化推荐");
dv.paragraph(recommendation);
dv.paragraph("");
dv.paragraph(`**您的复盘数据：**`);
dv.paragraph(`- 总复盘次数：${totalReviews} 次`);
dv.paragraph(`- 累计经验值：${totalExperience} 点`);
dv.paragraph(`- 入门级：${beginnerReviews} 次 | 标准级：${standardReviews} 次`);
```

---
## 📋 复盘级别选择

### 🌱 入门级复盘 - 新手友好
**适合人群：** 刚开始复盘的用户
**特点：** 3个简单问题，2-3分钟完成
**经验值：** +10点/次

```dataview
TABLE without id
    "📅 " + file.link as "模板",
    "🌱 入门级" as "级别",
    "2-3分钟" as "用时"
FROM "Templates/复盘引导"
WHERE contains(file.name, "入门级")
```

**🎯 升级条件：** 完成7次入门级复盘，获得100经验值

---
### 🌿 标准级复盘 - 进阶发展
**适合人群：** 有一定复盘经验的用户
**特点：** 5个核心问题，5-8分钟完成
**经验值：** +15点/次

```dataview
TABLE without id
    "📅 " + file.link as "模板",
    "🌿 标准级" as "级别", 
    "5-8分钟" as "用时"
FROM "Templates/复盘引导"
WHERE contains(file.name, "标准级")
```

**🎯 升级条件：** 完成30次标准级复盘，获得500经验值

---
### 🌳 深度复盘 - 专家模式
**适合人群：** 复盘习惯稳定的用户
**特点：** 完整问题集，10-15分钟完成
**经验值：** +25点/次

```dataview
TABLE without id
    "📅 " + file.link as "模板",
    "🌳 深度级" as "级别",
    "10-15分钟" as "用时"
FROM "Templates"
WHERE file.name = "5_BuJo - Daily Log"
```

---
## 🎮 成就系统

### 🏆 当前成就
```dataviewjs
// 成就统计
const beginnerReviews = dv.pages('#beginner-level').where(p => p.review_type === 'daily').length;
const standardReviews = dv.pages('#standard-level').where(p => p.review_type === 'daily').length;
const totalReviews = beginnerReviews + standardReviews;

const achievements = [
    { name: "初次复盘", emoji: "🌟", condition: totalReviews >= 1, unlocked: totalReviews >= 1 },
    { name: "坚持三天", emoji: "🔥", condition: totalReviews >= 3, unlocked: totalReviews >= 3 },
    { name: "一周达人", emoji: "🏆", condition: totalReviews >= 7, unlocked: totalReviews >= 7 },
    { name: "进阶复盘者", emoji: "🌿", condition: standardReviews >= 1, unlocked: standardReviews >= 1 },
    { name: "标准模式熟练者", emoji: "🎯", condition: standardReviews >= 10, unlocked: standardReviews >= 10 },
    { name: "复盘专家", emoji: "🏅", condition: standardReviews >= 30, unlocked: standardReviews >= 30 },
    { name: "持续成长者", emoji: "🌟", condition: totalReviews >= 50, unlocked: totalReviews >= 50 }
];

const unlockedCount = achievements.filter(a => a.unlocked).length;
const totalCount = achievements.length;

dv.paragraph(`**🏆 成就进度：${unlockedCount}/${totalCount}**`);
dv.paragraph("");

achievements.forEach(achievement => {
    const status = achievement.unlocked ? "✅" : "⏸️";
    dv.paragraph(`${status} ${achievement.emoji} ${achievement.name}`);
});
```

### 🎯 下一个目标
```dataviewjs
// 显示下一个可解锁的成就
const beginnerReviews = dv.pages('#beginner-level').where(p => p.review_type === 'daily').length;
const standardReviews = dv.pages('#standard-level').where(p => p.review_type === 'daily').length;
const totalReviews = beginnerReviews + standardReviews;

let nextGoal = "";
if (totalReviews === 0) {
    nextGoal = "🌟 完成第一次复盘，解锁「初次复盘」成就！";
} else if (totalReviews < 3) {
    nextGoal = `🔥 再坚持 ${3 - totalReviews} 天，解锁「坚持三天」成就！`;
} else if (totalReviews < 7) {
    nextGoal = `🏆 再坚持 ${7 - totalReviews} 天，解锁「一周达人」成就！`;
} else if (standardReviews === 0) {
    nextGoal = "🌿 尝试标准级复盘，解锁「进阶复盘者」成就！";
} else if (standardReviews < 10) {
    nextGoal = `🎯 再完成 ${10 - standardReviews} 次标准级复盘，解锁「标准模式熟练者」成就！`;
} else if (standardReviews < 30) {
    nextGoal = `🏅 再完成 ${30 - standardReviews} 次标准级复盘，解锁「复盘专家」成就！`;
} else if (totalReviews < 50) {
    nextGoal = `🌟 再完成 ${50 - totalReviews} 次复盘，解锁「持续成长者」成就！`;
} else {
    nextGoal = "🎉 恭喜！您已经解锁了所有成就，是真正的复盘大师！";
}

dv.paragraph("**🎯 下一个目标：**");
dv.paragraph(nextGoal);
```

---
## 📊 复盘数据统计

### 📈 整体进度
```dataviewjs
// 复盘统计面板
const beginnerReviews = dv.pages('#beginner-level').where(p => p.review_type === 'daily').length;
const standardReviews = dv.pages('#standard-level').where(p => p.review_type === 'daily').length;
const totalReviews = beginnerReviews + standardReviews;
const totalExperience = beginnerReviews * 10 + standardReviews * 15;

// 计算连续复盘天数（简化计算）
const recentReviews = dv.pages().where(p => p.review_type === 'daily' && p.date).sort(p => p.date, 'desc').limit(30);
let streakDays = 0;
if (recentReviews.length > 0) {
    // 简化的连续天数计算
    streakDays = Math.min(recentReviews.length, 7);
}

dv.table(
    ["指标", "数值", "状态"],
    [
        ["📊 总复盘次数", `${totalReviews} 次`, totalReviews >= 30 ? "🌟 优秀" : totalReviews >= 10 ? "👍 良好" : "🌱 起步"],
        ["⭐ 累计经验值", `${totalExperience} 点`, totalExperience >= 500 ? "🏆 专家" : totalExperience >= 100 ? "🎯 进阶" : "🌱 新手"],
        ["🔥 连续复盘", `${streakDays} 天`, streakDays >= 7 ? "🔥 火热" : streakDays >= 3 ? "💪 坚持" : "🌱 开始"],
        ["🌱 入门级", `${beginnerReviews} 次`, beginnerReviews >= 7 ? "✅ 完成" : "🔄 进行中"],
        ["🌿 标准级", `${standardReviews} 次`, standardReviews >= 20 ? "✅ 熟练" : standardReviews > 0 ? "🔄 进行中" : "⏸️ 未开始"]
    ]
);
```

---
## 💡 复盘小贴士

### 🌱 新手指南
- **选择固定时间**：建议每天睡前10分钟
- **从小事开始**：记录生活中的小美好和小进步
- **保持真实**：诚实记录比完美答案更重要
- **坚持优先**：连续性比完美性更重要

### 🌿 进阶技巧
- **量化思维**：尝试用数字和评分衡量表现
- **因果分析**：思考事件背后的原因和影响
- **模式识别**：寻找行为和结果之间的规律
- **目标关联**：将每日行动与长期目标联系

### 🌳 专家心得
- **深层反思**：进行多角度的自我分析
- **系统思考**：建立反馈循环，持续改进
- **知识整合**：将复盘与学习和成长联系
- **价值创造**：让复盘成为人生成长的重要工具

---
## 🔗 快速导航

### 📅 开始复盘
- [[Templates/复盘引导/入门级日复盘|🌱 入门级日复盘]]
- [[Templates/复盘引导/标准级日复盘|🌿 标准级日复盘]]
- [[Templates/5_BuJo - Daily Log|🌳 深度复盘模式]]

### 📊 数据分析
- [[复盘系统/复盘数据仪表盘|📊 复盘数据仪表盘]]
- [[复盘系统/配置/复盘引导配置|⚙️ 引导系统配置]]

### 📚 帮助文档
- [[复盘系统/配置/复盘系统配置|📋 系统配置说明]]
- [[复盘.txt|📖 复盘方法论]]

---
*🎯 复盘引导中心 v1.0 | 让每一天都成为成长的阶梯*
