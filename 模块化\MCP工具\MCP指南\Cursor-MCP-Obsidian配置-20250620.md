# Cursor通过MCP操作Obsidian本地知识库配置任务

## 📋 任务概述

**任务目标**: 配置Cursor通过MCP（Model Context Protocol）实现对Obsidian本地知识库的丝滑操作

**任务背景**:
- 用户希望在Cursor中通过MCP直接操作Obsidian笔记
- 需要安装Obsidian Local REST API插件
- 配置Cursor的MCP JSON配置文件
- 使用uv工具运行mcp-obsidian服务

**预期成果**:
- Obsidian Local REST API插件正常运行
- Cursor MCP配置完成
- 能够通过Cursor AI助手直接操作Obsidian笔记

## 🎯 详细计划

### 阶段1: Obsidian插件配置
1. **安装Local REST API插件**
   - 在Obsidian中启用社区插件
   - 搜索并安装"Local REST API"插件
   - 启用插件

2. **获取API Key**
   - 在插件设置中复制API Key
   - 记录API配置信息（Host、Port）

3. **测试插件功能**
   - 访问插件的在线文档页面
   - 使用API Key进行授权测试

### 阶段2: 环境准备
1. **检查uv工具**
   - 确认uv工具已安装
   - 测试uv命令可用性

2. **检查Python环境**
   - 确认Python环境正常
   - 检查网络连接

### 阶段3: MCP配置
1. **定位Cursor配置文件**
   - Windows: `%APPDATA%/Cursor/claude_desktop_config.json`
   - 检查配置文件是否存在

2. **配置mcp-obsidian**
   - 添加mcp-obsidian服务配置
   - 设置环境变量（API_KEY、HOST、PORT）
   - 使用uvx命令运行已发布版本

3. **配置示例**:
```json
{
  "mcpServers": {
    "mcp-obsidian": {
      "command": "uvx",
      "args": [
        "mcp-obsidian"
      ],
      "env": {
        "OBSIDIAN_API_KEY": "你的API密钥",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```

### 阶段4: 测试验证
1. **重启Cursor**
   - 保存配置后重启Cursor
   - 检查MCP服务是否正常加载

2. **功能测试**
   - 测试列出笔记文件
   - 测试搜索功能
   - 测试读取笔记内容
   - 测试创建/修改笔记

## 🔧 技术要点

### mcp-obsidian功能特性
- `list_files_in_vault`: 列出知识库中的所有文件
- `list_files_in_dir`: 列出指定目录中的文件
- `get_file_contents`: 获取文件内容
- `search`: 搜索匹配文本的文档
- `patch_content`: 在现有笔记中插入内容
- `append_content`: 向文件追加内容
- `delete_file`: 删除文件或目录

### 配置参数说明
- `OBSIDIAN_API_KEY`: Local REST API插件生成的密钥
- `OBSIDIAN_HOST`: 默认为127.0.0.1
- `OBSIDIAN_PORT`: 默认为27124

## ⚠️ 注意事项

1. **插件依赖**: 必须先安装并启用Local REST API插件
2. **网络访问**: 确保本地API服务正常运行
3. **配置路径**: Windows系统配置文件路径为`%APPDATA%/Cursor/claude_desktop_config.json`
4. **重启要求**: 修改配置后需要重启Cursor才能生效

## 📚 参考资源

- [mcp-obsidian GitHub仓库](https://github.com/MarkusPfundstein/mcp-obsidian)
- [Obsidian Local REST API插件](https://github.com/coddingtonbear/obsidian-local-rest-api)
- [MCP Inspector调试工具](https://github.com/modelcontextprotocol/inspector)

## 🎯 成功标准

- [ ] Obsidian Local REST API插件正常运行
- [ ] API Key获取成功
- [x] Cursor MCP配置文件正确
- [ ] mcp-obsidian服务启动成功
- [ ] 能够通过Cursor操作Obsidian笔记
- [ ] 基本功能测试通过（读取、搜索、创建笔记）

## 📝 当前进度

### ✅ 已完成
1. **环境检查完成**
   - 找到Cursor MCP配置文件：`C:\Users\<USER>\AppData\Roaming\Cursor\User\globalStorage\anysphere.cursor\mcp.json`
   - 确认uv工具已安装并可用
   - 现有MCP配置（mcp-feedback-enhanced）正常

2. **文档和脚本创建完成**
   - 详细配置指南：`文档/Obsidian-MCP配置详细指南.md`
   - 快速配置脚本：`配置Obsidian-MCP.ps1`
   - 配置测试脚本：`测试Obsidian-MCP.ps1`
   - 快速说明文档：`Obsidian-MCP快速配置说明.md`

### 🔄 待用户操作
1. **安装Obsidian插件**
   - 在Obsidian中安装"Local REST API"插件
   - 启用插件并获取API Key

2. **运行配置脚本**
   ```powershell
   .\配置Obsidian-MCP.ps1 -ApiKey "你的API密钥"
   ```

3. **重启Cursor并测试**

### 📋 配置模板已准备
```json
{
  "mcpServers": {
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced@latest"],
      "timeout": 600,
      "autoApprove": ["interactive_feedback"]
    },
    "mcp-obsidian": {
      "command": "uvx",
      "args": ["mcp-obsidian"],
      "env": {
        "OBSIDIAN_API_KEY": "你的API密钥",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```
