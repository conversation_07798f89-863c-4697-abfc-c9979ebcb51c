---
tags:
  - tag-management
  - classification
  - review-organization
created: 2025-08-07
updated: 2025-08-07
---
# 🏷️ 复盘标签管理器

> 智能标签系统，让复盘内容井然有序，便于搜索和关联

## 📋 标准标签体系

### 🎯 复盘级别标签
```dataview
TABLE without id
    "🏷️ " + rows.file.tags as "标签",
    length(rows) as "使用次数",
    "📝 " + choice(length(rows) > 10, "高频", choice(length(rows) > 5, "中频", "低频")) as "频率"
FROM "0_Bullet Journal"
WHERE contains(file.tags, "beginner-level") OR contains(file.tags, "standard-level") OR contains(file.tags, "advanced-level")
GROUP BY file.tags
SORT length(rows) DESC
```

### 📅 复盘类型标签
```dataview
TABLE without id
    "🏷️ " + rows.file.tags as "标签",
    length(rows) as "使用次数",
    "📊 " + choice(length(rows) > 20, "高频", choice(length(rows) > 10, "中频", "低频")) as "频率"
FROM "0_Bullet Journal"
WHERE contains(file.tags, "daily-review") OR contains(file.tags, "weekly-review") OR contains(file.tags, "monthly-review")
GROUP BY file.tags
SORT length(rows) DESC
```

---
## 🎨 主题标签分类

### 💼 工作相关标签
```dataviewjs
// 工作相关标签统计
const workTags = [
    "work", "project", "meeting", "deadline", "task", "team", "client",
    "工作", "项目", "会议", "任务", "团队", "客户", "业务"
];

const workTaggedFiles = dv.pages('"0_Bullet Journal"')
    .where(p => {
        const tags = p.file.tags || [];
        return workTags.some(tag => 
            tags.some(fileTag => fileTag.toLowerCase().includes(tag.toLowerCase()))
        );
    });

dv.paragraph(`**💼 工作相关复盘**: ${workTaggedFiles.length} 篇`);

if (workTaggedFiles.length > 0) {
    // 按月份统计工作复盘
    const monthlyWork = {};
    workTaggedFiles.forEach(file => {
        const month = file.file.ctime.toFormat('yyyy-MM');
        monthlyWork[month] = (monthlyWork[month] || 0) + 1;
    });
    
    const sortedMonths = Object.entries(monthlyWork)
        .sort(([a], [b]) => b.localeCompare(a))
        .slice(0, 6);
    
    dv.paragraph("**📈 最近6个月工作复盘趋势：**");
    sortedMonths.forEach(([month, count]) => {
        const bar = "█".repeat(Math.floor(count / 2)) + "░".repeat(5 - Math.floor(count / 2));
        dv.paragraph(`${month}: ${bar} ${count}篇`);
    });
}
```

### 📚 学习成长标签
```dataviewjs
// 学习成长标签统计
const learningTags = [
    "learning", "study", "skill", "knowledge", "book", "course", "growth",
    "学习", "技能", "知识", "书籍", "课程", "成长", "进步"
];

const learningTaggedFiles = dv.pages('"0_Bullet Journal"')
    .where(p => {
        const tags = p.file.tags || [];
        return learningTags.some(tag => 
            tags.some(fileTag => fileTag.toLowerCase().includes(tag.toLowerCase()))
        );
    });

dv.paragraph(`**📚 学习成长复盘**: ${learningTaggedFiles.length} 篇`);

if (learningTaggedFiles.length > 0) {
    // 最近的学习复盘
    const recentLearning = learningTaggedFiles
        .sort(p => p.file.ctime, 'desc')
        .slice(0, 5);
    
    dv.paragraph("**🔥 最近的学习复盘：**");
    recentLearning.forEach(file => {
        const date = file.file.ctime.toFormat('MM-dd');
        dv.paragraph(`- [[${file.file.path}|${date} ${file.file.name}]]`);
    });
}
```

### 🏃 健康生活标签
```dataviewjs
// 健康生活标签统计
const healthTags = [
    "health", "exercise", "fitness", "running", "sleep", "diet", "wellness",
    "健康", "运动", "锻炼", "跑步", "睡眠", "饮食", "养生"
];

const healthTaggedFiles = dv.pages('"0_Bullet Journal"')
    .where(p => {
        const tags = p.file.tags || [];
        return healthTags.some(tag => 
            tags.some(fileTag => fileTag.toLowerCase().includes(tag.toLowerCase()))
        );
    });

dv.paragraph(`**🏃 健康生活复盘**: ${healthTaggedFiles.length} 篇`);

// 健康复盘频率分析
if (healthTaggedFiles.length > 0) {
    const last30Days = 30;
    const cutoffDate = dv.date('today') - dv.duration(`${last30Days} days`);
    const recentHealthReviews = healthTaggedFiles
        .where(p => p.file.ctime >= cutoffDate).length;
    
    const frequency = Math.round(recentHealthReviews / last30Days * 100);
    dv.paragraph(`**📊 最近30天健康复盘频率**: ${frequency}% (${recentHealthReviews}/${last30Days}天)`);
}
```

---
## 🔍 标签搜索工具

### 🎯 按标签搜索复盘
```dataview
TABLE without id
    "📅 " + file.link as "复盘记录",
    file.tags as "标签",
    file.ctime as "创建时间"
FROM "0_Bullet Journal"
WHERE contains(file.tags, "work") OR contains(file.tags, "工作")
SORT file.ctime DESC
LIMIT 10
```

### 📊 标签组合搜索
```dataviewjs
// 多标签组合搜索示例
const searchTags = ["learning", "growth", "学习", "成长"]; // 可以修改这里的搜索标签

const matchingFiles = dv.pages('"0_Bullet Journal"')
    .where(p => {
        const tags = p.file.tags || [];
        return searchTags.some(searchTag => 
            tags.some(fileTag => fileTag.toLowerCase().includes(searchTag.toLowerCase()))
        );
    })
    .sort(p => p.file.ctime, 'desc')
    .limit(15);

dv.paragraph(`**🔍 搜索标签**: ${searchTags.join(", ")}`);
dv.paragraph(`**📊 找到**: ${matchingFiles.length} 篇相关复盘`);

if (matchingFiles.length > 0) {
    dv.paragraph("\n**📋 搜索结果：**");
    matchingFiles.forEach(file => {
        const date = file.file.ctime.toFormat('MM-dd');
        const tags = (file.file.tags || []).slice(0, 3).join(", ");
        dv.paragraph(`- [[${file.file.path}|${date} ${file.file.name}]] (${tags})`);
    });
}
```

---
## 📈 标签使用统计

### 🏆 热门标签排行
```dataviewjs
// 统计所有标签的使用频率
const allFiles = dv.pages('"0_Bullet Journal"')
    .where(p => p.file.name.includes("Daily Log") || 
                p.file.name.includes("Weekly Log") || 
                p.file.name.includes("Monthly Log"));

const tagStats = {};

allFiles.forEach(file => {
    const tags = file.file.tags || [];
    tags.forEach(tag => {
        tagStats[tag] = (tagStats[tag] || 0) + 1;
    });
});

const sortedTags = Object.entries(tagStats)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 15);

dv.paragraph("**🏆 热门标签排行榜：**");
if (sortedTags.length > 0) {
    sortedTags.forEach(([tag, count], index) => {
        const medal = index < 3 ? ["🥇", "🥈", "🥉"][index] : `${index + 1}.`;
        const bar = "█".repeat(Math.floor(count / Math.max(sortedTags[0][1] / 10, 1)));
        dv.paragraph(`${medal} **${tag}**: ${bar} ${count}次`);
    });
} else {
    dv.paragraph("*暂无标签数据，建议在复盘中添加相关标签*");
}
```

### 📊 标签趋势分析
```dataviewjs
// 分析标签使用趋势
const last60Days = dv.pages('"0_Bullet Journal"')
    .where(p => p.file.ctime >= dv.date('today') - dv.duration('60 days') &&
                (p.file.name.includes("Daily Log") || 
                 p.file.name.includes("Weekly Log") || 
                 p.file.name.includes("Monthly Log")));

// 按时间段分组
const early = last60Days.where(p => p.file.ctime < dv.date('today') - dv.duration('30 days'));
const recent = last60Days.where(p => p.file.ctime >= dv.date('today') - dv.duration('30 days'));

// 统计各时期的标签
const earlyTags = {};
const recentTags = {};

early.forEach(file => {
    (file.file.tags || []).forEach(tag => {
        earlyTags[tag] = (earlyTags[tag] || 0) + 1;
    });
});

recent.forEach(file => {
    (file.file.tags || []).forEach(tag => {
        recentTags[tag] = (recentTags[tag] || 0) + 1;
    });
});

// 分析趋势
const trendAnalysis = {};
const allTags = new Set([...Object.keys(earlyTags), ...Object.keys(recentTags)]);

allTags.forEach(tag => {
    const earlyCount = earlyTags[tag] || 0;
    const recentCount = recentTags[tag] || 0;
    const totalCount = earlyCount + recentCount;
    
    if (totalCount >= 2) { // 只分析出现次数较多的标签
        let trend = "→";
        let trendDesc = "稳定";
        
        if (recentCount > earlyCount) {
            trend = "📈";
            trendDesc = "上升";
        } else if (recentCount < earlyCount) {
            trend = "📉";
            trendDesc = "下降";
        }
        
        trendAnalysis[tag] = {
            trend,
            trendDesc,
            early: earlyCount,
            recent: recentCount,
            total: totalCount
        };
    }
});

dv.paragraph("**📈 标签使用趋势 (最近60天)：**");
const sortedTrends = Object.entries(trendAnalysis)
    .sort(([,a], [,b]) => b.total - a.total)
    .slice(0, 10);

if (sortedTrends.length > 0) {
    sortedTrends.forEach(([tag, data]) => {
        dv.paragraph(`**${tag}**: ${data.trend} ${data.trendDesc} (前30天:${data.early}, 后30天:${data.recent})`);
    });
} else {
    dv.paragraph("*数据不足，需要更多带标签的复盘记录*");
}
```

---
## 🛠️ 标签管理工具

### 📝 推荐标签列表
```dataviewjs
// 基于内容推荐标签
const recommendedTags = {
    "复盘级别": ["beginner-level", "standard-level", "advanced-level"],
    "复盘类型": ["daily-review", "weekly-review", "monthly-review", "yearly-review"],
    "工作相关": ["work", "project", "meeting", "task", "team", "client", "business"],
    "学习成长": ["learning", "skill", "knowledge", "book", "course", "growth", "improvement"],
    "健康生活": ["health", "exercise", "fitness", "sleep", "diet", "wellness", "mindfulness"],
    "人际关系": ["relationship", "family", "friends", "social", "communication", "networking"],
    "情绪管理": ["emotion", "mood", "stress", "anxiety", "happiness", "gratitude", "mindset"],
    "目标管理": ["goal", "target", "achievement", "milestone", "progress", "planning"],
    "时间管理": ["time", "productivity", "efficiency", "schedule", "priority", "focus"],
    "财务管理": ["finance", "money", "budget", "investment", "saving", "expense"]
};

dv.paragraph("**📝 推荐标签体系：**");
Object.entries(recommendedTags).forEach(([category, tags]) => {
    dv.paragraph(`\n**${category}**:`);
    const tagList = tags.map(tag => `\`${tag}\``).join(", ");
    dv.paragraph(tagList);
});
```

### 🔧 标签使用指南
```dataviewjs
dv.paragraph("**🔧 标签使用最佳实践：**");
dv.paragraph("1. **保持一致性**: 使用统一的标签命名规范");
dv.paragraph("2. **适度使用**: 每篇复盘建议使用3-5个标签");
dv.paragraph("3. **分层标签**: 使用级别标签 + 主题标签的组合");
dv.paragraph("4. **定期整理**: 定期检查和合并相似的标签");
dv.paragraph("5. **英中结合**: 可以使用中英文标签，便于搜索");

dv.paragraph("\n**📋 标签添加方法：**");
dv.paragraph("- 在YAML front matter中添加: `tags: [tag1, tag2]`");
dv.paragraph("- 在正文中使用: `#tag1 #tag2`");
dv.paragraph("- 使用Obsidian的标签面板管理");
```

---
## 🎯 智能标签建议

```dataviewjs
// 基于用户的复盘内容智能推荐标签
const userFiles = dv.pages('"0_Bullet Journal"')
    .where(p => p.file.name.includes("Daily Log") || 
                p.file.name.includes("Weekly Log") || 
                p.file.name.includes("Monthly Log"))
    .sort(p => p.file.ctime, 'desc')
    .limit(20);

// 分析用户常用的标签
const userTags = {};
userFiles.forEach(file => {
    (file.file.tags || []).forEach(tag => {
        userTags[tag] = (userTags[tag] || 0) + 1;
    });
});

const topUserTags = Object.entries(userTags)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 5)
    .map(([tag]) => tag);

dv.paragraph("**🎯 基于您的使用习惯的标签建议：**");

if (topUserTags.length > 0) {
    dv.paragraph(`**您最常使用的标签**: ${topUserTags.join(", ")}`);
    
    // 基于常用标签推荐相关标签
    const relatedTags = {
        "work": ["project", "meeting", "task", "team"],
        "learning": ["skill", "knowledge", "growth", "book"],
        "health": ["exercise", "sleep", "diet", "wellness"],
        "emotion": ["mood", "stress", "happiness", "gratitude"]
    };
    
    const suggestions = [];
    topUserTags.forEach(tag => {
        if (relatedTags[tag]) {
            suggestions.push(...relatedTags[tag]);
        }
    });
    
    if (suggestions.length > 0) {
        const uniqueSuggestions = [...new Set(suggestions)]
            .filter(tag => !topUserTags.includes(tag))
            .slice(0, 5);
        
        if (uniqueSuggestions.length > 0) {
            dv.paragraph(`**推荐尝试的相关标签**: ${uniqueSuggestions.join(", ")}`);
        }
    }
} else {
    dv.paragraph("*开始使用标签来获得个性化建议！*");
    dv.paragraph("**建议从这些基础标签开始**: `daily-review`, `work`, `learning`, `health`");
}

// 检查标签使用的完整性
const untaggedFiles = userFiles.filter(file => !file.file.tags || file.file.tags.length === 0);
if (untaggedFiles.length > 0) {
    dv.paragraph(`\n**⚠️ 发现 ${untaggedFiles.length} 篇复盘没有标签，建议添加标签以便更好地组织和搜索**`);
}
```

---
## 🔗 相关功能

### 📊 数据分析
- [[复盘系统/复盘搜索系统|🔍 复盘搜索系统]] - 基于标签的智能搜索
- [[复盘系统/复盘模式分析器|🧠 模式分析器]] - 深度数据挖掘
- [[复盘系统/复盘数据仪表盘|📊 数据仪表盘]] - 综合统计

### 🎯 系统导航
- [[复盘系统/复盘引导中心|🎯 复盘引导中心]] - 系统总览
- [[复盘系统/复盘快速启动器|🚀 快速启动器]] - 创建复盘

---
*🏷️ 复盘标签管理器 v1.0 | 让复盘内容井然有序*
