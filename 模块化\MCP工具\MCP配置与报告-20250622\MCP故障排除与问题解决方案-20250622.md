---
created: 2025-06-22 11:25
updated: 2025-06-22 11:25
problem:
  - "MCP故障排除"
subject:
  - "问题解决方案"
importance: "高"
tracking: true
timeliness: "及时"
scenario:
  - "故障诊断"
project:
  - "MCP完整报告"
Area:
  - "技术支持"
content_type: "故障排除指南"
Status: "进行中"
tags:
  - "MCP"
  - "故障排除"
  - "问题解决"
  - "诊断工具"
---

# MCP故障排除与问题解决方案汇总

## 📋 概述

本文档汇总了MCP配置和使用过程中的常见问题及解决方案，包括诊断脚本、修复工具等。基于实际故障排除经验，提供系统化的问题分类和解决流程。

## 🔍 问题分类体系

### 1. 配置问题 (Configuration Issues)
- JSON格式错误
- 环境变量缺失或错误
- 路径配置问题
- API密钥配置错误

### 2. 连接问题 (Connection Issues)
- 网络连接失败
- API服务不可达
- 端口占用或冲突
- 超时问题

### 3. 依赖问题 (Dependency Issues)
- 运行环境缺失
- 包版本冲突
- 工具未安装
- 权限问题

### 4. 兼容性问题 (Compatibility Issues)
- IDE版本兼容性
- 操作系统差异
- Schema验证失败
- 版本不匹配

## 🛠️ 诊断工具库

### 一键修复工具

#### 1. 一键修复Obsidian-MCP.bat
**功能**: 全自动诊断和修复Obsidian MCP配置

**使用方法**:
```batch
# 右键以管理员身份运行
.\一键修复Obsidian-MCP.bat
```

**主要功能**:
- ✅ 自动检查Python环境
- ✅ 验证Obsidian API连接
- ✅ 修复Cursor MCP配置
- ✅ 提供多种启动方式
- ✅ 交互式菜单操作

**适用场景**: 初次配置或配置损坏时的快速修复

#### 2. fix_cursor_mcp_config.py
**功能**: Python配置修复工具

**使用方法**:
```bash
# 基础修复
python fix_cursor_mcp_config.py

# 安装依赖并修复
python fix_cursor_mcp_config.py --install

# 自定义API密钥
python fix_cursor_mcp_config.py --api-key "你的API密钥"

# 自定义vault路径
python fix_cursor_mcp_config.py --vault-path "C:\你的路径"

# 强制更新所有配置
python fix_cursor_mcp_config.py --force
```

**主要功能**:
- 🔍 自动搜索MCP配置文件
- 🔧 修复JSON格式错误
- 📝 补充缺失的配置项
- 🔄 更新环境变量设置
- 📁 复制配置到正确位置

### 诊断工具

#### 1. obsidian_mcp_diagnostic.py
**功能**: 全面系统诊断工具

**使用方法**:
```bash
python obsidian_mcp_diagnostic.py
```

**检查项目**:
- 🖥️ 系统环境和Python版本
- 📦 依赖包安装状态
- 🌐 Obsidian API连接测试
- 🔧 MCP服务器状态检查
- 📄 Cursor配置文件验证

**输出示例**:
```
============================================================
                    1. 系统环境检查
============================================================
操作系统: Windows 10.0.19045
Python版本: 3.11.5
工作目录: C:\Users\<USER>\Desktop\测试库

环境变量检查:
  ✓ OBSIDIAN_API_KEY = a712c319...f6b6
  ✓ OBSIDIAN_HOST = 127.0.0.1
  ✓ OBSIDIAN_PORT = 27124
  ✗ OBSIDIAN_VAULT_PATH 未设置
```

#### 2. 测试Obsidian-MCP.ps1
**功能**: PowerShell配置验证工具

**使用方法**:
```powershell
.\测试Obsidian-MCP.ps1
```

**检查项目**:
- 📁 配置文件存在性检查
- 📋 JSON格式验证
- 🔧 配置项完整性检查
- 🌐 网络连接测试
- 📊 问题总结和建议

## 🚨 常见问题解决方案

### 配置问题

#### 问题1: JSON格式错误
**症状**:
```
Error: Invalid JSON format in mcp.json
```

**原因分析**:
- JSON语法错误（缺少逗号、括号不匹配等）
- 编码问题（BOM、特殊字符）
- 文件损坏

**解决方案**:
```bash
# 方案1: 使用修复工具
python fix_cursor_mcp_config.py --force

# 方案2: 手动验证JSON
cat mcp.json | jq .

# 方案3: 使用在线JSON验证器
# 访问 https://jsonlint.com/ 验证格式
```

**预防措施**:
- 使用专业编辑器编辑JSON文件
- 定期备份正确的配置文件
- 使用配置验证脚本

#### 问题2: 环境变量缺失
**症状**:
```
Error: OBSIDIAN_API_KEY not found
```

**原因分析**:
- 环境变量未设置
- 环境变量值为空
- 作用域问题（用户级vs系统级）

**解决方案**:
```powershell
# PowerShell设置环境变量
$env:OBSIDIAN_API_KEY="你的API密钥"
$env:OBSIDIAN_HOST="127.0.0.1"
$env:OBSIDIAN_PORT="27124"
$env:OBSIDIAN_VAULT_PATH="C:\你的路径"

# 永久设置（重启后仍有效）
[Environment]::SetEnvironmentVariable("OBSIDIAN_API_KEY", "你的API密钥", "User")
```

```bash
# Linux/macOS设置环境变量
export OBSIDIAN_API_KEY="你的API密钥"
export OBSIDIAN_HOST="127.0.0.1"
export OBSIDIAN_PORT="27124"
export OBSIDIAN_VAULT_PATH="/你的路径"

# 永久设置（添加到.bashrc或.zshrc）
echo 'export OBSIDIAN_API_KEY="你的API密钥"' >> ~/.bashrc
```

#### 问题3: API密钥错误
**症状**:
```
HTTP 401: Unauthorized
```

**原因分析**:
- API密钥错误或过期
- Local REST API插件未启用
- 插件配置错误

**解决方案**:
1. **重新获取API密钥**:
   - 打开Obsidian
   - 进入设置 → 社区插件 → Local REST API
   - 复制新的API密钥

2. **验证插件状态**:
   - 确认插件已启用
   - 检查端口设置（默认27124）
   - 重启Obsidian

3. **测试连接**:
```bash
# 使用curl测试
curl -H "Authorization: Bearer 你的API密钥" http://127.0.0.1:27124/vault
```

### 连接问题

#### 问题4: 无法连接到Obsidian API
**症状**:
```
ConnectionError: Failed to connect to 127.0.0.1:27124
```

**诊断步骤**:
```bash
# 1. 检查端口是否开放
netstat -an | findstr 27124

# 2. 测试网络连接
telnet 127.0.0.1 27124

# 3. 使用诊断工具
python obsidian_mcp_diagnostic.py
```

**解决方案**:
1. **确认Obsidian运行状态**:
   - Obsidian必须处于运行状态
   - Local REST API插件必须启用

2. **检查端口配置**:
   - 默认端口：27124
   - 可尝试其他端口：27123, 27125

3. **防火墙设置**:
   - 允许Obsidian通过防火墙
   - 检查杀毒软件拦截

#### 问题5: 超时错误
**症状**:
```
TimeoutError: Request timed out after 30 seconds
```

**解决方案**:
```json
// 在MCP配置中增加超时设置
{
  "mcp-obsidian": {
    "command": "uv",
    "args": ["tool", "run", "mcp-obsidian"],
    "timeout": 600,
    "env": {
      "OBSIDIAN_API_KEY": "你的API密钥"
    }
  }
}
```

### 依赖问题

#### 问题6: uvx命令未找到
**症状**:
```
'uvx' is not recognized as an internal or external command
```

**解决方案**:
```bash
# 安装uv工具
pip install uv

# 验证安装
uv --version

# 如果pip安装失败，使用官方安装脚本
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### 问题7: mcp-obsidian包未安装
**症状**:
```
ModuleNotFoundError: No module named 'mcp_obsidian'
```

**解决方案**:
```bash
# 方案1: 使用pip安装
pip install mcp-obsidian

# 方案2: 使用uv安装
uv pip install mcp-obsidian

# 方案3: 使用修复工具自动安装
python fix_cursor_mcp_config.py --install
```

### 兼容性问题

#### 问题8: Augment IDE Schema验证失败
**症状**:
```
Invalid schema for tool obsidian_get_file_contents:
unknown format "path" ignored in schema
```

**原因分析**:
- Augment IDE的schema验证过于严格
- mcp-obsidian使用了非标准schema格式

**解决方案**:
1. **使用双IDE工作流**:
   - Cursor IDE: 用于Obsidian集成
   - Augment IDE: 用于其他开发工作

2. **简化配置**:
```json
// Augment兼容配置
{
  "mcpServers": {
    "mcp-obsidian": {
      "command": "uvx",
      "args": ["mcp-obsidian"],
      "env": {
        "OBSIDIAN_API_KEY": "你的API密钥"
      }
    }
  }
}
```

3. **使用文件系统工具替代**:
   - 在Augment中使用文件系统工具操作Obsidian文件
   - 避免使用mcp-obsidian特定功能

## 🔧 快速诊断流程

### 标准诊断步骤

#### 第1步: 基础环境检查
```bash
# 检查Python版本
python --version

# 检查必要工具
uv --version
curl --version

# 运行诊断工具
python obsidian_mcp_diagnostic.py
```

#### 第2步: 配置文件验证
```bash
# 验证JSON格式
cat mcp.json | jq .

# 检查配置完整性
.\测试Obsidian-MCP.ps1
```

#### 第3步: 连接测试
```bash
# 测试Obsidian API
curl -H "Authorization: Bearer API密钥" http://127.0.0.1:27124/vault

# 测试MCP服务器
curl http://localhost:3001/health
```

#### 第4步: 修复和重试
```bash
# 自动修复
python fix_cursor_mcp_config.py --install

# 一键修复
.\一键修复Obsidian-MCP.bat
```

### 问题优先级处理

#### 🔴 高优先级问题
1. **配置文件损坏**: 立即使用修复工具
2. **API连接失败**: 检查Obsidian状态和网络
3. **依赖缺失**: 安装必要的包和工具

#### 🟡 中优先级问题
1. **性能问题**: 调整超时设置
2. **兼容性问题**: 使用替代方案
3. **版本冲突**: 更新到兼容版本

#### 🟢 低优先级问题
1. **警告信息**: 记录但不影响功能
2. **非关键配置**: 可选的优化设置
3. **文档问题**: 更新说明文档

## 📋 故障排除检查清单

### 配置前检查
- [ ] Python 3.8+ 已安装
- [ ] uv工具已安装
- [ ] Obsidian已安装并运行
- [ ] Local REST API插件已启用
- [ ] 网络连接正常

### 配置后验证
- [ ] JSON配置文件格式正确
- [ ] 环境变量设置完整
- [ ] API密钥配置正确
- [ ] 路径设置有效
- [ ] 端口配置正确

### 功能测试
- [ ] 基础连接测试通过
- [ ] 文件列表功能正常
- [ ] 文件读取功能正常
- [ ] 搜索功能正常
- [ ] 错误处理正常

### 性能检查
- [ ] 响应时间合理（<5秒）
- [ ] 内存使用正常
- [ ] CPU使用率正常
- [ ] 网络流量正常

## 🔧 PowerShell诊断脚本库

### 1. 配置Obsidian-MCP.ps1
**功能**: 自动配置Obsidian MCP环境

**使用方法**:
```powershell
.\配置Obsidian-MCP.ps1 -ApiKey "你的API密钥"
```

**主要功能**:
- 🔧 自动创建配置文件
- 📝 设置环境变量
- 🔍 验证配置正确性
- 📁 复制到正确位置

### 2. 检查mcp-obsidian执行方式.ps1
**功能**: 检查不同的mcp-obsidian启动方式

**检查项目**:
```powershell
# 检查uvx方式
uvx mcp-obsidian --version

# 检查uv tool run方式
uv tool run mcp-obsidian --version

# 检查Python模块方式
python -m mcp_obsidian --version
```

### 3. 验证Obsidian-API连接.ps1
**功能**: 测试Obsidian API的各种连接方式

**测试内容**:
- 基础连接测试
- 认证测试
- 功能接口测试
- 性能测试

## 🐍 Python工具集

### 1. test_obsidian_mcp_tools.py
**功能**: 测试所有Obsidian MCP工具功能

**测试项目**:
```python
# 测试文件列表
def test_list_files():
    """测试文件列表功能"""

# 测试文件读取
def test_read_file():
    """测试文件读取功能"""

# 测试文件搜索
def test_search_files():
    """测试文件搜索功能"""

# 测试文件创建
def test_create_file():
    """测试文件创建功能"""
```

### 2. run_mcp_obsidian.py
**功能**: 启动mcp-obsidian服务器

**启动选项**:
```python
# 基础启动
python run_mcp_obsidian.py

# 调试模式启动
python run_mcp_obsidian.py --debug

# 自定义端口启动
python run_mcp_obsidian.py --port 3002
```

## 📊 高级故障排除技巧

### 日志分析

#### 1. Cursor IDE日志
**位置**: `%APPDATA%\Cursor\logs\`

**关键日志文件**:
- `main.log`: 主程序日志
- `renderer.log`: 渲染进程日志
- `mcp.log`: MCP相关日志

**分析方法**:
```bash
# 搜索MCP相关错误
grep -i "mcp\|obsidian" main.log

# 查看最近的错误
tail -f main.log | grep -i error
```

#### 2. MCP服务器日志
**获取方法**:
```bash
# 启动时开启详细日志
uv tool run mcp-obsidian --verbose --log-level debug

# 重定向日志到文件
uv tool run mcp-obsidian 2>&1 | tee mcp-obsidian.log
```

### 网络诊断

#### 1. 端口扫描
```bash
# Windows
netstat -an | findstr :27124

# Linux/macOS
netstat -an | grep :27124
lsof -i :27124
```

#### 2. 连接测试
```bash
# 基础连接测试
telnet 127.0.0.1 27124

# HTTP测试
curl -v http://127.0.0.1:27124

# 带认证的测试
curl -H "Authorization: Bearer API密钥" http://127.0.0.1:27124/vault
```

### 性能分析

#### 1. 响应时间测试
```python
import time
import requests

def test_response_time():
    start_time = time.time()
    response = requests.get("http://127.0.0.1:27124/vault",
                          headers={"Authorization": "Bearer API密钥"})
    end_time = time.time()

    print(f"响应时间: {end_time - start_time:.2f}秒")
    print(f"状态码: {response.status_code}")
```

#### 2. 内存使用监控
```powershell
# 监控Obsidian进程内存使用
Get-Process obsidian | Select-Object Name, CPU, WorkingSet

# 监控Python进程内存使用
Get-Process python | Select-Object Name, CPU, WorkingSet
```

## 🚀 自动化修复脚本

### 1. 全自动修复脚本
```bash
#!/bin/bash
# auto_fix_mcp.sh - 全自动MCP修复脚本

echo "🔧 开始自动修复MCP配置..."

# 1. 检查并安装依赖
if ! command -v python &> /dev/null; then
    echo "❌ Python未安装，请先安装Python"
    exit 1
fi

if ! command -v uv &> /dev/null; then
    echo "📦 安装uv工具..."
    pip install uv
fi

# 2. 安装mcp-obsidian
echo "📦 安装mcp-obsidian..."
uv pip install mcp-obsidian

# 3. 运行诊断
echo "🔍 运行诊断..."
python obsidian_mcp_diagnostic.py

# 4. 修复配置
echo "🔧 修复配置..."
python fix_cursor_mcp_config.py --install --force

echo "✅ 自动修复完成！"
```

### 2. 配置备份和恢复
```powershell
# backup_mcp_config.ps1
$backupDir = ".\mcp_backup_$(Get-Date -Format 'yyyyMMdd_HHmmss')"
New-Item -ItemType Directory -Path $backupDir

# 备份配置文件
$configFiles = @(
    "$env:APPDATA\Cursor\User\globalStorage\anysphere.cursor\mcp.json",
    ".\Cursor-完整版MCP配置.json",
    ".\Augment-成功配置.json"
)

foreach ($file in $configFiles) {
    if (Test-Path $file) {
        Copy-Item $file $backupDir
        Write-Host "✅ 已备份: $file"
    }
}

Write-Host "📁 备份完成，位置: $backupDir"
```

## 🎯 特定场景解决方案

### 场景1: 全新安装配置
```bash
# 1. 环境准备
pip install uv requests

# 2. 下载配置工具
curl -O https://raw.githubusercontent.com/user/repo/main/fix_cursor_mcp_config.py

# 3. 运行一键配置
python fix_cursor_mcp_config.py --install --api-key "你的API密钥"

# 4. 验证配置
python obsidian_mcp_diagnostic.py
```

### 场景2: 配置迁移
```powershell
# 从旧电脑迁移配置到新电脑

# 1. 在旧电脑上备份
.\backup_mcp_config.ps1

# 2. 复制备份文件到新电脑

# 3. 在新电脑上恢复
.\restore_mcp_config.ps1 -BackupPath ".\mcp_backup_20250622_112500"

# 4. 更新路径配置
python fix_cursor_mcp_config.py --vault-path "新的vault路径"
```

### 场景3: 多用户环境配置
```bash
# 为多个用户配置MCP

# 1. 创建共享配置模板
cp Cursor-完整版MCP配置.json /shared/mcp_template.json

# 2. 为每个用户生成配置
for user in user1 user2 user3; do
    python fix_cursor_mcp_config.py \
        --api-key "${user}_api_key" \
        --vault-path "/home/<USER>/vault"
done
```

## 📞 技术支持和社区资源

### 官方资源
- **MCP官方文档**: https://modelcontextprotocol.io/
- **mcp-obsidian GitHub**: https://github.com/calclavia/mcp-obsidian
- **Obsidian论坛**: https://forum.obsidian.md/

### 社区支持
- **Discord社区**: MCP开发者频道
- **Reddit**: r/ObsidianMD
- **GitHub Issues**: 各项目的问题跟踪

### 问题反馈模板
```markdown
## 问题描述
[简要描述遇到的问题]

## 环境信息
- 操作系统: [Windows/macOS/Linux + 版本]
- Python版本: [python --version]
- Obsidian版本: [版本号]
- IDE: [Cursor/Augment + 版本]

## 重现步骤
1. [步骤1]
2. [步骤2]
3. [步骤3]

## 错误信息
```
[粘贴完整的错误信息]
```

## 诊断结果
[粘贴 obsidian_mcp_diagnostic.py 的输出]

## 已尝试的解决方案
- [x] 运行了诊断工具
- [x] 尝试了修复脚本
- [ ] 其他方案...
```

## 🔄 持续维护和更新

### 定期检查项目
- **每周**: 运行诊断工具检查系统状态
- **每月**: 更新依赖包和工具版本
- **每季度**: 备份配置文件和重要数据

### 版本更新流程
1. **备份当前配置**
2. **更新相关包和工具**
3. **运行兼容性测试**
4. **更新配置文件**
5. **验证功能正常**

### 监控和告警
```python
# health_check.py - 健康检查脚本
import requests
import smtplib
from datetime import datetime

def check_mcp_health():
    try:
        response = requests.get("http://127.0.0.1:27124/vault",
                              headers={"Authorization": "Bearer API密钥"},
                              timeout=5)
        if response.status_code == 200:
            print(f"✅ {datetime.now()}: MCP服务正常")
            return True
        else:
            print(f"❌ {datetime.now()}: MCP服务异常 - HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {datetime.now()}: MCP服务连接失败 - {e}")
        return False

# 可以设置为定时任务运行
if __name__ == "__main__":
    check_mcp_health()
```

---

**故障排除指南版本**: v1.0
**最后更新**: 2025-06-22
**基于**: 实际故障排除经验和工具测试
**维护周期**: 每月更新一次
