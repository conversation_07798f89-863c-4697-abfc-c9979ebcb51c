# Claude Code 启动脚本
# 直接连接问问AI API 服务

Write-Host "启动 Claude Code..." -ForegroundColor Green
Write-Host "使用 API 地址: https://code.wenwen-ai.com/v1" -ForegroundColor Cyan

# 设置环境变量
$env:CLAUDE_CODE_MAX_OUTPUT_TOKENS = "64000"
$env:ANTHROPIC_BASE_URL = "https://code.wenwen-ai.com/v1"
$env:ANTHROPIC_AUTH_TOKEN = "sk-hzzntp11lud8ep8cT3UC7m2VNLsLpQWZvVAHQ4qS3GuKYxuU"

# 使用 Git Bash 启动 Claude Code
Write-Host "正在启动 Claude Code，请稍候..." -ForegroundColor Yellow
Write-Host ""

try {
    & "C:\Program Files\Git\usr\bin\bash.exe" -c "export SHELL='/usr/bin/bash'; export CLAUDE_CODE_MAX_OUTPUT_TOKENS='$env:CLAUDE_CODE_MAX_OUTPUT_TOKENS'; export ANTHROPIC_BASE_URL='$env:ANTHROPIC_BASE_URL'; export ANTHROPIC_AUTH_TOKEN='$env:ANTHROPIC_AUTH_TOKEN'; claude"
} catch {
    Write-Host "启动过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "Claude Code 已退出。按任意键关闭此窗口..." -ForegroundColor Green
Read-Host
