---
tags:
  - daily-review
  - standard-level
  - progressive-guide
review_level: "standard"
review_type: "daily"
experience_points: 15
created: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
date: <% tp.date.now("YYYY-MM-DD") %>
mood: 
energy_level: 
---
# 🌿 标准级日复盘 - <% tp.date.now("MM月DD日") %>

> 🎯 **进阶模式** | 预计用时：5-8分钟 | 经验值：+15

## 💡 升级提示
恭喜您升级到标准模式！这个版本包含5个核心问题，帮助您更全面地回顾每一天。
- 🎯 更深入的思考和分析
- 📊 可以给自己的表现打分
- 🔗 思考与长期目标的关系

---
## 📝 今日复盘（标准级）

### 🎯 今天最大的成就是什么？
*包括工作成果、个人突破、习惯坚持等，可以量化描述*

**我的回答：**
- 

**成就评分：** ⭐⭐⭐⭐⭐ (1-5星)
**💡 进阶提示：** 尝试分析这个成就对你长期目标的贡献！

---
### 🚧 今天遇到了什么挑战，如何解决的？
*困难、障碍、问题及你的应对方式和解决方案*

**我的回答：**
- 

**解决效果：** ⭐⭐⭐⭐⭐ (1-5星)
**💡 进阶提示：** 思考这个解决方案是否可以应用到类似情况？

---
### 💡 今天有什么新的学习和收获？
*知识、技能、经验、感悟，以及如何应用*

**我的回答：**
- 

**学习价值：** ⭐⭐⭐⭐⭐ (1-5星)
**💡 进阶提示：** 考虑如何将今天的学习转化为明天的行动！

---
### 😊 今天的情绪状态如何，什么影响了你？
*情绪变化、影响因素、情绪管理*

**我的回答：**
- 

**情绪管理：** ⭐⭐⭐⭐⭐ (1-5星)
**💡 进阶提示：** 识别情绪模式，找到更好的情绪调节方法！

---
### 🔧 明天可以在哪些方面做得更好？
*具体的改进计划和行动步骤*

**我的回答：**
- 

**改进可行性：** ⭐⭐⭐⭐⭐ (1-5星)
**💡 进阶提示：** 设定具体的时间和衡量标准！

---
## 📊 今日数据面板

### 🎯 综合评分
```dataviewjs
// 自动计算今日综合表现
const page = dv.current();

// 从YAML front matter获取评分数据
const mood = page.mood || 0;
const energy = page.energy_level || 0;

if (mood > 0 || energy > 0) {
    dv.paragraph("**📊 今日状态指标：**");
    
    if (mood > 0) {
        const moodEmoji = mood >= 8 ? "😄" : mood >= 6 ? "😊" : mood >= 4 ? "😐" : "😔";
        dv.paragraph(`- 心情指数：${moodEmoji} ${mood}/10`);
    }
    
    if (energy > 0) {
        const energyEmoji = energy >= 8 ? "⚡" : energy >= 6 ? "🔋" : energy >= 4 ? "🪫" : "😴";
        dv.paragraph(`- 精力水平：${energyEmoji} ${energy}/10`);
    }
    
    if (mood > 0 && energy > 0) {
        const overall = Math.round((mood + energy) / 2);
        const overallEmoji = overall >= 8 ? "🌟" : overall >= 6 ? "👍" : overall >= 4 ? "👌" : "💪";
        dv.paragraph(`- 综合状态：${overallEmoji} ${overall}/10`);
    }
} else {
    dv.paragraph("*💡 提示：在YAML front matter中添加mood和energy_level字段来显示状态评分*");
}
```

### 📈 进度追踪
```dataviewjs
// 标准级复盘统计
const standardReviews = dv.pages('#standard-level')
    .where(p => p.review_type === 'daily')
    .length;

const beginnerReviews = dv.pages('#beginner-level')
    .where(p => p.review_type === 'daily')
    .length;

const totalReviews = standardReviews + beginnerReviews;
const totalExperience = standardReviews * 15 + beginnerReviews * 10;

dv.paragraph(`🌿 **标准级复盘**: ${standardReviews} 次`);
dv.paragraph(`🌱 **入门级复盘**: ${beginnerReviews} 次`);
dv.paragraph(`📊 **总复盘次数**: ${totalReviews} 次`);
dv.paragraph(`⭐ **累计经验值**: ${totalExperience} 点`);

// 深度模式解锁提示
if (totalExperience >= 500 && standardReviews >= 20) {
    dv.paragraph(`🎉 **恭喜解锁深度模式！** 您已经是复盘专家了！`);
    dv.paragraph(`👉 [[Templates/5_BuJo - Daily Log|体验深度复盘模式]]`);
} else {
    const expNeeded = 500 - totalExperience;
    const reviewsNeeded = Math.max(20 - standardReviews, 0);
    dv.paragraph(`🎯 **深度模式解锁进度**: 还需要 ${expNeeded} 经验值和 ${reviewsNeeded} 次标准复盘`);
}
```

---
## 🏆 成就系统

```dataviewjs
// 标准级成就
const standardReviews = dv.pages('#standard-level').where(p => p.review_type === 'daily').length;
const totalReviews = dv.pages().where(p => p.review_type === 'daily').length;

const achievements = [
    { name: "进阶复盘者", threshold: 1, emoji: "🌿", unlocked: standardReviews >= 1 },
    { name: "标准模式熟练者", threshold: 10, emoji: "🎯", unlocked: standardReviews >= 10 },
    { name: "复盘专家", threshold: 30, emoji: "🏆", unlocked: standardReviews >= 30 },
    { name: "持续成长者", threshold: 50, emoji: "🌟", unlocked: totalReviews >= 50 }
];

dv.paragraph("**🏆 成就收集：**");
achievements.forEach(achievement => {
    const status = achievement.unlocked ? "✅" : "⏸️";
    const current = achievement.name === "持续成长者" ? totalReviews : standardReviews;
    const progress = achievement.unlocked ? "已解锁" : `${current}/${achievement.threshold}`;
    dv.paragraph(`${status} ${achievement.emoji} ${achievement.name} (${progress})`);
});
```

---
## 📚 进阶指南

### 🎯 标准级复盘技巧
1. **量化思维**：尝试用数字和评分来衡量表现
2. **因果分析**：思考事件背后的原因和影响
3. **模式识别**：寻找行为和结果之间的规律
4. **目标关联**：将每日行动与长期目标联系
5. **持续改进**：基于复盘结果制定具体改进计划

### 💡 深度思考提示
- **成就分析**：这个成就体现了我的哪些能力？
- **挑战反思**：类似的挑战我以前是如何处理的？
- **学习应用**：今天的学习如何改变我的认知？
- **情绪管理**：我的情绪模式是什么？如何优化？
- **改进计划**：具体的行动步骤和时间安排是什么？

### 🔄 复盘质量提升
- 使用具体的例子而不是抽象的概念
- 记录数据和事实，不只是感受
- 思考"为什么"而不只是"是什么"
- 制定可执行的改进计划
- 定期回顾之前的复盘内容

---
## 🔗 相关链接
- [[复盘系统/复盘数据仪表盘|📊 复盘数据仪表盘]]
- [[Templates/5_BuJo - Daily Log|🌳 深度复盘模式]]
- [[Templates/复盘引导/入门级日复盘|🌱 入门级复盘]]
- [[复盘系统/配置/复盘引导配置|⚙️ 引导系统配置]]

---
*🌿 标准级复盘 v1.0 | 深入思考，全面成长*
