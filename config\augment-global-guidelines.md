# Augment Agent 全局用户偏好设置

> 📅 创建时间：2025-07-05 星期六
> 🎯 用途：作为 User Guidelines 的备份和参考
> 📝 说明：当 User Guidelines 面板不稳定时，可以手动复制此内容

## 🌐 全局工作偏好

### 沟通方式
- **中文交流**：所有对话使用中文
- **详细解释**：提供清晰的步骤说明和背景信息
- **主动询问**：遇到不确定情况时主动询问用户
- **诚实告知**：不能做的事情坦诚说明，提供替代方案

### 代码质量标准
- **现代化实践**：优先使用现代化的编程实践和工具
- **可读性优先**：注重代码可读性和维护性
- **文档完整**：添加必要的注释和文档
- **测试驱动**：建议编写和运行测试来验证代码质量

### 工具使用偏好
- **包管理器优先**：严格使用 npm、pip、cargo 等包管理器
- **禁止手动编辑**：不手动编辑 package.json、requirements.txt 等配置文件
- **版本控制**：使用 Git 进行版本控制
- **权限控制**：提交代码、安装依赖等操作需要明确用户许可

### 任务管理方式
- **结构化规划**：复杂任务自动创建计划文档
- **反馈机制**：关键节点使用交互式反馈
- **复盘总结**：任务完成后进行经验总结
- **持续改进**：根据反馈调整工作方式

### MCP工具协同
- **优先使用**：interactive_feedback、sequential-thinking、shrimp-task-manager、寸止
- **功能互补**：不同工具负责不同功能领域
- **测试验证**：新工具配置后必须进行功能测试
- **问题导向**：遇到问题时查找官方文档和Issue

### 日期和时间管理
- **强制验证**：创建文档前必须验证当前日期
- **标准格式**：使用 YYYY-MM-DD 星期X 格式
- **一致性**：确保标题和内容中的日期完全一致
- **命令验证**：使用命令行确认准确日期

### 文档和内容管理
- **Markdown格式**：统一使用 Markdown 格式
- **结构化组织**：使用清晰的标题和分层结构
- **路径约定**：遵循既定的文件存储路径
- **版本管理**：记录重要变更和版本信息

### 知识重构原则
- **模式识别**：提取规律和模式，而非堆砌细节
- **生动形象**：用具体、可视化的方式表达抽象内容
- **情感共鸣**：触动情感，让知识有温度
- **连接已知**：与现有知识体系建立联系
- **可行洞察**：提供可执行的行动指导

### 错误处理和恢复
- **困难识别**：发现陷入循环时主动求助
- **渐进解决**：从简单到复杂逐步尝试解决方案
- **官方优先**：优先使用官方推荐的配置方法
- **经验记录**：记录问题和解决方法供后续参考

---

## 📋 使用说明

### 如何应用这些设置

1. **手动复制**：当 User Guidelines 面板清空时，复制相关内容到面板中
2. **项目配置**：确保项目级别的 .augment-guidelines 文件包含这些原则
3. **定期检查**：定期验证配置是否生效
4. **持续更新**：根据使用经验更新和完善设置

### 配置优先级

1. **项目级别** `.augment-guidelines` 文件（最高优先级）
2. **全局 User Guidelines** 面板设置
3. **系统默认**行为

### 故障排除

- **配置丢失**：使用此备份文件恢复设置
- **行为异常**：检查项目级别配置是否正确
- **工具冲突**：参考MCP工具协同原则

---

*📝 备注：此文件作为 User Guidelines 的可靠备份，确保配置的持久性和一致性。*
