{
    // Windows Terminal 美化配置
    // 复制到 Windows Terminal 设置中
    
    "profiles": {
        "defaults": {
            // 字体设置
            "fontFace": "CascadiaCode Nerd Font",
            "fontSize": 12,
            "fontWeight": "normal",
            
            // 外观设置
            "colorScheme": "Campbell Powershell",
            "cursorShape": "bar",
            "cursorColor": "#FFFFFF",
            
            // 透明度和模糊效果
            "opacity": 95,
            "useAcrylic": true,
            "acrylicOpacity": 0.8,
            
            // 背景设置
            "backgroundImage": null,
            "backgroundImageOpacity": 0.3,
            
            // 滚动设置
            "historySize": 9001,
            "snapOnInput": true,
            
            // 其他设置
            "antialiasingMode": "grayscale",
            "bellStyle": "none"
        },
        
        "list": [
            {
                "name": "PowerShell",
                "source": "Windows.Terminal.PowershellCore",
                "startingDirectory": "C:\\Users\\<USER>\\Desktop\\测试库",
                "icon": "ms-appx:///ProfileIcons/{574e775e-4f2a-5b96-ac1e-a2962a402336}.png"
            }
        ]
    },
    
    "schemes": [
        {
            "name": "Dracula",
            "black": "#000000",
            "red": "#ff5555",
            "green": "#50fa7b",
            "yellow": "#f1fa8c",
            "blue": "#bd93f9",
            "purple": "#ff79c6",
            "cyan": "#8be9fd",
            "white": "#bbbbbb",
            "brightBlack": "#555555",
            "brightRed": "#ff5555",
            "brightGreen": "#50fa7b",
            "brightYellow": "#f1fa8c",
            "brightBlue": "#bd93f9",
            "brightPurple": "#ff79c6",
            "brightCyan": "#8be9fd",
            "brightWhite": "#ffffff",
            "background": "#282a36",
            "foreground": "#f8f8f2"
        }
    ],
    
    "actions": [
        {
            "command": {
                "action": "copy",
                "singleLine": false
            },
            "keys": "ctrl+c"
        },
        {
            "command": "paste",
            "keys": "ctrl+v"
        },
        {
            "command": "find",
            "keys": "ctrl+shift+f"
        },
        {
            "command": {
                "action": "splitPane",
                "split": "auto",
                "splitMode": "duplicate"
            },
            "keys": "alt+shift+d"
        }
    ]
}
