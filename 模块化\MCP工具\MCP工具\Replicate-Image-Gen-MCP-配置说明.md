# Replicate Image-Gen-Server MCP 配置说明

## ✅ 配置完成

您的 <PERSON> Desktop 配置文件已成功更新，添加了 Replicate Image-Gen-Server MCP 服务。

## 📁 配置文件位置

**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

## 🔧 当前配置详情

```json
{
  "mcpServers": {
    "together-image-gen": {
      "command": "npx",
      "args": ["together-mcp@latest"],
      "env": {
        "TOGETHER_API_KEY": "652f9821b98564a4b8a3b8ad00048c5b5f9c7df0bd912d97332b87dc9fc2248e"
      },
      "timeout": 600
    },
    "replicate-image-gen": {
      "command": "npx",
      "args": ["@gongrzhe/image-gen-server"],
      "env": {
        "REPLICATE_API_TOKEN": "****************************************",
        "MODEL": "black-forest-labs/flux-schnell"
      },
      "disabled": false,
      "autoApprove": []
    }
  }
}
```

## 🚀 使用步骤

### 1. 复制配置文件
将生成的 `claude_desktop_config.json` 复制到：
```
%APPDATA%\Claude\claude_desktop_config.json
```

### 2. 重启 Claude Desktop
- 完全关闭 Claude Desktop 应用
- 重新启动 Claude Desktop

### 3. 验证配置
重启后，在 Claude 对话中输入：
```
请帮我生成一张图片：一只可爱的小猫在花园里玩耍
```

## 💰 费用说明

### FLUX Schnell 模型费用
- **价格**: $3.00 / 1000张图片
- **单张成本**: 约 $0.003 (约 0.02元人民币)
- **特点**: 速度快，质量好，性价比高

### 使用成本示例
- 生成 10 张图片 ≈ $0.03 (约 0.2元)
- 生成 100 张图片 ≈ $0.30 (约 2.1元)
- 生成 1000 张图片 ≈ $3.00 (约 21元)

## 🛠️ 可用参数

### generate_image 工具参数
- **prompt** (必需): 图像描述文本
- **seed** (可选): 随机种子，用于可重现生成
- **aspect_ratio** (可选): 宽高比
  - "1:1" (默认)
  - "16:9", "9:16"
  - "4:3", "3:4"
  - "3:2", "2:3"
- **output_format** (可选): 输出格式
  - "webp" (默认)
  - "jpg", "png"
- **num_outputs** (可选): 生成图像数量 (1-4, 默认: 1)

## 📝 使用示例

### 基础使用
```
生成一张图片：夕阳下的山脉风景
```

### 高级使用
```
请使用以下参数生成图片：
- 描述：现代简约风格的客厅设计
- 宽高比：16:9
- 格式：png
- 数量：2张
```

## ⚠️ 注意事项

1. **网络要求**: 需要稳定的网络连接访问 Replicate API
2. **费用控制**: 建议先小额充值测试使用
3. **安全设置**: `autoApprove` 设为空数组，每次生成都需要确认
4. **模型选择**: 默认使用 flux-schnell，性价比最高

## 🔧 故障排除

### 如果遇到问题：

1. **检查网络连接**
2. **确认 API Token 有效**
3. **检查 Replicate 账户余额**
4. **重启 Claude Desktop**
5. **查看 Claude Desktop 日志**

### 常见错误：
- `API Token invalid`: 检查 Token 是否正确
- `Insufficient credits`: 需要充值 Replicate 账户
- `Network timeout`: 检查网络连接

## 📞 支持

如有问题，可以：
1. 检查 [Replicate 官方文档](https://replicate.com/docs)
2. 查看 [Image-Gen-Server GitHub](https://github.com/GongRzhe/Image-Generation-MCP-Server)
3. 检查 Replicate 账户状态

---

**配置完成！现在您可以在 Claude 中直接生成高质量图片了！** 🎨
