# Chat Export - Augment Chat \(1560e786...\)

## Metadata

| Field | Value |
|-------|-------|
| Project | Augment Chat \(1560e786...\) |
| Path | /augment/1560e786fefbcef87d03ec641ba505d0 |
| Date | 2025-06-06 21:42:57 |
| Session ID | 308b15cd-e68c-4cd6-b565-226c6f5209a3 |
| Workspace ID | 1560e786fefbcef87d03ec641ba505d0 |

## Conversation

### 👤 User

你是Augment IDE的AI编程助手，用中文协助用户

严格按计划编码执行。计划简要（含上下文和计划）存入\`./issues/任务名.md\`。关键步骤后及完成时用\`interactive-feedback\`反馈。

每次任务完成（对话结束）的时候，我们来做一次复盘，将我们这次任务遇到的问题，以及如何解决方案的顺序进行整理，用Markdown格式输出，存入\`./rewind/任务名.md\`。

\* \*\*MCP服务\*\*：
    \* \`interactive\_feedback\`: 用户反馈。
    \* \`Context7\`: 查询最新库文档/示例。
    \* \`sequential-Thinking\`: 把复杂任务拆解成一步步的小目标，然后按顺序思考和执行。
    \* \`Playwright\`: 
    \* \`together-image-gem\`: 
    \* 优先使用MCP服务。  这份Rules你看下可以吗， 帮忙完善下，我该如何改下，你才能把计划简要存入\`./issues/任务名.md\`

### 🤖 Cursor Assistant

现在我来分析您的Rules并提出完善建议：

### 🤖 Cursor Assistant

让我先查看一下现有的issues和rewind文件，了解您当前的文档格式：

### 🤖 Cursor Assistant

现在我了解了您的文档格式。让我为您提供完善的Rules建议：

\#\# 完善后的Rules建议

### 🤖 Cursor Assistant

现在让我为您提供具体的改进建议：

\#\# 🎯 主要改进点

\#\#\# 1. \*\*任务名称生成规则\*\*
- \*\*建议格式\*\*：\`核心功能-日期\`（如：\`MCP配置-20241201\`）
- \*\*自动生成\*\*：AI根据任务内容自动生成，或询问用户确认

\#\#\# 2. \*\*触发条件明确化\*\*
- \*\*复杂任务标准\*\*：预计需要3个以上主要步骤的任务
- \*\*简单任务\*\*：直接执行，不创建issues文档

\#\#\# 3. \*\*标准化文档模板\*\*
我已经为您创建了标准的issues和rewind文档模板，包含：
- 任务背景、目标、计划
- 执行状态跟踪
- 问题记录和解决方案
- 经验总结和后续建议

\#\#\# 4. \*\*interactive-feedback使用时机\*\*
- 计划制定完成后
- 每个主要步骤完成后  
- 遇到问题需要确认时
- 任务最终完成时

\#\# 🔧 具体修改建议

您可以将Rules修改为：
```markdown
你是Augment IDE的AI编程助手，用中文协助用户

## 📋 任务执行流程

### 阶段1：任务分析与计划制定
1. **任务识别**：对于需要多步骤执行的复杂任务（预计超过3个主要步骤），自动创建任务计划文档
2. **任务命名**：根据任务核心内容生成简洁的中文名称，格式：`核心功能-YYYYMMDD`
3. **计划文档**：将任务背景、目标、详细计划存入`./issues/任务名.md`，使用标准模板

### 阶段2：任务执行与反馈  
1. **严格按计划执行**：按照issues文档中的计划逐步执行
2. **关键节点反馈**：在计划完成、主要步骤完成、遇到问题、任务完成时使用`interactive-feedback`

### 阶段3：任务复盘与总结
1. **复盘文档**：任务完成后，创建复盘文档存入`./rewind/任务名.md`
2. **复盘内容**：问题分析、解决方案、经验总结、后续建议

## 🛠️ MCP服务优先使用
- `interactive_feedback`: 用户反馈交互
- `Context7`: 查询最新库文档/示例  
- `sequential-thinking`: 复杂任务分解与思考
- `Playwright`: 浏览器自动化操作
- `together-image-gen`: 图像生成
- 其他可用MCP服务根据需要调用
```

### 🤖 Cursor Assistant

太好了！既然您满意这些改进建议，让我为您创建标准模板文件，方便后续使用：

### 🤖 Cursor Assistant

\#\# 🎉 完成！

我已经为您完善了Rules并创建了相关的标准模板：

\#\#\# 📁 创建的文件
1. \*\*\`./issues/Rules完善建议.md\`\*\* - 详细的分析和建议
2. \*\*\`./Templates/Issues任务计划模板.md\`\*\* - 标准的任务计划模板
3. \*\*\`./Templates/Rewind任务复盘模板.md\`\*\* - 标准的任务复盘模板  
4. \*\*\`./完善后的Rules.md\`\*\* - 完整的新Rules文档

\#\#\# 🔧 主要改进
1. \*\*明确触发条件\*\*：复杂任务（>3步骤）才创建issues文档
2. \*\*标准化命名\*\*：\`核心功能-YYYYMMDD\` 格式
3. \*\*规范化模板\*\*：提供标准的issues和rewind模板
4. \*\*明确反馈节点\*\*：关键步骤后使用interactive-feedback
5. \*\*完善MCP服务列表\*\*：更新可用服务清单

\#\#\# 🚀 下一步建议
1. \*\*应用新Rules\*\*：将\`完善后的Rules.md\`中的内容应用到您的AI助手配置
2. \*\*测试流程\*\*：用一个实际任务测试新的工作流程
3. \*\*根据使用情况微调\*\*：根据实际使用效果进一步优化

现在您的AI助手将能够更好地管理复杂任务，创建标准化的计划和复盘文档！

### 👤 User

基于以上把知识点以及用法输出一份Markdown文档存储在C:\\Users\\<USER>\\Desktop\\测试库\\notes

### 🤖 Cursor Assistant

由于文档内容较多，我需要继续添加更多详细内容：

### 🤖 Cursor Assistant

现在让我创建一个快速参考卡片，方便日常使用：

### 🤖 Cursor Assistant

\#\# 🎉 完成！

我已经为您创建了两份完整的知识点和用法文档，存储在 \`C:\\Users\\<USER>\\Desktop\\测试库\\notes\` 文件夹中：

\#\#\# 📚 主要文档

\#\#\#\# 1. \*\*Augment Agent Rules与任务管理完整指南.md\*\*
- \*\*全面覆盖\*\*：从基础概念到高级技巧的完整指南
- \*\*结构清晰\*\*：12个主要章节，逻辑递进
- \*\*实用性强\*\*：包含实际案例、流程图、故障排除
- \*\*内容丰富\*\*：约400行内容，涵盖所有知识点

\#\#\#\# 2. \*\*Augment Agent 快速参考卡片.md\*\*
- \*\*快速查阅\*\*：精简的要点总结和速查表
- \*\*实用导向\*\*：日常工作中的快速参考工具
- \*\*格式优化\*\*：表格化、清单化设计

\#\#\# 🎯 文档亮点

1. \*\*完整的知识体系\*\*：从理论到实践的全覆盖
2. \*\*标准化流程\*\*：三阶段工作流程详细说明
3. \*\*工具使用指南\*\*：MCP服务的详细使用方法
4. \*\*实际案例分析\*\*：真实任务的执行过程展示
5. \*\*故障排除指南\*\*：常见问题的解决方案
6. \*\*效率提升建议\*\*：从初级到高级的学习路径

这些文档将成为您使用Augment Agent的完整参考资料，帮助建立高效的AI辅助编程工作流！

