# Augment IDE MCP 兼容性指南

## 兼容性状态总览

基于实际测试，以下是各MCP服务器在Augment IDE中的兼容性状态：

### ✅ 完全兼容
- **mcp-obsidian** - Obsidian集成
- **context7** - 库文档查询
- **sequential-thinking** - 思维链工具
- **playwright** - 浏览器自动化
- **shrimp-task-manager** - 任务管理
- **replicate-flux-mcp** - 图像生成
- **together-image-gen** - 图像生成
- **mcp-feedback-enhanced** - 反馈收集

### ❌ 兼容性问题
- **fetch/mcp-server-fetch** - Schema格式不兼容

### 🔄 替代方案

#### 网页内容获取替代方案

1. **内置web-fetch工具**
   ```
   请获取 https://example.com 的内容
   ```

2. **Playwright MCP**
   ```
   请使用playwright访问 https://example.com 并获取内容
   ```

3. **web-search + 内容获取组合**
   ```
   请搜索相关信息，然后获取详细内容
   ```

## 推荐配置

### 当前最佳配置
```json
{
  "mcpServers": {
    "mcp-obsidian": {
      "command": "uvx",
      "args": ["mcp-obsidian"],
      "env": {
        "OBSIDIAN_API_KEY": "your-key",
        "OBSIDIAN_HOST": "https://127.0.0.1:27124/"
      }
    },
    "context7": {
      "command": "npx",
      "args": ["context7"]
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["sequential-thinking"]
    },
    "playwright": {
      "command": "npx",
      "args": ["@executeautomation/playwright-mcp-server"],
      "env": {
        "PLAYWRIGHT_API_KEY": "your-key"
      }
    },
    "shrimp-task-manager": {
      "command": "npx",
      "args": ["shrimp-task-manager"]
    },
    "replicate-flux-mcp": {
      "command": "uvx",
      "args": ["replicate-flux-mcp"],
      "env": {
        "REPLICATE_API_TOKEN": "your-token"
      }
    },
    "together-image-gen": {
      "command": "uvx",
      "args": ["together-image-gen"],
      "env": {
        "TOGETHER_API_KEY": "your-key"
      }
    },
    "mcp-feedback-enhanced": {
      "command": "uvx",
      "args": ["mcp-feedback-enhanced"]
    }
  }
}
```

## 故障排除

### Schema错误
如果遇到类似错误：
```
invalid schema for tool fetch: unknown format "uri"
```

**解决方案：**
1. 移除有问题的MCP服务器
2. 使用替代工具
3. 等待Augment更新

### 连接问题
如果MCP服务器无法启动：
1. 检查命令是否正确安装
2. 验证环境变量设置
3. 查看Augment日志

## 开发建议

### 对于MCP开发者
- 避免使用"uri"格式的schema
- 测试与Augment的兼容性
- 提供多种安装方式

### 对于用户
- 优先使用验证兼容的MCP
- 保持工具更新
- 使用替代方案

## 未来展望

### 可能的改进
- Augment改进schema支持
- 更多MCP服务器适配Augment
- 社区提供兼容性补丁

### 建议
- 关注Augment更新
- 参与社区讨论
- 反馈兼容性问题

---

**总结**：虽然某些MCP存在兼容性问题，但Augment提供了丰富的替代方案和内置工具。
