---
tags:
  - type/dashboard
  - dashboard/search-fixed
  - obsidian/search
created: 2025-07-11T13:30
updated: 2025-07-11T13:30
---

# 🔧 修复版搜索系统

```dataviewjs
// 修复版搜索系统 - 正确处理单个关键词和多个关键词
const container = this.container;

// 创建主容器
const mainDiv = document.createElement('div');
mainDiv.style.cssText = `
    font-family: var(--font-interface);
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
`;

// 搜索区域
const searchDiv = document.createElement('div');
searchDiv.style.cssText = `
    background: var(--background-secondary);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--background-modifier-border);
`;

// 标题
const title = document.createElement('h3');
title.textContent = '🔧 修复版搜索系统';
title.style.cssText = 'margin: 0 0 15px 0; color: var(--text-normal);';

// 输入区域
const inputDiv = document.createElement('div');
inputDiv.style.cssText = 'display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;';

// 搜索输入框
const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.placeholder = '输入关键词（如：任务管理）或多个关键词（如：MCP 配置）...';
searchInput.style.cssText = `
    flex: 1;
    min-width: 300px;
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
`;

// 模式选择
const modeSelect = document.createElement('select');
modeSelect.style.cssText = `
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
`;

const modes = [
    { value: 'SINGLE', text: '单个关键词' },
    { value: 'OR', text: 'OR (任一)' },
    { value: 'AND', text: 'AND (所有)' },
    { value: 'EXACT', text: '精确匹配' }
];

modes.forEach(mode => {
    const option = document.createElement('option');
    option.value = mode.value;
    option.textContent = mode.text;
    modeSelect.appendChild(option);
});

// 按钮和说明
const buttonDiv = document.createElement('div');
buttonDiv.style.cssText = 'text-align: center;';

const searchBtn = document.createElement('button');
searchBtn.textContent = '🔍 搜索';
searchBtn.style.cssText = `
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
`;

const clearBtn = document.createElement('button');
clearBtn.textContent = '🗑️ 清空';
clearBtn.style.cssText = `
    background: var(--background-modifier-border);
    color: var(--text-normal);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
`;

// 说明文字
const helpDiv = document.createElement('div');
helpDiv.style.cssText = 'font-size: 12px; color: var(--text-muted); margin-top: 10px;';
helpDiv.innerHTML = `
    <strong>搜索模式说明：</strong><br>
    • <strong>单个关键词</strong>：将整个输入作为一个关键词搜索（如"任务管理"）<br>
    • <strong>OR (任一)</strong>：包含任意一个词即可（如"MCP 配置"中包含"MCP"或"配置"）<br>
    • <strong>AND (所有)</strong>：必须包含所有词（如"MCP 配置"中必须同时包含"MCP"和"配置"）<br>
    • <strong>精确匹配</strong>：完全匹配整个短语（如完整的"MCP 配置"短语）
`;

// 组装搜索区域
inputDiv.appendChild(searchInput);
inputDiv.appendChild(modeSelect);
buttonDiv.appendChild(searchBtn);
buttonDiv.appendChild(clearBtn);

searchDiv.appendChild(title);
searchDiv.appendChild(inputDiv);
searchDiv.appendChild(buttonDiv);
searchDiv.appendChild(helpDiv);

// 结果区域
const resultDiv = document.createElement('div');
resultDiv.style.cssText = `
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    padding: 20px;
    background: var(--background-primary);
    min-height: 200px;
`;

const resultTitle = document.createElement('h3');
resultTitle.textContent = '📋 搜索结果';
resultTitle.style.cssText = 'margin: 0 0 15px 0; color: var(--text-normal);';

const resultContent = document.createElement('div');
resultContent.innerHTML = `
    <div style="text-align: center; color: var(--text-muted); padding: 40px;">
        <div style="font-size: 48px; margin-bottom: 10px;">🔧</div>
        <div style="font-size: 18px;">修复版搜索系统准备就绪</div>
    </div>
`;

resultDiv.appendChild(resultTitle);
resultDiv.appendChild(resultContent);

// 组装主界面
mainDiv.appendChild(searchDiv);
mainDiv.appendChild(resultDiv);
container.appendChild(mainDiv);

// 搜索函数
async function performFixedSearch() {
    const keyword = searchInput.value.trim();
    const mode = modeSelect.value;
    
    if (!keyword) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                <div>请输入搜索关键词</div>
            </div>
        `;
        return;
    }
    
    // 显示加载状态
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
            <div>正在搜索中...</div>
        </div>
    `;
    
    try {
        // 获取所有页面
        let allPages = dv.pages();
        const excludeDirs = ['.obsidian'];
        allPages = allPages.where(p => 
            !excludeDirs.some(dir => p.file.path.includes(dir))
        );
        
        // 根据模式处理关键词
        let searchTerms;
        if (mode === 'SINGLE') {
            // 单个关键词模式：整个输入作为一个关键词
            searchTerms = [keyword];
        } else {
            // 多关键词模式：按空格分割
            searchTerms = keyword.split(/\s+/).filter(k => k.length > 0);
        }
        
        const results = [];
        
        for (const page of allPages) {
            try {
                const content = await dv.io.load(page.file.path);
                const fileName = page.file.name.replace('.md', '');
                
                let matched = false;
                let matchType = '';
                let snippet = '';
                
                // 检查文件名匹配
                if (isFixedMatch(fileName, searchTerms, mode)) {
                    matched = true;
                    matchType = '文件名';
                    snippet = fileName;
                }
                
                // 检查内容匹配
                if (!matched && isFixedMatch(content, searchTerms, mode)) {
                    matched = true;
                    matchType = '文件内容';
                    snippet = getFixedSnippet(content, searchTerms);
                }
                
                if (matched) {
                    results.push({
                        name: fileName,
                        path: page.file.path,
                        link: page.file.link,
                        matchType: matchType,
                        snippet: snippet,
                        mtime: page.file.mtime
                    });
                }
            } catch (error) {
                // 跳过无法读取的文件
            }
        }
        
        displayFixedResults(results, searchTerms, mode);
        
    } catch (error) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-error); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div>搜索失败，请重试</div>
            </div>
        `;
    }
}

// 修复的匹配函数
function isFixedMatch(text, searchTerms, mode) {
    if (!text || !searchTerms || searchTerms.length === 0) return false;
    
    const lowerText = text.toLowerCase();
    const lowerTerms = searchTerms.map(term => term.toLowerCase());
    
    switch (mode) {
        case 'SINGLE':
            // 单个关键词：直接搜索整个词
            return lowerText.includes(lowerTerms[0]);
            
        case 'AND':
            // AND模式：必须包含所有词
            return lowerTerms.every(term => lowerText.includes(term));
            
        case 'OR':
            // OR模式：包含任意一个词
            return lowerTerms.some(term => lowerText.includes(term));
            
        case 'EXACT':
            // 精确匹配：完全匹配整个短语
            const exactPhrase = searchTerms.join(' ').toLowerCase();
            return lowerText.includes(exactPhrase);
            
        default:
            return lowerText.includes(lowerTerms[0]);
    }
}

// 修复的片段提取函数
function getFixedSnippet(content, searchTerms, maxLength = 150) {
    const lines = content.split('\n');
    const lowerTerms = searchTerms.map(term => term.toLowerCase());
    
    for (const line of lines) {
        const lowerLine = line.toLowerCase();
        if (lowerTerms.some(term => lowerLine.includes(term))) {
            return line.length > maxLength ? line.substring(0, maxLength) + '...' : line;
        }
    }
    
    return content.substring(0, maxLength) + '...';
}

// 显示结果
function displayFixedResults(results, searchTerms, mode) {
    let modeDescription = '';
    switch (mode) {
        case 'SINGLE':
            modeDescription = `单个关键词搜索: "${searchTerms[0]}"`;
            break;
        case 'AND':
            modeDescription = `必须包含所有关键词: ${searchTerms.join(', ')}`;
            break;
        case 'OR':
            modeDescription = `包含任意关键词: ${searchTerms.join(', ')}`;
            break;
        case 'EXACT':
            modeDescription = `精确匹配短语: "${searchTerms.join(' ')}"`;
            break;
    }
    
    if (results.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">未找到匹配的笔记</div>
                <div style="font-size: 14px;">${modeDescription}</div>
            </div>
        `;
        return;
    }
    
    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: var(--background-secondary); border-radius: 6px; border-left: 4px solid var(--interactive-accent);">
            <strong>🎯 找到 ${results.length} 个匹配结果</strong><br>
            <span style="color: var(--text-muted); font-size: 14px;">${modeDescription}</span>
        </div>
    `;
    
    results.forEach(result => {
        const date = new Date(result.mtime).toLocaleDateString('zh-CN');
        const pathParts = result.path.split('/');
        const directory = pathParts.length > 1 ? pathParts[0] : '根目录';
        
        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 8px; margin-bottom: 15px; padding: 15px; background: var(--background-secondary);">
                <h4 style="margin: 0 0 8px 0; color: var(--text-normal);">
                    <a href="${result.link}" style="text-decoration: none; color: var(--link-color); font-weight: bold;">
                        📄 ${result.name}
                    </a>
                </h4>
                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 8px;">
                    <span style="margin-right: 15px;">📁 ${directory}</span>
                    <span style="margin-right: 15px;">📅 ${date}</span>
                    <span>🔍 匹配: ${result.matchType}</span>
                </div>
                <div style="background: var(--background-primary); padding: 10px; border-radius: 4px; border-left: 3px solid var(--color-green);">
                    <div style="font-size: 13px; color: var(--text-normal); line-height: 1.4;">
                        ${highlightFixedText(result.snippet, searchTerms)}
                    </div>
                </div>
            </div>
        `;
    });
    
    resultContent.innerHTML = html;
}

// 高亮关键词
function highlightFixedText(text, searchTerms) {
    let highlighted = text;
    searchTerms.forEach(term => {
        const regex = new RegExp(`(${term})`, 'gi');
        highlighted = highlighted.replace(regex, '<mark style="background: var(--text-highlight-bg); color: var(--text-normal);">$1</mark>');
    });
    return highlighted;
}

// 清空结果
function clearResults() {
    searchInput.value = '';
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 10px;">🔧</div>
            <div style="font-size: 18px;">修复版搜索系统准备就绪</div>
        </div>
    `;
}

// 事件绑定
setTimeout(() => {
    searchBtn.onclick = performFixedSearch;
    clearBtn.onclick = clearResults;
    
    searchInput.onkeypress = function(e) {
        if (e.key === 'Enter') {
            performFixedSearch();
        }
    };
}, 100);
```

## 🔧 修复说明

### 主要改进：
1. **新增"单个关键词"模式**：将整个输入作为一个完整的关键词
2. **明确区分单词和短语**：
   - "任务管理" → 单个关键词模式
   - "MCP 配置" → 多关键词模式

### 四种搜索模式：
- **单个关键词**：搜索"任务管理"这个完整词汇
- **OR (任一)**：包含"MCP"或"配置"任意一个
- **AND (所有)**：同时包含"MCP"和"配置"
- **精确匹配**：完全匹配"MCP 配置"这个短语

现在"任务管理"在不同模式下应该有明显区别了！

---

*💡 现在可以正确处理单个关键词和多个关键词的搜索了！*
