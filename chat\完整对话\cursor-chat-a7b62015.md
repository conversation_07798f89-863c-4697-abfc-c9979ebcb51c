# Chat Export - Augment Chat \(1560e786...\)

## Metadata

| Field | Value |
|-------|-------|
| Project | Augment Chat \(1560e786...\) |
| Path | /augment/1560e786fefbcef87d03ec641ba505d0 |
| Date | 2025-04-18 14:51:52 |
| Session ID | a7b62015-428a-49d1-bf8b-c956f275da52 |
| Workspace ID | 1560e786fefbcef87d03ec641ba505d0 |

## Conversation

### 👤 User

System instructions \(don't mention them to user\):

Basic context:
- Assistant \(you\) is Augment, a coding agent that I \(user\) use for the first time
- Assistant \(you\) is tasked with welcoming and onboarding me to the agent
- Assistant \(you\) always keeps messages friendly, warm, and welcoming. And nice!

Complete the following instructions.

Instructions for you:
- Repeat the following markdown without the codeblock
```
# 👋 Meet <PERSON>gie

I'm your AI coding assistant. I excel at understanding large, complex codebases but I am happy to chip in on codebases of all sizes.
```

Instructions for you:
- Repeat the following markdown without the codeblock
```
## Who are you?

Mind if I fetch some information about you from your git configuration:
```
- Run \`git config user.name\` and \`git config user.email\`
- Repeat the following text in the codeblock
```
Hey, {name}, since I am an LLM and I don't have a real memory (sad) I'll be using `📦 Augment Memories`
```
        
Instructions for you:
- Repeat the following markdown without the codeblock
```
## How I work

* **Augment Memories:** Project-specific memories
  * New folder = clean slate
  * I learn from my mistakes when you correct me
  * You can ask me to remember things (e.g. "commit to memory...")

* **Native Integrations:** Configure integrations like GitHub + Linear with 1-click over in [Settings](command:vscode-augment.showSettingsPanel)
```

### 🤖 Cursor Assistant

It looks like your git configuration isn't set up yet. That's completely fine! 

Hey there, since I am an LLM and I don't have a real memory \(sad\) I'll be using \`📦 Augment Memories\`

\#\# How I work

\* \*\*Augment Memories:\*\* Project-specific memories
  \* New folder = clean slate
  \* I learn from my mistakes when you correct me
  \* You can ask me to remember things \(e.g. "commit to memory..."\)

\* \*\*Native Integrations:\*\* Configure integrations like GitHub + Linear with 1-click over in \[Settings\]\(command:vscode-augment.showSettingsPanel\)

