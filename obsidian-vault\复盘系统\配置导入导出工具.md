---
tags:
  - config-import-export
  - backup-restore
  - config-sharing
created: 2025-08-07
updated: 2025-08-07
---
# 📦 配置导入导出工具

> 备份、恢复和分享复盘系统配置，确保配置安全和便于迁移

## 📤 配置导出

### 🔄 当前配置导出
```dataviewjs
// 读取配置管理中心的配置
const configFile = dv.pages('"复盘系统"').where(p => p.file.name === "配置管理中心").first();

if (configFile && configFile.user_config) {
    const config = configFile.user_config;
    
    // 生成导出数据
    const exportData = {
        export_info: {
            version: "1.0",
            export_date: new Date().toISOString(),
            export_source: "复盘系统配置管理中心",
            description: "复盘系统个性化配置备份"
        },
        user_config: config,
        metadata: {
            total_daily_dimensions: config.daily_dimensions?.length || 0,
            enabled_daily_dimensions: config.daily_dimensions?.filter(d => d.enabled).length || 0,
            total_weekly_dimensions: config.weekly_dimensions?.length || 0,
            enabled_weekly_dimensions: config.weekly_dimensions?.filter(d => d.enabled).length || 0,
            theme: config.display_settings?.theme || "default",
            language: config.display_settings?.language || "zh-CN"
        }
    };
    
    dv.paragraph("**📋 配置导出数据：**");
    dv.paragraph("```json");
    dv.paragraph(JSON.stringify(exportData, null, 2));
    dv.paragraph("```");
    
    // 显示导出统计
    dv.paragraph("\n**📊 导出统计：**");
    dv.table(
        ["项目", "数量", "说明"],
        [
            ["日复盘维度", `${exportData.metadata.enabled_daily_dimensions}/${exportData.metadata.total_daily_dimensions}`, "已启用/总数"],
            ["周复盘维度", `${exportData.metadata.enabled_weekly_dimensions}/${exportData.metadata.total_weekly_dimensions}`, "已启用/总数"],
            ["界面主题", exportData.metadata.theme, "当前使用的主题"],
            ["界面语言", exportData.metadata.language, "当前使用的语言"],
            ["导出时间", new Date(exportData.export_info.export_date).toLocaleString(), "配置导出的时间"]
        ]
    );
    
    dv.paragraph("\n**💾 使用方法：**");
    dv.paragraph("1. 复制上面的JSON配置数据");
    dv.paragraph("2. 保存到文本文件中作为备份");
    dv.paragraph("3. 可以分享给其他用户使用");
    dv.paragraph("4. 使用下方的导入功能恢复配置");
    
} else {
    dv.paragraph("❌ **无法导出配置**");
    dv.paragraph("原因：未找到配置管理中心文件或配置数据为空");
    dv.paragraph("请确保 [[复盘系统/配置管理中心]] 文件存在且包含有效配置");
}
```

---
## 📥 配置导入

### 📋 导入说明
要导入配置，请按照以下步骤操作：

1. **准备配置数据**：
   - 从备份文件中复制JSON配置数据
   - 或从其他用户获取配置分享

2. **验证配置格式**：
   - 确保JSON格式正确
   - 检查必需的配置项是否存在

3. **应用配置**：
   - 将配置数据转换为YAML格式
   - 更新配置管理中心文件的front matter

### 🔧 配置转换工具
```dataviewjs
dv.paragraph("**🔧 JSON到YAML转换指南：**");
dv.paragraph("");
dv.paragraph("**步骤1：** 复制要导入的JSON配置数据");
dv.paragraph("**步骤2：** 提取其中的 `user_config` 部分");
dv.paragraph("**步骤3：** 转换为YAML格式（可使用在线转换工具）");
dv.paragraph("**步骤4：** 替换配置管理中心文件的YAML front matter");
dv.paragraph("");
dv.paragraph("**示例转换：**");
dv.paragraph("```json");
dv.paragraph('{\n  "daily_dimensions": [\n    {\n      "name": "今日成就",\n      "enabled": true\n    }\n  ]\n}');
dv.paragraph("```");
dv.paragraph("");
dv.paragraph("转换为YAML：");
dv.paragraph("```yaml");
dv.paragraph("user_config:\n  daily_dimensions:\n    - name: \"今日成就\"\n      enabled: true");
dv.paragraph("```");
```

---
## 🎨 预设配置模板

### 📋 配置模板库
```dataviewjs
// 提供几种预设配置模板
const presetConfigs = {
    "简约模式": {
        description: "适合新手用户，只包含最基本的复盘维度",
        config: {
            daily_dimensions: [
                {
                    name: "今日成就",
                    description: "今天最重要的成就是什么？",
                    required: true,
                    enabled: true
                },
                {
                    name: "今日反思",
                    description: "今天有什么需要改进的地方？",
                    required: true,
                    enabled: true
                },
                {
                    name: "明日计划",
                    description: "明天最重要的3件事是什么？",
                    required: false,
                    enabled: true
                }
            ],
            display_settings: {
                theme: "light",
                language: "zh-CN",
                date_format: "YYYY-MM-DD",
                time_format: "24h"
            },
            review_style: {
                detailed: false,
                include_prompts: true,
                auto_fill_data: false,
                template_style: "minimal"
            }
        }
    },
    
    "标准模式": {
        description: "平衡的复盘配置，适合大多数用户",
        config: {
            daily_dimensions: [
                {
                    name: "今日成就",
                    description: "今天做了什么？做这些事情用了多少时间？",
                    required: true,
                    enabled: true
                },
                {
                    name: "今日反思",
                    description: "应该做的事、不应该做的事、问题根本原因、时间分配改进",
                    required: true,
                    enabled: true
                },
                {
                    name: "今日收获",
                    description: "今天有哪些收获，包括感受和启发？",
                    required: true,
                    enabled: true
                },
                {
                    name: "今日美好",
                    description: "今天遇到了什么美好的事情？",
                    required: false,
                    enabled: true
                },
                {
                    name: "今日感恩",
                    description: "今天遇到了什么值得感恩的事？",
                    required: false,
                    enabled: true
                }
            ],
            display_settings: {
                theme: "default",
                language: "zh-CN",
                date_format: "YYYY-MM-DD",
                time_format: "24h"
            },
            review_style: {
                detailed: true,
                include_prompts: true,
                auto_fill_data: true,
                template_style: "standard"
            }
        }
    },
    
    "深度模式": {
        description: "适合有经验的用户，包含全面的复盘维度",
        config: {
            daily_dimensions: [
                {
                    name: "时间分配分析",
                    description: "今天的时间是如何分配的？哪些活动最有价值？",
                    required: true,
                    enabled: true
                },
                {
                    name: "目标进展评估",
                    description: "今天在长期目标上有什么进展？",
                    required: true,
                    enabled: true
                },
                {
                    name: "情绪状态记录",
                    description: "今天的情绪状态如何？什么影响了情绪？",
                    required: true,
                    enabled: true
                },
                {
                    name: "学习成长记录",
                    description: "今天学到了什么新知识或技能？",
                    required: true,
                    enabled: true
                },
                {
                    name: "人际互动分析",
                    description: "今天与他人的互动如何？有什么收获？",
                    required: false,
                    enabled: true
                },
                {
                    name: "健康状态评估",
                    description: "今天的身体和精神状态如何？",
                    required: false,
                    enabled: true
                },
                {
                    name: "明日优化计划",
                    description: "基于今天的反思，明天如何做得更好？",
                    required: true,
                    enabled: true
                }
            ],
            display_settings: {
                theme: "dark",
                language: "zh-CN",
                date_format: "YYYY-MM-DD",
                time_format: "24h",
                show_advanced_features: true
            },
            review_style: {
                detailed: true,
                include_prompts: true,
                auto_fill_data: true,
                template_style: "advanced"
            }
        }
    }
};

dv.paragraph("**🎨 预设配置模板：**");

Object.entries(presetConfigs).forEach(([name, preset]) => {
    dv.paragraph(`\n### ${name}`);
    dv.paragraph(`**说明**: ${preset.description}`);
    dv.paragraph(`**维度数量**: ${preset.config.daily_dimensions.length}个`);
    dv.paragraph(`**风格**: ${preset.config.review_style.template_style}`);
    
    dv.paragraph("**配置数据：**");
    dv.paragraph("```json");
    dv.paragraph(JSON.stringify({
        export_info: {
            version: "1.0",
            preset_name: name,
            export_date: new Date().toISOString(),
            description: preset.description
        },
        user_config: preset.config
    }, null, 2));
    dv.paragraph("```");
});
```

---
## 🔄 配置迁移工具

### 📋 版本兼容性检查
```dataviewjs
dv.paragraph("**🔄 配置版本兼容性：**");

const versionCompatibility = [
    {
        version: "1.0",
        status: "✅ 当前版本",
        features: ["基础复盘维度", "显示设置", "复盘风格"],
        compatibility: "完全兼容"
    },
    {
        version: "0.9",
        status: "⚠️ 旧版本",
        features: ["基础复盘维度", "简单设置"],
        compatibility: "需要升级"
    }
];

dv.table(
    ["版本", "状态", "功能特性", "兼容性"],
    versionCompatibility.map(v => [
        v.version,
        v.status,
        v.features.join(", "),
        v.compatibility
    ])
);

dv.paragraph("\n**💡 迁移建议：**");
dv.paragraph("- 从旧版本迁移时，建议先备份现有配置");
dv.paragraph("- 新版本配置包含更多功能，可能需要手动调整");
dv.paragraph("- 建议使用预设模板作为迁移的起点");
```

### 🛠️ 配置修复工具
```dataviewjs
dv.paragraph("**🛠️ 常见配置问题修复：**");

const commonIssues = [
    {
        problem: "配置文件损坏",
        symptoms: "无法加载配置，显示默认设置",
        solution: "使用备份配置恢复，或重新导入预设配置"
    },
    {
        problem: "维度配置错误",
        symptoms: "复盘模板显示异常，缺少某些维度",
        solution: "检查维度的enabled字段，确保必需维度已启用"
    },
    {
        problem: "格式不兼容",
        symptoms: "导入配置后系统报错",
        solution: "检查JSON格式，确保所有字段类型正确"
    },
    {
        problem: "配置不生效",
        symptoms: "修改配置后没有变化",
        solution: "确保配置文件已保存，重新创建复盘笔记"
    }
];

dv.table(
    ["问题", "症状", "解决方案"],
    commonIssues.map(issue => [
        issue.problem,
        issue.symptoms,
        issue.solution
    ])
);
```

---
## 📚 使用指南

### 🎯 配置管理最佳实践
1. **定期备份**：
   - 每周导出一次配置作为备份
   - 重要修改前先备份当前配置

2. **版本控制**：
   - 在配置文件中记录修改日期和原因
   - 保留多个版本的配置备份

3. **测试验证**：
   - 导入新配置后先测试创建复盘笔记
   - 确认所有功能正常工作

4. **分享交流**：
   - 可以与其他用户分享优秀的配置
   - 参考他人的配置优化自己的设置

### 🔗 相关功能
- [[复盘系统/配置管理中心|⚙️ 配置管理中心]] - 主配置界面
- [[Attachment/scripts/配置应用器.js|🔧 配置应用器]] - 配置应用脚本
- [[复盘系统/复盘引导中心|🎯 引导中心]] - 系统导航

---
*📦 配置导入导出工具 v1.0 | 让配置管理更加便捷*
