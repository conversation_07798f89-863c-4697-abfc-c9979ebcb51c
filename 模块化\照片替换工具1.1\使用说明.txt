使用步骤：

0.运行时，先查看是否用过蛋糕，确保替换的文件夹正确

1.直接双击运行【运行批量照片替换.bat】

2.提供需要替换照片的文件路径（例如）：G:\015-Work-设计\已修\替换

需要替换的文件夹，放入替换这个文件夹

3.替换完，会有日志logs，检查最下面看下是否全部替换完

4.第一次用要先备份，熟悉后随意

5.运行时，看下是否需要python【需要就网上下载最新版本下载安装】


程序：batch_photo_replacer.py   【这个需要终端运行，适合懂Python的】

脚本：运行批量照片替换.bat  【直接运行这个就可以了】



详细说明：


1. **准备工作**：
   - 确保您已保存了两个文件：`batch_photo_replacer.py`（Python脚本）和`运行批量照片替换.bat`（批处理文件）
   - 两个文件应保存在同一个文件夹中，例如可以在桌面创建一个名为"照片替换工具1.0"的文件夹

2. **启动工具**：
   - 直接双击`运行批量照片替换.bat`文件启动工具
   - **不需要**直接运行`batch_photo_replacer.py`文件

3. **输入路径**：
   - 在弹出的命令窗口中，您会看到提示：`请输入包含多个客户文件夹的父文件夹路径:`
   - 在这里输入包含所有客户文件夹的父文件夹完整路径，例如：`D:\照片工作\客户照片`
   - 输入完成后按回车键

4. **等待处理**：
   - 程序会自动扫描所有客户文件夹
   - 对于每个客户文件夹，会自动查找修好的照片（以"-([123]) 入盘"开头的文件夹）
   - 然后自动替换需要更新的照片（在其他以"-("开头的文件夹中）
   - 处理过程中会显示进度和结果

5. **查看结果**：
   - 处理完成后，会显示总体处理结果
   - 详细的日志文件会保存在同一个文件夹Logs中，文件名格式为`batch_photo_replacer_年-月-日.log`