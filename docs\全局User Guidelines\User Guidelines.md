# Augment Agent 全局用户偏好配置

> 📅 创建时间：2025-07-07 星期一  
> 🎯 配置目标：基于MCP工具与系统工具对比分析报告优化全局偏好  
> 📝 版本：v3.0 (基于完整报告优化)  
> 🔄 适用范围：跨项目的全局工作偏好和长期协作原则

---

## 🏗️ 五层架构工作原则

### 第1层：用户交互层优先级
```yaml
交互工具选择策略:
1. 简单确认 → 系统交互 (即时响应)
2. 复杂决策 → mcp-feedback-enhanced (丰富交互)
3. 关键节点 → 寸止MCP (智能拦截)

响应速度要求:
- 常规操作: <3秒响应
- 复杂分析: <30秒启动
- 用户反馈: 无超时限制
```

### 第2层：智能决策层策略
```yaml
决策工具组合:
- Sequential Thinking: 复杂问题分析和多步推理
- ACE: 上下文理解和智能路由
- 组合使用: 先分析后决策，确保逻辑清晰

决策质量标准:
- 可追溯性: 完整的推理链条
- 可调整性: 支持中途修正
- 可验证性: 提供验证机制
```

### 第3层：专业工具层选择原则
```yaml
工具选择优先级:
1. 系统工具优先 (稳定性和性能)
2. 官方MCP工具 (兼容性和维护)
3. 第三方MCP工具 (功能完整性)

性能要求:
- 响应时间: 系统工具 < MCP工具
- 稳定性: 官方工具 > 第三方工具
- 功能性: 专业工具 > 通用工具
```

### 第4层：三层记忆管理架构
```yaml
全局记忆层 (Remember):
- 存储内容: 跨项目工作偏好、长期协作原则、质量标准
- 更新频率: 低频更新，长期稳定
- 访问模式: 全局读取，谨慎写入

项目记忆层 (寸止MCP):
- 存储内容: 项目特定规则、临时上下文、阶段性决策
- 更新频率: 中频更新，项目周期内有效
- 访问模式: 项目内共享，灵活调整

知识图谱层 (Memory MCP):
- 存储内容: 复杂知识关系、实体图谱、历史记录
- 更新频率: 高频更新，持续积累
- 访问模式: 智能检索，关联分析
```

### 第5层：基础设施层标准
```yaml
基础工具使用原则:
- File Operations: 所有文件操作的首选
- Codebase Retrieval: 代码搜索的唯一选择
- Web Search: 简单信息查询的首选
- 稳定性要求: 99.9%可用性，<1秒响应
```

---

## 🛠️ 智能工具选择算法

### 核心选择逻辑
```python
def select_optimal_tool(task_type, complexity, performance_req):
    """
    基于报告分析的智能工具选择算法
    """
    # 第一优先级：任务类型匹配
    if task_type == "file_operation":
        return "File Operations"  # 系统工具绝对优先
    elif task_type == "code_search":
        return "Codebase Retrieval"  # 语义搜索优势
    elif task_type == "memory_management":
        return select_memory_layer(complexity)
    
    # 第二优先级：性能要求
    if performance_req == "high_speed":
        return select_system_tool(task_type)
    elif performance_req == "high_functionality":
        return select_mcp_tool(task_type, complexity)
    
    # 第三优先级：稳定性要求
    return select_by_stability(task_type, complexity)

def select_memory_layer(complexity):
    """三层记忆管理选择"""
    if complexity == "global_preference":
        return "Remember"
    elif complexity == "project_specific":
        return "寸止MCP"
    elif complexity == "knowledge_graph":
        return "Memory MCP"
    else:
        return "Remember"  # 默认选择
```

### 故障切换机制
```yaml
故障检测标准:
- 响应超时: >30秒无响应
- 错误率: >10%的操作失败
- 资源占用: CPU>80%或内存>2GB

切换策略:
1. MCP工具故障 → 系统工具备用
2. 第三方工具故障 → 官方工具替代
3. 复杂工具故障 → 简单工具组合

恢复机制:
- 自动重试: 最多3次，间隔递增
- 降级服务: 功能简化但保证可用
- 手动干预: 提供用户选择权
```

---

## 📊 工作质量标准

### 代码质量要求
```yaml
编码标准:
- 使用包管理器: 严禁手动编辑配置文件
- 代码显示: 必须使用 <augment_code_snippet> 标签
- 文件操作: 优先使用 str-replace-editor
- 权限控制: 敏感操作需明确用户许可

性能标准:
- 响应时间: 常规操作<3秒，复杂分析<30秒
- 准确性: 配置示例100%可用，故障方案90%有效
- 可维护性: 模块化设计，清晰文档，版本控制
```

### 文档质量标准
```yaml
格式规范:
- 日期验证: 每份文档必须验证当前日期
- 结构化: 使用标准Markdown格式和层次结构
- 可读性: 清晰的目录、表格、代码示例

内容质量:
- 准确性: 基于实际验证的信息
- 完整性: 覆盖主要使用场景
- 实用性: 提供可操作的指导
```

---

## 🔄 持续优化机制

### 反馈收集策略
```yaml
定期评估:
- 月度: 工具使用效果评估
- 季度: 工作流程优化评估
- 年度: 全面架构评估

触发更新:
- 新工具发布或重大版本更新
- 发现重要故障或解决方案
- 用户反馈的重要改进建议

更新流程:
1. 收集反馈和需求
2. 分析影响和优先级
3. 制定优化方案
4. 测试验证效果
5. 更新配置和文档
```

### 版本管理策略
```yaml
版本控制:
- 主版本: 架构重大变更
- 次版本: 功能增加或重要优化
- 修订版本: 问题修复和小幅调整

兼容性保证:
- 向后兼容: 保持现有配置可用
- 迁移指南: 提供升级路径
- 回滚机制: 支持版本回退
```

---

## 🚨 风险控制与安全

### 操作权限控制
```yaml
高风险操作 (需明确许可):
- 代码提交和推送
- 依赖包安装和更新
- 系统配置修改
- 数据删除和清理

中风险操作 (需确认):
- 文件结构调整
- 配置文件修改
- 批量操作执行

低风险操作 (可直接执行):
- 文档查看和搜索
- 信息收集和分析
- 报告生成和展示
```

### 故障恢复机制
```yaml
问题识别:
- 工具无响应或错误率高
- 配置冲突或兼容性问题
- 性能下降或资源占用异常

恢复策略:
1. 立即切换到备用工具
2. 记录问题详情和上下文
3. 尝试自动修复机制
4. 必要时请求用户干预
5. 更新故障排除知识库
```

---

## 📈 成功指标与监控

### 关键性能指标
```yaml
效率指标:
- 任务完成时间: 相比手动操作提升50%+
- 错误率: <5%的操作需要重试
- 用户满意度: >90%的任务达到预期

质量指标:
- 配置成功率: >95%的配置一次成功
- 文档准确性: >98%的信息经过验证
- 故障解决率: >90%的问题有有效方案

学习指标:
- 知识积累: 每月新增有效经验>10条
- 模式识别: 能够识别80%+的常见问题
- 优化建议: 每季度提出有价值改进>5项
```

---

*📝 备注：此配置为Augment Agent的全局工作偏好，适用于所有项目和场景。项目特定的规则和临时配置请使用项目级别的.augment-guidelines文件。*

*🔄 更新日志：*
- *v3.0 (2025-07-07) - 基于《MCP工具与系统工具对比分析完整报告》全面优化，建立五层架构和三层记忆管理体系*
