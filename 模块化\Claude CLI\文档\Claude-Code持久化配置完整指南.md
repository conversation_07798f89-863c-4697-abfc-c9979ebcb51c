# Claude Code 持久化配置完整指南

> 📅 创建时间：2025-07-15 星期二
> 🎯 目标：详解 Claude Code 持久化配置方法
> 📚 类型：实操配置指南
> 🔄 重点：永久性环境变量配置

---

## 🎯 持久化配置概述

### 📋 **临时配置 vs 持久化配置**

```powershell
# ❌ 临时配置（重启PowerShell后失效）
$env:ANTHROPIC_BASE_URL = "https://code.wenwen-ai.com/v1"
$env:ANTHROPIC_AUTH_TOKEN = "sk-your-api-key"
claude

# ✅ 持久化配置（永久生效）
[Environment]::SetEnvironmentVariable("ANTHROPIC_BASE_URL", "https://code.wenwen-ai.com/v1", "User")
[Environment]::SetEnvironmentVariable("ANTHROPIC_AUTH_TOKEN", "sk-your-api-key", "User")
claude
```

---

## 🔧 方法一：系统环境变量配置（推荐）

### 🎯 **PowerShell 命令配置**

```powershell
# === Claude Code 持久化环境变量配置 ===

# 1. 设置 API 基础地址
[Environment]::SetEnvironmentVariable("ANTHROPIC_BASE_URL", "https://code.wenwen-ai.com/v1", "User")

# 2. 设置 API 认证令牌
[Environment]::SetEnvironmentVariable("ANTHROPIC_AUTH_TOKEN", "sk-Nl3C44OG20bVDaZ03aQJGZQzOK9G6Ehd45ALtM1lmVWgQ4d0", "User")

# 3. 设置最大输出令牌数
[Environment]::SetEnvironmentVariable("CLAUDE_CODE_MAX_OUTPUT_TOKENS", "64000", "User")

# 4. 验证配置
Write-Host "✅ 持久化环境变量配置完成" -ForegroundColor Green
Write-Host "ANTHROPIC_BASE_URL = $([Environment]::GetEnvironmentVariable('ANTHROPIC_BASE_URL', 'User'))" -ForegroundColor Cyan
Write-Host "ANTHROPIC_AUTH_TOKEN = $([Environment]::GetEnvironmentVariable('ANTHROPIC_AUTH_TOKEN', 'User'))" -ForegroundColor Cyan
Write-Host "CLAUDE_CODE_MAX_OUTPUT_TOKENS = $([Environment]::GetEnvironmentVariable('CLAUDE_CODE_MAX_OUTPUT_TOKENS', 'User'))" -ForegroundColor Cyan

# 5. 重新加载环境变量（当前会话）
$env:ANTHROPIC_BASE_URL = [Environment]::GetEnvironmentVariable("ANTHROPIC_BASE_URL", "User")
$env:ANTHROPIC_AUTH_TOKEN = [Environment]::GetEnvironmentVariable("ANTHROPIC_AUTH_TOKEN", "User")
$env:CLAUDE_CODE_MAX_OUTPUT_TOKENS = [Environment]::GetEnvironmentVariable("CLAUDE_CODE_MAX_OUTPUT_TOKENS", "User")

# 6. 启动 Claude Code
claude
```

### 🔍 **配置验证脚本**

```powershell
# === 验证持久化配置脚本 ===
function Test-ClaudePersistentConfig {
    Write-Host "🔍 检查 Claude Code 持久化配置..." -ForegroundColor Cyan
    
    $requiredVars = @(
        "ANTHROPIC_BASE_URL",
        "ANTHROPIC_AUTH_TOKEN", 
        "CLAUDE_CODE_MAX_OUTPUT_TOKENS"
    )
    
    $allConfigured = $true
    
    foreach ($var in $requiredVars) {
        $value = [Environment]::GetEnvironmentVariable($var, "User")
        if ($value) {
            if ($var -eq "ANTHROPIC_AUTH_TOKEN") {
                Write-Host "✅ $var = $($value.Substring(0,10))..." -ForegroundColor Green
            } else {
                Write-Host "✅ $var = $value" -ForegroundColor Green
            }
        } else {
            Write-Host "❌ $var 未配置" -ForegroundColor Red
            $allConfigured = $false
        }
    }
    
    if ($allConfigured) {
        Write-Host "🎉 所有持久化配置已完成，可以直接使用 'claude' 命令" -ForegroundColor Green
    } else {
        Write-Host "⚠️ 部分配置缺失，请完成配置后再使用" -ForegroundColor Yellow
    }
    
    return $allConfigured
}

# 运行验证
Test-ClaudePersistentConfig
```

---

## 🔧 方法二：PowerShell Profile 配置

### 📝 **PowerShell Profile 方式**

```powershell
# === 添加到 PowerShell Profile ===

# 1. 检查 Profile 文件是否存在
if (!(Test-Path $PROFILE)) {
    Write-Host "📝 创建 PowerShell Profile 文件..." -ForegroundColor Blue
    New-Item -ItemType File -Path $PROFILE -Force
}

# 2. 添加 Claude Code 配置到 Profile
$claudeConfig = @"

# === Claude Code 配置 ===
# 自动加载 Claude Code 环境变量
`$env:ANTHROPIC_BASE_URL = "https://code.wenwen-ai.com/v1"
`$env:ANTHROPIC_AUTH_TOKEN = "sk-Nl3C44OG20bVDaZ03aQJGZQzOK9G6Ehd45ALtM1lmVWgQ4d0"
`$env:CLAUDE_CODE_MAX_OUTPUT_TOKENS = "64000"

# Claude Code 快速启动函数
function Start-Claude {
    Write-Host "🚀 启动 Claude Code..." -ForegroundColor Green
    Write-Host "API 地址: `$env:ANTHROPIC_BASE_URL" -ForegroundColor Cyan
    Write-Host "最大令牌: `$env:CLAUDE_CODE_MAX_OUTPUT_TOKENS" -ForegroundColor Cyan
    claude
}

# 创建别名
Set-Alias -Name cc -Value Start-Claude

Write-Host "✅ Claude Code 环境已加载" -ForegroundColor Green
"@

# 3. 写入 Profile 文件
Add-Content -Path $PROFILE -Value $claudeConfig

Write-Host "✅ Claude Code 配置已添加到 PowerShell Profile" -ForegroundColor Green
Write-Host "📝 Profile 位置: $PROFILE" -ForegroundColor Cyan
Write-Host "🔄 重启 PowerShell 或运行 '. `$PROFILE' 来加载配置" -ForegroundColor Yellow
```

### 🎯 **Profile 配置使用方法**

```powershell
# 重新加载 Profile
. $PROFILE

# 使用快速启动函数
Start-Claude

# 或使用别名
cc
```

---

## 🔧 方法三：批处理文件配置

### 📝 **创建启动批处理文件**

```powershell
# === 创建 Claude Code 启动批处理文件 ===

$batContent = @"
@echo off
echo 🚀 启动 Claude Code...
echo.

REM 设置环境变量
set ANTHROPIC_BASE_URL=https://code.wenwen-ai.com/v1
set ANTHROPIC_AUTH_TOKEN=sk-Nl3C44OG20bVDaZ03aQJGZQzOK9G6Ehd45ALtM1lmVWgQ4d0
set CLAUDE_CODE_MAX_OUTPUT_TOKENS=64000

REM 显示配置信息
echo ✅ 环境变量已设置
echo API 地址: %ANTHROPIC_BASE_URL%
echo 最大令牌: %CLAUDE_CODE_MAX_OUTPUT_TOKENS%
echo.

REM 启动 Claude Code
echo 🎯 正在启动 Claude Code...
claude

REM 保持窗口打开
pause
"@

# 保存批处理文件
$batPath = "start-claude-code.bat"
$batContent | Out-File -FilePath $batPath -Encoding ASCII

Write-Host "✅ 批处理文件已创建: $batPath" -ForegroundColor Green
Write-Host "🎯 双击运行或在命令行中执行: .\start-claude-code.bat" -ForegroundColor Cyan
```

---

## 🔧 方法四：配置文件方式

### 📝 **创建 Claude Code 配置文件**

```powershell
# === 创建 Claude Code 配置文件 ===

# 1. 创建配置目录
$configDir = "$env:USERPROFILE\.claude"
if (!(Test-Path $configDir)) {
    New-Item -ItemType Directory -Path $configDir -Force
    Write-Host "📁 创建配置目录: $configDir" -ForegroundColor Blue
}

# 2. 创建配置文件
$configFile = Join-Path $configDir "config.json"
$config = @{
    "api" = @{
        "base_url" = "https://code.wenwen-ai.com/v1"
        "auth_token" = "sk-Nl3C44OG20bVDaZ03aQJGZQzOK9G6Ehd45ALtM1lmVWgQ4d0"
        "max_tokens" = 64000
    }
    "settings" = @{
        "log_level" = "WARNING"
        "timeout" = 120
        "retries" = 3
    }
} | ConvertTo-Json -Depth 3

$config | Out-File -FilePath $configFile -Encoding UTF8

Write-Host "✅ 配置文件已创建: $configFile" -ForegroundColor Green

# 3. 创建配置加载脚本
$loaderScript = @"
# === Claude Code 配置加载器 ===
function Load-ClaudeConfig {
    `$configFile = "`$env:USERPROFILE\.claude\config.json"
    
    if (Test-Path `$configFile) {
        `$config = Get-Content `$configFile | ConvertFrom-Json
        
        `$env:ANTHROPIC_BASE_URL = `$config.api.base_url
        `$env:ANTHROPIC_AUTH_TOKEN = `$config.api.auth_token
        `$env:CLAUDE_CODE_MAX_OUTPUT_TOKENS = `$config.api.max_tokens
        
        Write-Host "✅ Claude Code 配置已从文件加载" -ForegroundColor Green
        return `$true
    } else {
        Write-Host "❌ 配置文件不存在: `$configFile" -ForegroundColor Red
        return `$false
    }
}

function Start-ClaudeWithConfig {
    if (Load-ClaudeConfig) {
        Write-Host "🚀 启动 Claude Code..." -ForegroundColor Green
        claude
    } else {
        Write-Host "❌ 无法加载配置，请检查配置文件" -ForegroundColor Red
    }
}

# 使用方法：Start-ClaudeWithConfig
"@

$loaderPath = Join-Path $configDir "loader.ps1"
$loaderScript | Out-File -FilePath $loaderPath -Encoding UTF8

Write-Host "✅ 配置加载器已创建: $loaderPath" -ForegroundColor Green
Write-Host "🎯 使用方法: . '$loaderPath'; Start-ClaudeWithConfig" -ForegroundColor Cyan
```

---

## 🎯 完整的一键配置脚本

### 🚀 **一键持久化配置脚本**

```powershell
# === Claude Code 一键持久化配置脚本 ===

function Set-ClaudePersistentConfig {
    param(
        [string]$ApiUrl = "https://code.wenwen-ai.com/v1",
        [string]$ApiToken = "sk-Nl3C44OG20bVDaZ03aQJGZQzOK9G6Ehd45ALtM1lmVWgQ4d0",
        [int]$MaxTokens = 64000
    )
    
    Write-Host "🔧 开始配置 Claude Code 持久化环境..." -ForegroundColor Green
    Write-Host ""
    
    try {
        # 1. 设置系统环境变量
        Write-Host "📝 设置系统环境变量..." -ForegroundColor Blue
        [Environment]::SetEnvironmentVariable("ANTHROPIC_BASE_URL", $ApiUrl, "User")
        [Environment]::SetEnvironmentVariable("ANTHROPIC_AUTH_TOKEN", $ApiToken, "User")
        [Environment]::SetEnvironmentVariable("CLAUDE_CODE_MAX_OUTPUT_TOKENS", $MaxTokens.ToString(), "User")
        
        # 2. 加载到当前会话
        Write-Host "🔄 加载到当前会话..." -ForegroundColor Blue
        $env:ANTHROPIC_BASE_URL = $ApiUrl
        $env:ANTHROPIC_AUTH_TOKEN = $ApiToken
        $env:CLAUDE_CODE_MAX_OUTPUT_TOKENS = $MaxTokens.ToString()
        
        # 3. 验证配置
        Write-Host "✅ 配置验证..." -ForegroundColor Blue
        Write-Host "   API 地址: $env:ANTHROPIC_BASE_URL" -ForegroundColor Cyan
        Write-Host "   API 令牌: $($env:ANTHROPIC_AUTH_TOKEN.Substring(0,10))..." -ForegroundColor Cyan
        Write-Host "   最大令牌: $env:CLAUDE_CODE_MAX_OUTPUT_TOKENS" -ForegroundColor Cyan
        Write-Host ""
        
        # 4. 测试 Claude Code 可用性
        Write-Host "🧪 测试 Claude Code 可用性..." -ForegroundColor Blue
        try {
            $claudeVersion = claude --version 2>$null
            if ($claudeVersion) {
                Write-Host "✅ Claude Code 可用: $claudeVersion" -ForegroundColor Green
            } else {
                Write-Host "⚠️ Claude Code 未安装，请先安装: npm install -g @anthropic-ai/claude-code" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "⚠️ Claude Code 未安装，请先安装: npm install -g @anthropic-ai/claude-code" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "🎉 Claude Code 持久化配置完成！" -ForegroundColor Green
        Write-Host "🚀 现在可以直接使用 'claude' 命令启动" -ForegroundColor Green
        Write-Host ""
        Write-Host "💡 使用提示：" -ForegroundColor Yellow
        Write-Host "   - 直接运行: claude" -ForegroundColor White
        Write-Host "   - 新开 PowerShell 窗口配置自动生效" -ForegroundColor White
        Write-Host "   - 如需修改配置，重新运行此脚本即可" -ForegroundColor White
        
        return $true
        
    } catch {
        Write-Host "❌ 配置过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# 执行配置
Set-ClaudePersistentConfig
```

---

## 🎯 使用方法总结

### 📋 **推荐使用顺序**

1. **首选：系统环境变量**（永久生效，最稳定）
```powershell
[Environment]::SetEnvironmentVariable("ANTHROPIC_BASE_URL", "https://code.wenwen-ai.com/v1", "User")
[Environment]::SetEnvironmentVariable("ANTHROPIC_AUTH_TOKEN", "sk-your-token", "User")
[Environment]::SetEnvironmentVariable("CLAUDE_CODE_MAX_OUTPUT_TOKENS", "64000", "User")
claude
```

2. **备选：PowerShell Profile**（自动加载，灵活配置）
3. **简单：批处理文件**（双击启动，适合非技术用户）
4. **高级：配置文件**（结构化配置，适合复杂场景）

### 🔍 **配置验证命令**

```powershell
# 检查环境变量是否正确设置
Get-ChildItem Env: | Where-Object Name -like "*ANTHROPIC*"
Get-ChildItem Env: | Where-Object Name -like "*CLAUDE*"

# 测试 Claude Code
claude --version
claude --help
```

---

*📝 备注：推荐使用系统环境变量方式进行持久化配置，这样配置一次后，任何新开的 PowerShell 窗口都能直接使用 `claude` 命令，无需重复设置。*
