---
tags:
  - type/dashboard
  - dashboard/notes-search
  - obsidian/search
created: 2025-07-11T12:00
updated: 2025-07-11T12:00
---

# 🔍 Obsidian专用搜索系统

```dataviewjs
// Obsidian 专用笔记搜索系统
const container = this.container;

// 创建主容器
const mainDiv = document.createElement('div');
mainDiv.style.cssText = `
    font-family: var(--font-interface);
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
`;

// 搜索区域
const searchDiv = document.createElement('div');
searchDiv.style.cssText = `
    background: var(--background-secondary);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--background-modifier-border);
`;

// 标题
const title = document.createElement('h3');
title.textContent = '🔍 笔记搜索';
title.style.cssText = 'margin: 0 0 15px 0; color: var(--text-normal);';

// 输入区域
const inputDiv = document.createElement('div');
inputDiv.style.cssText = 'display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;';

// 搜索输入框
const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.placeholder = '输入搜索关键词...';
searchInput.style.cssText = `
    flex: 1;
    min-width: 250px;
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
`;

// 模式选择
const modeSelect = document.createElement('select');
modeSelect.style.cssText = `
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
`;

const modes = [
    { value: 'OR', text: 'OR (任一)' },
    { value: 'AND', text: 'AND (所有)' },
    { value: 'EXACT', text: '精确匹配' }
];

modes.forEach(mode => {
    const option = document.createElement('option');
    option.value = mode.value;
    option.textContent = mode.text;
    modeSelect.appendChild(option);
});

// 按钮区域
const buttonDiv = document.createElement('div');
buttonDiv.style.cssText = 'text-align: center;';

const searchBtn = document.createElement('button');
searchBtn.textContent = '🔍 搜索';
searchBtn.style.cssText = `
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
`;

const clearBtn = document.createElement('button');
clearBtn.textContent = '🗑️ 清空';
clearBtn.style.cssText = `
    background: var(--background-modifier-border);
    color: var(--text-normal);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
`;

// 组装搜索区域
inputDiv.appendChild(searchInput);
inputDiv.appendChild(modeSelect);
buttonDiv.appendChild(searchBtn);
buttonDiv.appendChild(clearBtn);

searchDiv.appendChild(title);
searchDiv.appendChild(inputDiv);
searchDiv.appendChild(buttonDiv);

// 结果区域
const resultDiv = document.createElement('div');
resultDiv.style.cssText = `
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    padding: 20px;
    background: var(--background-primary);
    min-height: 200px;
`;

const resultTitle = document.createElement('h3');
resultTitle.textContent = '📋 搜索结果';
resultTitle.style.cssText = 'margin: 0 0 15px 0; color: var(--text-normal);';

const resultContent = document.createElement('div');
resultContent.innerHTML = `
    <div style="text-align: center; color: var(--text-muted); padding: 40px;">
        <div style="font-size: 48px; margin-bottom: 10px;">🔍</div>
        <div style="font-size: 18px;">准备开始搜索</div>
    </div>
`;

resultDiv.appendChild(resultTitle);
resultDiv.appendChild(resultContent);

// 组装主界面
mainDiv.appendChild(searchDiv);
mainDiv.appendChild(resultDiv);
container.appendChild(mainDiv);

// 搜索函数
async function performSearch() {
    const keyword = searchInput.value.trim();
    const mode = modeSelect.value;
    
    if (!keyword) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                <div>请输入搜索关键词</div>
            </div>
        `;
        return;
    }
    
    // 显示加载状态
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
            <div>正在搜索中...</div>
        </div>
    `;
    
    try {
        // 获取所有页面（不限制目录）
        const allPages = dv.pages();

        // 可选：排除某些不需要搜索的目录
        const excludeDirs = ['.obsidian', 'Templates'];
        const searchPages = allPages.where(p =>
            !excludeDirs.some(dir => p.file.path.includes(dir))
        );
        const keywords = keyword.split(/\s+/).filter(k => k.length > 0);
        const results = [];
        
        for (const page of searchPages) {
            try {
                const content = await dv.io.load(page.file.path);
                const fileName = page.file.name.replace('.md', '');
                
                let matched = false;
                let matchType = '';
                let snippet = '';
                
                // 检查文件名匹配
                if (isMatch(fileName, keywords, mode)) {
                    matched = true;
                    matchType = '文件名';
                    snippet = fileName;
                }
                
                // 检查内容匹配
                if (!matched && isMatch(content, keywords, mode)) {
                    matched = true;
                    matchType = '文件内容';
                    snippet = getSnippet(content, keywords);
                }
                
                if (matched) {
                    results.push({
                        name: fileName,
                        link: page.file.link,
                        matchType: matchType,
                        snippet: snippet,
                        mtime: page.file.mtime
                    });
                }
            } catch (error) {
                // 跳过无法读取的文件
            }
        }
        
        displayResults(results, keywords);
        
    } catch (error) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-error); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div>搜索失败，请重试</div>
            </div>
        `;
    }
}

// 匹配函数
function isMatch(text, keywords, mode) {
    if (!text || !keywords || keywords.length === 0) return false;
    
    const lowerText = text.toLowerCase();
    const lowerKeywords = keywords.map(k => k.toLowerCase());
    
    switch (mode) {
        case 'AND':
            return lowerKeywords.every(k => lowerText.includes(k));
        case 'OR':
            return lowerKeywords.some(k => lowerText.includes(k));
        case 'EXACT':
            return lowerText.includes(keywords.join(' ').toLowerCase());
        default:
            return lowerKeywords.some(k => lowerText.includes(k));
    }
}

// 提取片段
function getSnippet(content, keywords, maxLength = 150) {
    const lines = content.split('\n');
    const lowerKeywords = keywords.map(k => k.toLowerCase());
    
    for (const line of lines) {
        if (lowerKeywords.some(k => line.toLowerCase().includes(k))) {
            return line.length > maxLength ? line.substring(0, maxLength) + '...' : line;
        }
    }
    
    return content.substring(0, maxLength) + '...';
}

// 显示结果
function displayResults(results, keywords) {
    if (results.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">未找到匹配的笔记</div>
                <div style="font-size: 14px;">尝试使用不同的关键词</div>
            </div>
        `;
        return;
    }
    
    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: var(--background-secondary); border-radius: 6px; border-left: 4px solid var(--interactive-accent);">
            <strong>🎯 找到 ${results.length} 个匹配结果</strong>
            <span style="margin-left: 10px; color: var(--text-muted);">关键词: ${keywords.join(', ')}</span>
        </div>
    `;
    
    results.forEach(result => {
        const date = new Date(result.mtime).toLocaleDateString('zh-CN');
        
        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 8px; margin-bottom: 15px; padding: 15px; background: var(--background-secondary);">
                <h4 style="margin: 0 0 8px 0; color: var(--text-normal);">
                    <a href="${result.link}" style="text-decoration: none; color: var(--link-color); font-weight: bold;">
                        📄 ${result.name}
                    </a>
                </h4>
                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 8px;">
                    <span style="margin-right: 15px;">📅 ${date}</span>
                    <span>🔍 匹配: ${result.matchType}</span>
                </div>
                <div style="background: var(--background-primary); padding: 10px; border-radius: 4px; border-left: 3px solid var(--color-green);">
                    <div style="font-size: 13px; color: var(--text-normal); line-height: 1.4;">
                        ${highlightText(result.snippet, keywords)}
                    </div>
                </div>
            </div>
        `;
    });
    
    resultContent.innerHTML = html;
}

// 高亮关键词
function highlightText(text, keywords) {
    let highlighted = text;
    keywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        highlighted = highlighted.replace(regex, '<mark style="background: var(--text-highlight-bg); color: var(--text-normal);">$1</mark>');
    });
    return highlighted;
}

// 清空结果
function clearResults() {
    searchInput.value = '';
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 10px;">🔍</div>
            <div style="font-size: 18px;">准备开始搜索</div>
        </div>
    `;
}

// 事件绑定
setTimeout(() => {
    searchBtn.onclick = performSearch;
    clearBtn.onclick = clearResults;
    
    searchInput.onkeypress = function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    };
}, 100);
```

## 📖 使用说明

### 🎯 特点
- **Obsidian 原生样式**：使用 Obsidian 的 CSS 变量，完美融入界面
- **简化逻辑**：去除复杂的调试代码，专注核心搜索功能
- **智能匹配**：支持文件名和内容搜索
- **多种模式**：OR/AND/精确匹配

### 🔍 使用方法
1. 输入关键词（如 "MCP"）
2. 选择搜索模式
3. 点击搜索或按回车键
4. 点击结果中的文件名跳转

---

*💡 专为 Obsidian 环境优化，使用原生样式和简化逻辑。*
