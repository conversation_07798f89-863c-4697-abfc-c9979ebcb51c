# 产品需求文档 (PRD)

## 1. 背景

### 1.1 项目背景
- **项目名称**: [项目名称]
- **项目类型**: [Web应用/移动应用/桌面应用/其他]
- **目标用户**: [描述主要用户群体]
- **业务价值**: [解决什么问题，带来什么价值]

### 1.2 现状分析
- **当前痛点**: [用户或业务面临的主要问题]
- **市场机会**: [市场需求和竞争分析]
- **技术可行性**: [技术实现的可行性评估]

## 2. 任务目标

### 2.1 核心目标
- **主要目标**: [项目要达成的核心目标]
- **成功指标**: [如何衡量项目成功]
- **时间节点**: [关键里程碑和交付时间]

### 2.2 用户价值
- **用户收益**: [用户能获得什么价值]
- **使用场景**: [用户在什么情况下使用]
- **预期效果**: [使用后的预期改善]

## 3. 功能综述

### 3.1 产品定位
[一句话描述产品是什么，解决什么问题]

### 3.2 核心功能
1. **功能模块A**: [简要描述]
2. **功能模块B**: [简要描述]
3. **功能模块C**: [简要描述]

### 3.3 功能优先级
- **P0 (必须有)**: [核心功能列表]
- **P1 (应该有)**: [重要功能列表]
- **P2 (可以有)**: [次要功能列表]

## 4. 功能设计

### 4.1 用户角色
| 角色 | 权限 | 主要功能 |
|------|------|----------|
| [角色1] | [权限描述] | [功能列表] |
| [角色2] | [权限描述] | [功能列表] |

### 4.2 功能模块详细设计

#### 4.2.1 [功能模块A]
- **功能描述**: [详细描述功能作用]
- **用户操作流程**: 
  1. [步骤1]
  2. [步骤2]
  3. [步骤3]
- **业务规则**: [相关的业务逻辑和约束]
- **异常处理**: [错误情况的处理方式]

#### 4.2.2 [功能模块B]
- **功能描述**: [详细描述功能作用]
- **用户操作流程**: 
  1. [步骤1]
  2. [步骤2]
  3. [步骤3]
- **业务规则**: [相关的业务逻辑和约束]
- **异常处理**: [错误情况的处理方式]

## 5. 整体链路

### 5.1 系统架构
```
[用户端] → [前端应用] → [API网关] → [业务服务] → [数据存储]
```

### 5.2 数据流向
1. **用户请求**: [用户操作触发]
2. **数据处理**: [系统如何处理数据]
3. **结果返回**: [如何返回给用户]

### 5.3 关键路径
- **主流程**: [描述主要业务流程]
- **分支流程**: [描述异常或特殊情况流程]

## 6. 输入规范

### 6.1 数据输入
| 字段名 | 类型 | 必填 | 格式要求 | 说明 |
|--------|------|------|----------|------|
| [字段1] | [类型] | [是/否] | [格式] | [说明] |
| [字段2] | [类型] | [是/否] | [格式] | [说明] |

### 6.2 接口输入
- **请求方式**: [GET/POST/PUT/DELETE]
- **请求路径**: [API路径]
- **请求参数**: [参数说明]
- **请求示例**: 
```json
{
  "param1": "value1",
  "param2": "value2"
}
```

## 7. 处理逻辑

### 7.1 核心算法
- **算法描述**: [核心处理逻辑说明]
- **处理步骤**: 
  1. [步骤1]
  2. [步骤2]
  3. [步骤3]

### 7.2 业务规则
- **验证规则**: [数据验证要求]
- **计算规则**: [业务计算逻辑]
- **状态转换**: [状态变化规则]

### 7.3 性能要求
- **响应时间**: [要求的响应时间]
- **并发处理**: [并发用户数要求]
- **数据量**: [预期数据处理量]

## 8. 输出规范

### 8.1 界面输出
- **页面布局**: [页面结构说明]
- **交互方式**: [用户交互设计]
- **视觉效果**: [UI/UX要求]

### 8.2 数据输出
| 字段名 | 类型 | 说明 |
|--------|------|------|
| [字段1] | [类型] | [说明] |
| [字段2] | [类型] | [说明] |

### 8.3 接口输出
- **响应格式**: [JSON/XML等]
- **状态码**: [HTTP状态码说明]
- **响应示例**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "result1": "value1",
    "result2": "value2"
  }
}
```

## 9. 技术要求

### 9.1 技术栈
- **前端**: [技术选型]
- **后端**: [技术选型]
- **数据库**: [数据库选择]
- **部署**: [部署方案]

### 9.2 非功能性要求
- **安全性**: [安全要求]
- **可用性**: [可用性要求]
- **扩展性**: [扩展性要求]
- **兼容性**: [兼容性要求]

### 9.3 开发规范
- **代码规范**: [编码标准]
- **文档规范**: [文档要求]
- **测试规范**: [测试要求]

## 10. 项目管理

### 10.1 团队角色
- **项目经理**: [职责描述]
- **产品经理**: [职责描述]
- **技术负责人**: [职责描述]
- **开发工程师**: [职责描述]
- **测试工程师**: [职责描述]

### 10.2 开发流程
1. **需求分析**: [时间安排]
2. **设计阶段**: [时间安排]
3. **开发阶段**: [时间安排]
4. **测试阶段**: [时间安排]
5. **部署上线**: [时间安排]

### 10.3 质量保证
- **代码审查**: [审查流程]
- **测试策略**: [测试计划]
- **发布流程**: [发布管控]

---

## 附录：执行者工作准则

### 核心工作原则
你是团队的"执行者"，负责准确完成分配的具体任务，生成代码和文档。

**基本要求**:
- 所有工作基于《需求定义文档》(REQ_xxx.md) 和《项目任务清单》(TASKLIST_xxx.md)
- 确保理解任务描述和验收标准
- 仅完成当前任务，不进行无关操作或扩展
- 按任务要求格式输出内容
- 使用 Python 3.8+ 语法和类型提示
- 使用 logging 模块记录关键信息
- 妥善处理错误和异常情况
- 有疑问时立即暂停并澄清，避免基于假设执行
- 保持专业、精确、客观的沟通

### 权力边界
- **无权限**: 需求定义和功能变更决策
- **职责范围**: 严格按照文档执行具体任务
- **异常处理**: 遇到超出范围情况时报告问题，不自行解决

### 工作流程
1. **接收阶段**: 获取 REQ_xxx.md 和 TASKLIST_xxx.md，理解项目目标
2. **确认阶段**: 确认理解并待命，准备接收具体任务指令
3. **执行阶段**: 接收任务详细指令，严格按照要求执行
4. **交付阶段**: 完成任务后提供产出，总结执行情况
5. **反馈阶段**: 等待审查反馈，根据反馈进行修正
6. **循环阶段**: 等待下一个任务指令

---

*文档版本: v1.0*  
*创建日期: [日期]*  
*最后更新: [日期]*  
*负责人: [姓名]*