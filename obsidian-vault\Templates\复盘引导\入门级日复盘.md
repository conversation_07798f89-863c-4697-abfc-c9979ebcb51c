---
tags:
  - daily-review
  - beginner-level
  - progressive-guide
review_level: "beginner"
review_type: "daily"
experience_points: 10
created: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
date: <% tp.date.now("YYYY-MM-DD") %>
---
# 🌱 入门级日复盘 - <% tp.date.now("MM月DD日") %>

> 🎯 **新手友好模式** | 预计用时：2-3分钟 | 经验值：+10

## 💡 温馨提示
欢迎开始您的复盘之旅！这是专为新手设计的简化版复盘，只需要回答3个简单问题。记住：
- ✨ 诚实记录比完美答案更重要
- 🌱 从小事开始，每天进步一点点
- ⏰ 建议在睡前花2-3分钟完成

---
## 📝 今日复盘（入门级）

### 😊 今天最开心的一件事是什么？
*可以是很小的事情，比如喝到好喝的咖啡、收到朋友的消息、完成一个小任务*

**我的回答：**
- 

**💡 小贴士：** 学会发现生活中的小美好，是培养积极心态的第一步！

---
### 💡 今天学到了什么新东西？
*知识、技能、经验或感悟都可以，哪怕是很小的发现*

**我的回答：**
- 

**💡 小贴士：** 每天都有新的学习，关键是要有发现的眼睛！

---
### 🎯 明天想要做得更好的一件事？
*一个具体的小改进就够了，不要给自己太大压力*

**我的回答：**
- 

**💡 小贴士：** 小改进积累起来就是大进步，明天的你会感谢今天的思考！

---
## 🎮 进度追踪

### 📊 我的复盘统计
```dataviewjs
// 自动统计复盘进度
const beginnerReviews = dv.pages('#beginner-level')
    .where(p => p.review_type === 'daily')
    .length;

const totalDays = Math.floor((new Date() - new Date('2025-01-01')) / (1000 * 60 * 60 * 24));
const frequency = Math.round(beginnerReviews / Math.max(totalDays, 1) * 100);

dv.paragraph(`🌱 **入门级复盘次数**: ${beginnerReviews} 次`);
dv.paragraph(`📈 **复盘频率**: ${frequency}%`);
dv.paragraph(`⭐ **累计经验值**: ${beginnerReviews * 10} 点`);

// 升级提示
if (beginnerReviews >= 7) {
    dv.paragraph(`🎉 **恭喜！** 您已经完成了 ${beginnerReviews} 次复盘，可以考虑升级到标准模式了！`);
    dv.paragraph(`👉 [[Templates/复盘引导/标准级日复盘|点击体验标准级复盘]]`);
} else {
    const remaining = 7 - beginnerReviews;
    dv.paragraph(`🎯 **升级进度**: 还需要 ${remaining} 次复盘就可以解锁标准模式！`);
}
```

### 🏆 成就进度
```dataviewjs
// 成就系统
const reviews = dv.pages('#beginner-level').where(p => p.review_type === 'daily');
const reviewCount = reviews.length;

const achievements = [
    { name: "初次复盘", threshold: 1, emoji: "🌟", unlocked: reviewCount >= 1 },
    { name: "坚持三天", threshold: 3, emoji: "🔥", unlocked: reviewCount >= 3 },
    { name: "一周达人", threshold: 7, emoji: "🏆", unlocked: reviewCount >= 7 }
];

dv.paragraph("**🏆 成就收集：**");
achievements.forEach(achievement => {
    const status = achievement.unlocked ? "✅" : "⏸️";
    const progress = achievement.unlocked ? "已解锁" : `${reviewCount}/${achievement.threshold}`;
    dv.paragraph(`${status} ${achievement.emoji} ${achievement.name} (${progress})`);
});
```

---
## 🌟 每日鼓励

```dataviewjs
// 随机显示鼓励语
const encouragements = [
    "🌟 又完成了一天的成长记录，你真棒！",
    "💪 坚持就是胜利，每一次复盘都是进步！",
    "🎯 你正在建立一个改变人生的好习惯！",
    "🌱 小小的坚持，会带来大大的改变！",
    "✨ 今天的反思是明天进步的基石！",
    "🎉 复盘让你更了解自己，继续加油！"
];

const randomIndex = Math.floor(Math.random() * encouragements.length);
dv.paragraph(encouragements[randomIndex]);
```

---
## 📚 新手指南

### 🤔 不知道怎么回答？
- **开心的事**：可以是完成工作、吃到美食、看到美景、收到关心等
- **学到的东西**：可以是工作技能、生活窍门、人际交往、自我认知等
- **明天改进**：可以是早起、多喝水、少看手机、多运动等小习惯

### 💡 复盘小技巧
1. **固定时间**：建议每天睡前10分钟进行复盘
2. **诚实记录**：不需要完美，真实记录就好
3. **积极视角**：多关注收获和成长，而不是遗憾
4. **具体明确**：用具体的事例而不是抽象的概念

### 🎯 下一步计划
当您完成7次入门级复盘后，可以尝试：
- [[Templates/复盘引导/标准级日复盘|标准级日复盘]] - 更深入的5个问题
- [[复盘系统/复盘数据仪表盘|查看复盘数据统计]]
- [[复盘系统/配置/复盘引导配置|了解完整的进阶体系]]

---
## 🔗 相关链接
- [[复盘系统/复盘数据仪表盘|📊 复盘数据仪表盘]]
- [[Templates/复盘引导/标准级日复盘|📈 标准级日复盘]]
- [[复盘系统/配置/复盘引导配置|⚙️ 引导系统配置]]

---
*🌱 入门级复盘 v1.0 | 让复盘成为一种习惯，让成长成为一种生活方式*
