---
tags:
  - type/structure
  - structure/bujo/yearly
start_date: ""
end_date: ""
template: "[[5_BuJo - Yearly Log]]"
created: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
updated: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
---
# 📅 年度回顾 YEARLY REVIEW - <% tp.date.now("YYYY") %>年

## 📊 年度精华内容

```dataviewjs
// 获取当前年记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前年内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 创建表格来显示精华内容
const rows = [];

for (const page of pages) {
    // 获取文件内容
    const content = await dv.io.load(page.file.path);
    
    // 查找包含 #精华 标签的行
    const lines = content.split("\n");
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes("#精华")) {
            // 提取文件名部分
            const fileName = page.file.name.replace(".md", ""); // 移除.md扩展名
            
            // 提取内容（去除时间戳和标签）
            let highlightContent = lines[i];
            
            // 移除时间戳部分 (start::XX:XX)
            highlightContent = highlightContent.replace(/\(start::\d+:\d+\)/g, "");
            
            // 移除 #精华 标签
            highlightContent = highlightContent.replace(/#精华/g, "");
            
            // 清理多余空格
            highlightContent = highlightContent.trim();
            
            // 添加到结果数组
            rows.push({
                fileName: fileName,
                content: highlightContent,
                link: page.file.link
            });
        }
    }
}

// 显示结果
if (rows.length > 0) {
    dv.table(["日期", "内容"], 
        rows.map(row => [
            `[[${row.fileName}|${row.fileName}]]`, 
            row.content
        ])
    );
} else {
    dv.paragraph("本年暂无精华内容");
}
```

---
## 📍 本年项目全景 Yearly Project Overview

```dataviewjs
// 获取当前年记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 1. 从项目文件夹收集年度项目
const yearlyProjects = dv.pages('"6_Project Notes"')
    .where(p => {
        if (p.file.ctime >= startDate && p.file.ctime <= endDate) {
            return true;
        }
        // 也包括在这一年中有活动的项目
        return p.updated && p.updated >= startDate && p.updated <= endDate;
    })
    .sort(p => p.file.ctime);

// 显示项目概览
if (yearlyProjects.length > 0) {
    dv.header(3, "📊 年度项目统计");
    
    dv.table(
        ["项目", "状态", "创建时间", "最后更新"],
        yearlyProjects.map(p => [
            p.file.link,
            p.status || "未知",
            p.file.ctime ? p.file.ctime.toDateString() : "-",
            p.updated ? p.updated.toDateString() : "-"
        ])
    );
    
    dv.paragraph(`- 本年共有 **${yearlyProjects.length}** 个项目活动`);
} else {
    dv.paragraph("*本年暂无项目活动记录*");
}
```

### 🧰 年度重点任务 Key Tasks
```tasks
due after <% tp.date.now("YYYY-01-01", "YYYY-MM-DD") %>
due before <% tp.date.now("YYYY-12-31", "YYYY-MM-DD") %>
sort by due reverse
short
```

### ✅ 本年已完成项 Done
```tasks
done
done after <% tp.date.now("YYYY-01-01", "YYYY-MM-DD") %>
done before <% tp.date.now("YYYY-12-31", "YYYY-MM-DD") %>
sort by done reverse
short
```

---
## 💻 知识库维护 Knowledge System

### ➕ 本年创建的笔记 Created
```dataview
TABLE without id file.cday AS Date, file.link AS Name, file.folder AS Folder
WHERE file.cday >= date(this.start_date) 
    AND file.cday <= date(this.end_date)
    AND contains(file.folder,"Templates") = False 
    AND contains(file.folder,"5") = False
SORT file.cday DESC,file.folder ASC
```

### 🔧 本年修改的笔记 Updated
```dataview
TABLE without id file.mday AS Date, file.link AS Name, file.folder AS Folder
WHERE file.mday >= date(this.start_date)
    AND file.mday <= date(this.end_date)
    AND contains(file.folder,"Templates") = False 
    AND contains(file.folder,"5") = False
SORT file.mday DESC,file.folder ASC
```

---
## 🎉 年复盘
> 基于复盘.txt的年复盘框架，全面回顾和规划每一年

### 🏆 汇总本年成就
**这一年的整体状态怎样，用什么关键词形容，为什么？**
- 关键词：
- 原因：

**取得了哪些关键成就，跟过去的自己比有什么突破？**
- 

**养成了什么习惯，坚持做了什么事情？**
- 

### 🎯 目标完成情况
**对去年定下的目标进行比对，都完成了哪些？**
- 已完成目标：
- 未完成目标：
- 完成率：%

### 💡 收获感悟
**这一年在各方面的收获与感悟，无论成败都要总结**
- 工作方面：
- 生活方面：
- 学习方面：
- 人际方面：

### 🚨 深刻领悟与教训
**这一年最大的教训是什么，未来如何规避？**
- 最大教训：
- 规避方法：

### 📊 年满意度打分
**家庭、健康、工作、财务、人际、成长多维度评价（1-10分）**
- 家庭：/10
- 健康：/10
- 工作：/10
- 财务：/10
- 人际：/10
- 成长：/10
- **总体满意度：**/10

### 🔄 回到1.1你会怎么过
**如果重来一次，你会怎么过这一年，哪些事情上改进？**
- 

### 📝 新年五个改变清单
**想要在未来哪些方面有更好的发展，设置合理的目标**
1. 
2. 
3. 
4. 
5. 

### 🎯 新一年目标愿望
**为新一年的工作、生活制定可量化能评价的目标**
- 工作目标：
- 生活目标：
- 学习目标：
- 健康目标：
- 财务目标：
- 人际目标：
