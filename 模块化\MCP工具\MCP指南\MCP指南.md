# MCP 指南

> **最后更新**：2025-06-22

---

## 目录
1. [环境配置](#环境配置)
2. [MCP 配置](#mcp-配置)
3. [MCP 用法](#mcp-用法)
4. [常见问题](#常见问题)
5. [参考文档](#参考文档)

---

## 环境配置

### 1. 安装 Obsidian Local REST API 插件
1. 打开 **Obsidian → Settings → Community Plugins**。
2. 关闭 *安全模式*，点击 **Browse** 搜索 `Local REST API` 并安装。
3. 启用插件后进入 **Settings**：
   - **API Key**：点击 *copy* 保存。
   - **Host**：默认 `127.0.0.1`。
   - **Port**：默认 `27124`（HTTPS） / `27123`（HTTP，可在插件页勾选启用）。

### 2. 配置系统环境变量
> 若使用 Cursor / VS Code / 其他 MCP 客户端，最稳妥方式是把 API Key 写入系统用户环境变量。

```powershell
# Windows PowerShell
setx OBSIDIAN_API_KEY "<你的 API Key>"
```

*重启客户端后生效*。

### 3. 安装 uv / uvx（可选）
部分 MCP 服务器通过 **uvx** 启动，推荐预先安装：
```bash
pip install uv
```

---

## MCP 配置

以下示例均写在 `~/.cursor/mcp.json`（或对应编辑器的 MCP 配置文件）中。

### 1. Obsidian MCP
```json
{
  "mcpServers": {
    "mcp-obsidian": {
      "command": "uvx",
      "args": ["mcp-obsidian"],
      "env": {
        "OBSIDIAN_API_KEY": "<你的 API Key>",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```

### 2. Shrimp-Task-Manager MCP
```json
{
  "shrimp-task-manager": {
    "command": "npx",
    "args": ["-y", "mcp-shrimp-task-manager"],
    "timeout": 600,
    "env": {
      "DATA_DIR": "C:/Users/<USER>/Desktop/测试库/shrimp-data",
      "TEMPLATES_USE": "zh",
      "ENABLE_GUI": "true"
    }
  }
}
```

> 其他例如 **Context7 MCP**、**Replicate-Image-Gen MCP** 可参考各自文档，将 `mcpServers` 条目添加到同一文件即可。

---

## MCP 用法

### 1. Obsidian 相关
| 功能 | 示例指令 |
| ---- | -------- |
| 列出文件 | `列出我的 Obsidian 知识库中的所有文件` |
| 搜索笔记 | `搜索包含 "项目" 关键词的笔记` |
| 读取笔记 | `读取 Homepage.md 的内容` |
| 创建笔记 | `创建新笔记 今日灵感.md` |

### 2. Shrimp-Task-Manager
| 场景 | 指令示例 |
| ---- | -------- |
| 任务规划 | `plan task 开发个人博客` |
| 任务分解 | `split tasks 构建 To-Do 应用` |
| 列出任务 | `list tasks` |
| 执行任务 | `execute task <任务ID>` |
| 验证完成 | `verify task <任务ID> score:90 summary:"已完成"` |

### 3. 通用最佳实践
1. **明确上下文**：在一次对话内先讲需求，再发指令。
2. **小步快跑**：先 `plan task` → `split tasks` → `execute task` → `verify task`。
3. **善用 research_mode**：复杂技术选型前先调研。

---

## 常见问题
| 问题 | 解决方案 |
| ---- | -------- |
| 401 Unauthorized | 确认 API Key 正确、已写入环境变量，并重启客户端。 |
| 端口占用 / 无法连接 | 检查 Obsidian 是否运行，插件端口是否被防火墙拦截。 |
| uvx 命令不存在 | `pip install uv` 或使用 `python -m uv`。 |
| Shrimp GUI 打不开 | 初次启动需耐心等待；确认 `ENABLE_GUI` 为 `true`。 |

---

## 参考文档
- 《Obsidian MCP配置详细指南》
- 《Augment IDE Obsidian MCP 配置指南》
- 《Shrimp-Task-Manager MCP 配置指南》
- Context7 MCP README

> 如有遗漏或错误，欢迎补充！ 