---
tags:
  - type/dashboard
  - dashboard/stable-search
  - obsidian/search
created: 2025-07-11T15:00
updated: 2025-07-11T15:00
---

# 🛡️ 稳定版搜索系统

```dataviewjs
// 稳定版搜索系统 - 避免闪退，保证稳定性
const container = this.container;

// 简化配置
const CONFIG = {
    excludeDirs: ['.obsidian', 'Templates', '.trash'],
    batchSize: 20, // 减小批处理大小
    maxResults: 100 // 限制最大结果数
};

// 创建主容器
const mainDiv = document.createElement('div');
mainDiv.style.cssText = `
    font-family: var(--font-interface);
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
`;

// 搜索区域
const searchDiv = document.createElement('div');
searchDiv.style.cssText = `
    background: var(--background-secondary);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--background-modifier-border);
`;

const title = document.createElement('h3');
title.textContent = '🛡️ 稳定版搜索系统';
title.style.cssText = 'margin: 0 0 15px 0; color: var(--text-normal);';

// 输入区域
const inputDiv = document.createElement('div');
inputDiv.style.cssText = 'display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;';

// 搜索输入框
const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.placeholder = '搜索笔记...';
searchInput.style.cssText = `
    flex: 1;
    min-width: 200px;
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
`;

// 搜索模式
const modeSelect = document.createElement('select');
modeSelect.style.cssText = `
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
`;

['OR (任一)', 'AND (所有)', '精确匹配'].forEach((text, index) => {
    const option = document.createElement('option');
    option.value = ['OR', 'AND', 'EXACT'][index];
    option.textContent = text;
    modeSelect.appendChild(option);
});

// 搜索范围
const scopeSelect = document.createElement('select');
scopeSelect.style.cssText = modeSelect.style.cssText;

['全部内容', '仅文件名', '仅文件内容'].forEach((text, index) => {
    const option = document.createElement('option');
    option.value = ['all', 'filename', 'content'][index];
    option.textContent = text;
    scopeSelect.appendChild(option);
});

// 按钮
const searchBtn = document.createElement('button');
searchBtn.textContent = '🔍 搜索';
searchBtn.style.cssText = `
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
`;

const clearBtn = document.createElement('button');
clearBtn.textContent = '🗑️ 清空';
clearBtn.style.cssText = `
    background: var(--background-modifier-border);
    color: var(--text-normal);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
`;

// 状态显示
const statusDiv = document.createElement('div');
statusDiv.style.cssText = 'font-size: 12px; color: var(--text-muted); margin-top: 10px; text-align: center;';
statusDiv.textContent = '稳定版搜索系统准备就绪';

// 组装搜索区域
inputDiv.appendChild(searchInput);
inputDiv.appendChild(modeSelect);
inputDiv.appendChild(scopeSelect);

const buttonDiv = document.createElement('div');
buttonDiv.style.cssText = 'text-align: center;';
buttonDiv.appendChild(searchBtn);
buttonDiv.appendChild(clearBtn);

searchDiv.appendChild(title);
searchDiv.appendChild(inputDiv);
searchDiv.appendChild(buttonDiv);
searchDiv.appendChild(statusDiv);

// 结果区域
const resultDiv = document.createElement('div');
resultDiv.style.cssText = `
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    padding: 20px;
    background: var(--background-primary);
    min-height: 200px;
`;

const resultContent = document.createElement('div');
resultContent.innerHTML = `
    <div style="text-align: center; color: var(--text-muted); padding: 40px;">
        <div style="font-size: 48px; margin-bottom: 10px;">🛡️</div>
        <div style="font-size: 18px;">稳定版搜索系统准备就绪</div>
    </div>
`;

resultDiv.appendChild(resultContent);

// 组装主界面
mainDiv.appendChild(searchDiv);
mainDiv.appendChild(resultDiv);
container.appendChild(mainDiv);

// 核心搜索函数（简化版）
async function performStableSearch() {
    const keyword = searchInput.value.trim();
    const mode = modeSelect.value;
    const searchScope = scopeSelect.value;
    
    if (!keyword) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                <div>请输入搜索关键词</div>
            </div>
        `;
        return;
    }
    
    statusDiv.textContent = '正在搜索中...';
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
            <div>正在搜索中...</div>
        </div>
    `;
    
    try {
        let allPages = dv.pages();
        
        // 排除系统目录
        allPages = allPages.where(p => 
            !CONFIG.excludeDirs.some(dir => p.file.path.includes(dir))
        );
        
        const keywords = keyword.split(/\s+/).filter(k => k.length > 0);
        const results = [];
        let processed = 0;
        
        // 简化的批处理
        for (const page of allPages) {
            if (results.length >= CONFIG.maxResults) break;
            
            try {
                const fileName = page.file.name.replace('.md', '');
                let content = '';
                let matched = false;
                let matchType = '';
                let snippet = '';
                
                // 根据搜索范围决定是否读取内容
                if (searchScope === 'all' || searchScope === 'content') {
                    content = await dv.io.load(page.file.path);
                }
                
                // 检查文件名匹配
                if (searchScope === 'all' || searchScope === 'filename') {
                    if (isSimpleMatch(fileName, keywords, mode)) {
                        matched = true;
                        matchType = '文件名';
                        snippet = fileName;
                    }
                }
                
                // 检查内容匹配
                if (!matched && (searchScope === 'all' || searchScope === 'content')) {
                    if (isSimpleMatch(content, keywords, mode)) {
                        matched = true;
                        matchType = '文件内容';
                        snippet = getSimpleSnippet(content, keywords);
                    }
                }
                
                if (matched) {
                    results.push({
                        name: fileName,
                        path: page.file.path,
                        link: page.file.link,
                        matchType: matchType,
                        snippet: snippet,
                        mtime: page.file.mtime
                    });
                }
                
                processed++;
                
                // 每处理20个文件更新一次状态
                if (processed % CONFIG.batchSize === 0) {
                    statusDiv.textContent = `已处理 ${processed} 个文件...`;
                    // 让出控制权
                    await new Promise(resolve => setTimeout(resolve, 1));
                }
                
            } catch (error) {
                // 跳过有问题的文件
                console.warn(`跳过文件: ${page.file.path}`);
            }
        }
        
        statusDiv.textContent = `搜索完成，处理了 ${processed} 个文件`;
        displayStableResults(results, keywords, searchScope);
        
    } catch (error) {
        statusDiv.textContent = '搜索出错，请重试';
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-error); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div>搜索失败，请重试</div>
            </div>
        `;
    }
}

// 简化的匹配函数
function isSimpleMatch(text, keywords, mode) {
    if (!text || !keywords || keywords.length === 0) return false;
    
    const lowerText = text.toLowerCase();
    const lowerKeywords = keywords.map(k => k.toLowerCase());
    
    switch (mode) {
        case 'AND':
            return lowerKeywords.every(k => lowerText.includes(k));
        case 'OR':
            return lowerKeywords.some(k => lowerText.includes(k));
        case 'EXACT':
            return lowerText.includes(keywords.join(' ').toLowerCase());
        default:
            return lowerKeywords.some(k => lowerText.includes(k));
    }
}

// 简化的片段提取
function getSimpleSnippet(content, keywords, maxLength = 150) {
    const lines = content.split('\n');
    const lowerKeywords = keywords.map(k => k.toLowerCase());
    
    for (const line of lines) {
        if (lowerKeywords.some(k => line.toLowerCase().includes(k))) {
            return line.length > maxLength ? line.substring(0, maxLength) + '...' : line;
        }
    }
    
    return content.substring(0, maxLength) + '...';
}

// 简化的结果显示
function displayStableResults(results, keywords, searchScope) {
    if (results.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">未找到匹配的笔记</div>
                <div style="font-size: 14px;">搜索范围: ${searchScope}</div>
            </div>
        `;
        return;
    }
    
    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: var(--background-secondary); border-radius: 6px; border-left: 4px solid var(--interactive-accent);">
            <strong>🎯 找到 ${results.length} 个匹配结果</strong><br>
            <span style="color: var(--text-muted); font-size: 14px;">
                关键词: ${keywords.join(', ')} | 搜索范围: ${searchScope}
            </span>
        </div>
    `;
    
    results.forEach(result => {
        const date = new Date(result.mtime).toLocaleDateString('zh-CN');
        const pathParts = result.path.split('/');
        const directory = pathParts.length > 1 ? pathParts[0] : '根目录';
        
        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 8px; margin-bottom: 12px; padding: 15px; background: var(--background-secondary);">
                <h4 style="margin: 0 0 8px 0; color: var(--text-normal);">
                    <a href="${result.link}" style="text-decoration: none; color: var(--link-color); font-weight: bold;">
                        📄 ${result.name}
                    </a>
                </h4>
                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 8px;">
                    <span style="margin-right: 15px;">📁 ${directory}</span>
                    <span style="margin-right: 15px;">📅 ${date}</span>
                    <span>🔍 匹配: ${result.matchType}</span>
                </div>
                <div style="background: var(--background-primary); padding: 10px; border-radius: 4px; border-left: 3px solid var(--color-green);">
                    <div style="font-size: 13px; color: var(--text-normal); line-height: 1.4;">
                        ${highlightText(result.snippet, keywords)}
                    </div>
                </div>
            </div>
        `;
    });
    
    resultContent.innerHTML = html;
}

// 简化的高亮函数
function highlightText(text, keywords) {
    let highlighted = text;
    keywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        highlighted = highlighted.replace(regex, '<mark style="background: var(--text-highlight-bg); color: var(--text-normal);">$1</mark>');
    });
    return highlighted;
}

// 清空结果
function clearStableResults() {
    searchInput.value = '';
    statusDiv.textContent = '稳定版搜索系统准备就绪';
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 10px;">🛡️</div>
            <div style="font-size: 18px;">稳定版搜索系统准备就绪</div>
        </div>
    `;
}

// 事件绑定
setTimeout(() => {
    searchBtn.onclick = performStableSearch;
    clearBtn.onclick = clearStableResults;
    
    searchInput.onkeypress = function(e) {
        if (e.key === 'Enter') {
            performStableSearch();
        }
    };
}, 100);
```

## 🛡️ 稳定版特点

### 安全优化
- **减小批处理大小**：每批20个文件，降低内存压力
- **限制结果数量**：最多100个结果，避免过载
- **简化代码逻辑**：移除复杂功能，确保稳定性
- **错误处理**：跳过有问题的文件，继续搜索

### 核心功能
- ✅ 基础搜索功能（OR/AND/精确匹配）
- ✅ 搜索范围选择（全部/文件名/内容）
- ✅ 实时状态显示
- ✅ 结果高亮显示

### 使用建议
- 适合日常基础搜索需求
- 在大型vault中更加稳定
- 避免复杂查询导致的性能问题

---

*🛡️ 这个版本优先保证稳定性，避免Obsidian闪退问题！*
