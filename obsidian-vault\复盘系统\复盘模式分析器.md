---
tags:
  - pattern-analysis
  - data-mining
  - review-insights
created: 2025-08-07
updated: 2025-08-07
analysis_period: 30  # 分析天数
min_reviews: 5       # 最少复盘数量
---
# 🧠 复盘模式分析器

> 深度挖掘复盘数据，发现个人成长模式和行为规律

## 📊 数据概览

```dataviewjs
// 获取分析参数
const analysisPeriod = dv.current().analysis_period || 30;
const minReviews = dv.current().min_reviews || 5;

// 收集分析数据
const cutoffDate = dv.date('today') - dv.duration(`${analysisPeriod} days`);
const reviewFiles = dv.pages('"0_Bullet Journal"')
    .where(p => p.file.ctime >= cutoffDate && 
                (p.file.name.includes("Daily Log") || 
                 p.file.name.includes("Weekly Log") || 
                 p.file.name.includes("Monthly Log")))
    .sort(p => p.file.ctime, 'desc');

const dailyReviews = reviewFiles.where(p => p.file.name.includes("Daily Log"));
const weeklyReviews = reviewFiles.where(p => p.file.name.includes("Weekly Log"));
const monthlyReviews = reviewFiles.where(p => p.file.name.includes("Monthly Log"));

dv.paragraph(`## 📈 ${analysisPeriod}天数据概览`);
dv.table(
    ["指标", "数量", "频率", "状态"],
    [
        ["📅 日复盘", dailyReviews.length, `${Math.round(dailyReviews.length/analysisPeriod*100)}%`, dailyReviews.length >= minReviews ? "✅ 充足" : "⚠️ 不足"],
        ["📊 周复盘", weeklyReviews.length, `${Math.round(weeklyReviews.length/(analysisPeriod/7)*100)}%`, weeklyReviews.length >= 2 ? "✅ 充足" : "⚠️ 不足"],
        ["📈 月复盘", monthlyReviews.length, `${Math.round(monthlyReviews.length/(analysisPeriod/30)*100)}%`, monthlyReviews.length >= 1 ? "✅ 充足" : "⚠️ 不足"],
        ["📝 总复盘", reviewFiles.length, "-", reviewFiles.length >= minReviews ? "✅ 可分析" : "❌ 数据不足"]
    ]
);

// 存储数据供后续分析使用
window.reviewAnalysisData = {
    reviewFiles,
    dailyReviews,
    weeklyReviews,
    monthlyReviews,
    analysisPeriod,
    minReviews
};
```

---
## 🎯 成就模式分析

```dataviewjs
// 分析成就相关的模式
const data = window.reviewAnalysisData;
if (!data || data.reviewFiles.length < data.minReviews) {
    dv.paragraph("*数据不足，需要至少" + data.minReviews + "篇复盘记录进行分析*");
} else {
    // 定义成就关键词
    const achievementKeywords = {
        "工作成就": ["完成", "项目", "任务", "目标", "交付", "成功"],
        "学习成就": ["学会", "掌握", "理解", "阅读", "课程", "技能"],
        "健康成就": ["运动", "锻炼", "跑步", "健身", "早起", "睡眠"],
        "人际成就": ["沟通", "合作", "帮助", "分享", "团队", "朋友"],
        "习惯成就": ["坚持", "养成", "改善", "规律", "自律", "进步"]
    };
    
    let achievementStats = {};
    Object.keys(achievementKeywords).forEach(category => {
        achievementStats[category] = { count: 0, files: [] };
    });
    
    // 分析每个复盘文件（基于文件名和标签的简化分析）
    data.reviewFiles.forEach(file => {
        const fileName = file.file.name.toLowerCase();
        const tags = file.file.tags || [];
        const content = fileName + " " + tags.join(" ");
        
        Object.entries(achievementKeywords).forEach(([category, keywords]) => {
            const hasKeyword = keywords.some(keyword => content.includes(keyword));
            if (hasKeyword) {
                achievementStats[category].count++;
                achievementStats[category].files.push(file);
            }
        });
    });
    
    dv.paragraph("**🎯 成就类型分布：**");
    const sortedAchievements = Object.entries(achievementStats)
        .sort(([,a], [,b]) => b.count - a.count)
        .filter(([,data]) => data.count > 0);
    
    if (sortedAchievements.length > 0) {
        sortedAchievements.forEach(([category, data]) => {
            const percentage = Math.round(data.count / data.reviewFiles.length * 100);
            const bar = "█".repeat(Math.floor(percentage / 10)) + "░".repeat(10 - Math.floor(percentage / 10));
            dv.paragraph(`**${category}**: ${bar} ${data.count}次 (${percentage}%)`);
        });
        
        // 成就模式洞察
        dv.paragraph("\n**💡 成就模式洞察：**");
        const topAchievement = sortedAchievements[0];
        dv.paragraph(`- 您最擅长的领域是**${topAchievement[0]}**，占总成就的${Math.round(topAchievement[1].count / data.reviewFiles.length * 100)}%`);
        
        if (sortedAchievements.length >= 2) {
            const secondAchievement = sortedAchievements[1];
            dv.paragraph(`- 其次是**${secondAchievement[0]}**，建议继续保持这两个优势领域`);
        }
        
        const lowAchievements = sortedAchievements.filter(([,data]) => data.count < data.reviewFiles.length * 0.1);
        if (lowAchievements.length > 0) {
            dv.paragraph(`- 可以考虑在**${lowAchievements.map(([name]) => name).join("、")}**方面投入更多精力`);
        }
    } else {
        dv.paragraph("*未检测到明显的成就模式，建议在复盘中更详细地记录成就*");
    }
}
```

---
## 🚧 挑战模式分析

```dataviewjs
// 分析挑战和问题的模式
const data = window.reviewAnalysisData;
if (data && data.reviewFiles.length >= data.minReviews) {
    // 定义挑战关键词
    const challengeKeywords = {
        "时间管理": ["时间", "拖延", "效率", "忙碌", "赶工", "deadline"],
        "情绪管理": ["焦虑", "压力", "烦躁", "沮丧", "疲惫", "情绪"],
        "人际关系": ["冲突", "误解", "沟通", "合作", "团队", "关系"],
        "技能挑战": ["困难", "不会", "学习", "掌握", "理解", "技术"],
        "健康问题": ["疲劳", "失眠", "生病", "不适", "体力", "精力"]
    };
    
    let challengeStats = {};
    Object.keys(challengeKeywords).forEach(category => {
        challengeStats[category] = { count: 0, trend: [] };
    });
    
    // 按时间顺序分析挑战趋势
    const sortedReviews = data.reviewFiles.sort(p => p.file.ctime);
    sortedReviews.forEach((file, index) => {
        const fileName = file.file.name.toLowerCase();
        const tags = file.file.tags || [];
        const content = fileName + " " + tags.join(" ");
        
        Object.entries(challengeKeywords).forEach(([category, keywords]) => {
            const hasKeyword = keywords.some(keyword => content.includes(keyword));
            if (hasKeyword) {
                challengeStats[category].count++;
                challengeStats[category].trend.push(index);
            }
        });
    });
    
    dv.paragraph("**🚧 挑战类型分析：**");
    const sortedChallenges = Object.entries(challengeStats)
        .sort(([,a], [,b]) => b.count - a.count)
        .filter(([,data]) => data.count > 0);
    
    if (sortedChallenges.length > 0) {
        sortedChallenges.forEach(([category, data]) => {
            const percentage = Math.round(data.count / data.reviewFiles.length * 100);
            const bar = "█".repeat(Math.floor(percentage / 10)) + "░".repeat(10 - Math.floor(percentage / 10));
            
            // 分析趋势
            let trendIndicator = "→";
            if (data.trend.length >= 2) {
                const recent = data.trend.slice(-Math.ceil(data.trend.length / 3));
                const early = data.trend.slice(0, Math.ceil(data.trend.length / 3));
                const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
                const earlyAvg = early.reduce((a, b) => a + b, 0) / early.length;
                
                if (recentAvg > earlyAvg + 2) trendIndicator = "📈"; // 增加
                else if (recentAvg < earlyAvg - 2) trendIndicator = "📉"; // 减少
            }
            
            dv.paragraph(`**${category}**: ${bar} ${data.count}次 (${percentage}%) ${trendIndicator}`);
        });
        
        // 挑战模式洞察
        dv.paragraph("\n**💡 挑战模式洞察：**");
        const topChallenge = sortedChallenges[0];
        dv.paragraph(`- 您最常遇到的挑战是**${topChallenge[0]}**，建议制定专门的应对策略`);
        
        // 趋势分析
        const increasingChallenges = sortedChallenges.filter(([,data]) => {
            if (data.trend.length < 2) return false;
            const recent = data.trend.slice(-Math.ceil(data.trend.length / 3));
            const early = data.trend.slice(0, Math.ceil(data.trend.length / 3));
            const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
            const earlyAvg = early.reduce((a, b) => a + b, 0) / early.length;
            return recentAvg > earlyAvg + 2;
        });
        
        if (increasingChallenges.length > 0) {
            dv.paragraph(`- ⚠️ **${increasingChallenges.map(([name]) => name).join("、")}**方面的挑战在增加，需要重点关注`);
        }
        
        const decreasingChallenges = sortedChallenges.filter(([,data]) => {
            if (data.trend.length < 2) return false;
            const recent = data.trend.slice(-Math.ceil(data.trend.length / 3));
            const early = data.trend.slice(0, Math.ceil(data.trend.length / 3));
            const recentAvg = recent.reduce((a, b) => a + b, 0) / recent.length;
            const earlyAvg = early.reduce((a, b) => a + b, 0) / early.length;
            return recentAvg < earlyAvg - 2;
        });
        
        if (decreasingChallenges.length > 0) {
            dv.paragraph(`- ✅ **${decreasingChallenges.map(([name]) => name).join("、")}**方面的挑战在减少，说明您的应对策略有效`);
        }
    } else {
        dv.paragraph("*未检测到明显的挑战模式，这可能表示您的适应能力很强*");
    }
}
```

---
## 📈 成长轨迹分析

```dataviewjs
// 分析个人成长轨迹和进步模式
const data = window.reviewAnalysisData;
if (data && data.reviewFiles.length >= data.minReviews) {
    // 按时间分组分析成长指标
    const timeGroups = {
        "早期": data.reviewFiles.slice(-Math.ceil(data.reviewFiles.length * 0.7)),
        "中期": data.reviewFiles.slice(-Math.ceil(data.reviewFiles.length * 0.5), -Math.ceil(data.reviewFiles.length * 0.3)),
        "近期": data.reviewFiles.slice(-Math.ceil(data.reviewFiles.length * 0.3))
    };
    
    // 成长指标关键词
    const growthKeywords = {
        "自我认知": ["反思", "认识", "发现", "意识", "理解"],
        "技能提升": ["学会", "掌握", "提高", "改进", "进步"],
        "习惯养成": ["坚持", "养成", "规律", "自律", "改变"],
        "目标达成": ["完成", "实现", "达到", "成功", "突破"],
        "情绪管理": ["平静", "放松", "调节", "控制", "稳定"]
    };
    
    let growthTrend = {};
    Object.keys(growthKeywords).forEach(category => {
        growthTrend[category] = { early: 0, middle: 0, recent: 0 };
    });
    
    // 分析各时期的成长指标
    Object.entries(timeGroups).forEach(([period, files]) => {
        files.forEach(file => {
            const fileName = file.file.name.toLowerCase();
            const tags = file.file.tags || [];
            const content = fileName + " " + tags.join(" ");
            
            Object.entries(growthKeywords).forEach(([category, keywords]) => {
                const hasKeyword = keywords.some(keyword => content.includes(keyword));
                if (hasKeyword) {
                    if (period === "早期") growthTrend[category].early++;
                    else if (period === "中期") growthTrend[category].middle++;
                    else if (period === "近期") growthTrend[category].recent++;
                }
            });
        });
    });
    
    dv.paragraph("**📈 成长轨迹分析：**");
    Object.entries(growthTrend).forEach(([category, trend]) => {
        const total = trend.early + trend.middle + trend.recent;
        if (total > 0) {
            let trendSymbol = "→";
            let trendDescription = "保持稳定";
            
            if (trend.recent > trend.early) {
                trendSymbol = "📈";
                trendDescription = "持续提升";
            } else if (trend.recent < trend.early) {
                trendSymbol = "📉"; 
                trendDescription = "需要关注";
            }
            
            const recentPercentage = Math.round(trend.recent / total * 100);
            dv.paragraph(`**${category}**: ${trendSymbol} ${trendDescription} (近期占比${recentPercentage}%)`);
        }
    });
    
    // 成长洞察
    dv.paragraph("\n**💡 成长轨迹洞察：**");
    const improvingAreas = Object.entries(growthTrend)
        .filter(([,trend]) => trend.recent > trend.early)
        .map(([category]) => category);
    
    const decliningAreas = Object.entries(growthTrend)
        .filter(([,trend]) => trend.recent < trend.early && trend.early > 0)
        .map(([category]) => category);
    
    if (improvingAreas.length > 0) {
        dv.paragraph(`- 🌟 **持续进步的领域**: ${improvingAreas.join("、")}，继续保持！`);
    }
    
    if (decliningAreas.length > 0) {
        dv.paragraph(`- ⚠️ **需要重新关注的领域**: ${decliningAreas.join("、")}，建议制定改进计划`);
    }
    
    // 整体成长评估
    const totalGrowthIndicators = Object.values(growthTrend)
        .reduce((sum, trend) => sum + trend.recent, 0);
    const growthScore = Math.min(Math.round(totalGrowthIndicators / data.reviewFiles.length * 100), 100);
    
    let growthLevel = "🌱 起步阶段";
    if (growthScore >= 80) growthLevel = "🌳 成熟阶段";
    else if (growthScore >= 60) growthLevel = "🌿 发展阶段";
    else if (growthScore >= 40) growthLevel = "🌱 成长阶段";
    
    dv.paragraph(`- 📊 **整体成长水平**: ${growthLevel} (${growthScore}分)`);
}
```

---
## 🔄 行为循环分析

```dataviewjs
// 分析行为模式和循环
const data = window.reviewAnalysisData;
if (data && data.dailyReviews.length >= 7) {
    // 按星期几分析行为模式
    const weekdayPatterns = {
        0: { name: "周日", reviews: [], mood: [] },
        1: { name: "周一", reviews: [], mood: [] },
        2: { name: "周二", reviews: [], mood: [] },
        3: { name: "周三", reviews: [], mood: [] },
        4: { name: "周四", reviews: [], mood: [] },
        5: { name: "周五", reviews: [], mood: [] },
        6: { name: "周六", reviews: [], mood: [] }
    };
    
    data.dailyReviews.forEach(review => {
        const weekday = review.file.ctime.weekday % 7; // 转换为0-6格式
        weekdayPatterns[weekday].reviews.push(review);
        if (review.mood) {
            weekdayPatterns[weekday].mood.push(review.mood);
        }
    });
    
    dv.paragraph("**🔄 周循环行为模式：**");
    Object.values(weekdayPatterns).forEach(pattern => {
        const reviewCount = pattern.reviews.length;
        const avgMood = pattern.mood.length > 0 
            ? Math.round(pattern.mood.reduce((a, b) => a + b, 0) / pattern.mood.length * 10) / 10
            : null;
        
        let moodIndicator = "";
        if (avgMood !== null) {
            if (avgMood >= 8) moodIndicator = "😄";
            else if (avgMood >= 6) moodIndicator = "😊";
            else if (avgMood >= 4) moodIndicator = "😐";
            else moodIndicator = "😔";
        }
        
        const frequency = reviewCount > 0 ? "✅" : "⏸️";
        dv.paragraph(`**${pattern.name}**: ${frequency} ${reviewCount}次复盘 ${moodIndicator} ${avgMood || "无数据"}`);
    });
    
    // 周循环洞察
    const bestDay = Object.entries(weekdayPatterns)
        .filter(([,pattern]) => pattern.mood.length > 0)
        .sort(([,a], [,b]) => {
            const avgA = a.mood.reduce((sum, mood) => sum + mood, 0) / a.mood.length;
            const avgB = b.mood.reduce((sum, mood) => sum + mood, 0) / b.mood.length;
            return avgB - avgA;
        })[0];
    
    const worstDay = Object.entries(weekdayPatterns)
        .filter(([,pattern]) => pattern.mood.length > 0)
        .sort(([,a], [,b]) => {
            const avgA = a.mood.reduce((sum, mood) => sum + mood, 0) / a.mood.length;
            const avgB = b.mood.reduce((sum, mood) => sum + mood, 0) / b.mood.length;
            return avgA - avgB;
        })[0];
    
    dv.paragraph("\n**💡 周循环洞察：**");
    if (bestDay) {
        dv.paragraph(`- 😄 **状态最佳的日子**: ${bestDay[1].name}，可以安排重要任务`);
    }
    if (worstDay) {
        dv.paragraph(`- 😔 **状态较差的日子**: ${worstDay[1].name}，建议安排轻松的活动`);
    }
    
    // 复盘频率分析
    const highFreqDays = Object.values(weekdayPatterns)
        .filter(pattern => pattern.reviews.length >= Math.ceil(data.dailyReviews.length / 7 * 1.2))
        .map(pattern => pattern.name);
    
    const lowFreqDays = Object.values(weekdayPatterns)
        .filter(pattern => pattern.reviews.length <= Math.ceil(data.dailyReviews.length / 7 * 0.5))
        .map(pattern => pattern.name);
    
    if (highFreqDays.length > 0) {
        dv.paragraph(`- 📈 **复盘频率高的日子**: ${highFreqDays.join("、")}，复盘习惯良好`);
    }
    if (lowFreqDays.length > 0) {
        dv.paragraph(`- 📉 **复盘频率低的日子**: ${lowFreqDays.join("、")}，可以设置提醒`);
    }
}
```

---
## 🎯 个性化建议

```dataviewjs
// 基于分析结果生成个性化建议
const data = window.reviewAnalysisData;
if (data && data.reviewFiles.length >= data.minReviews) {
    dv.paragraph("**🎯 基于您的复盘模式的个性化建议：**");
    
    // 基于复盘频率的建议
    const dailyFrequency = data.dailyReviews.length / data.analysisPeriod;
    if (dailyFrequency < 0.5) {
        dv.paragraph("- 📅 **提高复盘频率**: 建议设置每日提醒，逐步建立稳定的复盘习惯");
    } else if (dailyFrequency > 0.8) {
        dv.paragraph("- 🌟 **复盘习惯优秀**: 您已经建立了很好的复盘习惯，可以尝试深度复盘模式");
    }
    
    // 基于复盘级别的建议
    const beginnerCount = data.reviewFiles.filter(f => f.review_level === 'beginner').length;
    const standardCount = data.reviewFiles.filter(f => f.review_level === 'standard').length;
    
    if (beginnerCount > standardCount && beginnerCount >= 7) {
        dv.paragraph("- 🌿 **升级建议**: 您已经完成多次入门级复盘，可以尝试标准级复盘了");
    } else if (standardCount >= 20) {
        dv.paragraph("- 🌳 **专家模式**: 您已经是复盘专家，可以使用深度复盘模式探索更多可能");
    }
    
    // 基于数据量的建议
    if (data.weeklyReviews.length === 0) {
        dv.paragraph("- 📊 **周复盘建议**: 尝试添加周复盘，可以更好地总结和规划");
    }
    
    if (data.monthlyReviews.length === 0) {
        dv.paragraph("- 📈 **月复盘建议**: 月复盘可以帮助您看到更大的成长轨迹");
    }
    
    // 基于分析深度的建议
    dv.paragraph("- 🔍 **深度分析**: 定期使用模式分析器，发现自己的成长规律");
    dv.paragraph("- 🎯 **目标设定**: 基于发现的模式，设定更有针对性的改进目标");
    dv.paragraph("- 📚 **知识管理**: 将复盘中的重要洞察整理成知识库，便于回顾");
    
} else {
    dv.paragraph("**🎯 开始建议：**");
    dv.paragraph("- 📝 **建立习惯**: 先完成至少5次复盘，积累足够的数据进行分析");
    dv.paragraph("- 🌱 **从简单开始**: 使用入门级复盘模板，每天花2-3分钟记录");
    dv.paragraph("- 🎯 **设定目标**: 以21天为目标，建立稳定的复盘习惯");
}
```

---
## ⚙️ 分析设置

### 🔧 自定义分析参数
在本页面的YAML front matter中可以调整以下参数：
- `analysis_period: 30` - 分析时间范围（天数）
- `min_reviews: 5` - 最少复盘数量要求

### 📊 分析范围说明
- **短期分析** (7-14天): 适合观察近期变化
- **中期分析** (30-60天): 适合发现行为模式
- **长期分析** (90-365天): 适合分析成长轨迹

---
## 🔗 相关功能

- [[复盘系统/复盘搜索系统|🔍 复盘搜索系统]] - 搜索历史复盘
- [[复盘系统/复盘数据仪表盘|📊 复盘数据仪表盘]] - 数据统计
- [[复盘系统/复盘引导中心|🎯 复盘引导中心]] - 系统导航

---
*🧠 复盘模式分析器 v1.0 | 发现成长规律，优化人生轨迹*
