---
tags:
  - type/structure
  - structure/bujo/daily
template_type: BuJo Daily
quote: "[[浊水变澄清，全在自流中。—— 种田山头火]]"
stretch: false
journal: false
notes: false
running: false
reading: false
Stoic: 
podcast: 
book: 
Reading_min: 0
mood: 
project: 
template: "[[5_BuJo - Daily Log]]"
created: <% tp.date.now("YYYY-MM-DD") %>
updated: 2025-05-25T10:20
sleep_bedtime: 23:30
sleep_fallasleep: 00:30
sleep_wakeup: 08:30
sleep_outofbed: 08:40
sleep_quality: 3
sleep_wakeups: 
sleep_total_wakeup: 
---
## 🗓️习惯打卡
```dataviewjs
const habits = [
  { key: 'stretch', label: '🧘 晨起拉伸' },
  { key: 'journal', label: '📓 晨间日记' },
  { key: 'notes', label: '💻 整理笔记' },
  { key: 'running', label: '🏃 有氧慢跑', extra: `距离${dv.current().Distance_km ?? '+'}km 配速${dv.current().Pace_per_km ?? '+'}/km` },
  { key: 'reading', label: '📖 睡前阅读', extra: `<${dv.current().book ?? '又忘记看书啦～🙃'}>${dv.current().Reading_min ?? '+'} min` },
  { key: 'podcast', label: '👂 播客', extra: `${dv.current().podcast ?? '今天啥也没听听🤪'}` },
  { key: 'Stoic', label: '🤔 每日斯多葛', extra: `${dv.current().Stoic ?? '你又偷懒咯～😮‍💨'}` }
];

habits.forEach(habit => {
  const status = dv.current()[habit.key] ? "✅" : "🔲";
  const extra = habit.extra ? ` ${habit.extra}` : '';
  dv.paragraph(`-  ${status} ${habit.label}${extra}`);
});
```
## 😴睡眠统计
```dataviewjs
const page = dv.current();

// 转换时间字符串为分钟数
function timeToMinutes(timeStr) {
  if (!timeStr) return null;
  const [hours, minutes] = timeStr.split(':').map(Number);
  return hours * 60 + minutes;
}

// 处理跨天的情况
function calculateDuration(start, end) {
  let startMin = timeToMinutes(start);
  let endMin = timeToMinutes(end);
  // 如果没有有效的时间数据，返回0
  if (startMin === null || endMin === null) return 0;
  // 如果结束时间小于开始时间，假设跨天
  if (endMin < startMin) {
    endMin += 24 * 60;
  }
  return endMin - startMin;
}

// 获取时间数据
const bedtime = page.sleep_bedtime;
const fallAsleep = page.sleep_fallasleep;
const wakeup = page.sleep_wakeup;
const outOfBed = page.sleep_outofbed;

// 计算清醒时长总和 - 修改为优先使用 sleep_total_wakeup
const totalWakeup = page.sleep_total_wakeup || 0;

// 计算各项指标
const timeToFallAsleep = calculateDuration(bedtime, fallAsleep);
const rawSleepTime = calculateDuration(fallAsleep, wakeup); // 原始睡眠时间
const totalSleepTime = Math.max(0, rawSleepTime - totalWakeup); // 减去清醒时长
const timeInBed = calculateDuration(bedtime, outOfBed);

// 计算睡眠效率 (使用实际睡眠时间)
const sleepEfficiency = totalSleepTime && timeInBed ? Math.round((totalSleepTime / timeInBed) * 100) : 0;

// 显示结果
if (bedtime && fallAsleep && wakeup && outOfBed) {
  dv.paragraph(`- **入睡时长**: ${timeToFallAsleep} 分钟`);
  dv.paragraph(`- **清醒时长**: ${totalWakeup} 分钟`);
  dv.paragraph(`- **原始睡眠时长**: ${Math.floor(rawSleepTime/60)}小时${rawSleepTime%60}分钟 (未减去清醒时间)`);
  dv.paragraph(`- **实际睡眠时长**: ${Math.floor(totalSleepTime/60)}小时${totalSleepTime%60}分钟 (已减去清醒时间)`);
  dv.paragraph(`- **卧床时长**: ${Math.floor(timeInBed/60)}小时${timeInBed%60}分钟`);
  dv.paragraph(`- **睡眠效率**: ${sleepEfficiency}%`);
} else {
  dv.paragraph("请在YAML front matter中填写睡眠数据以查看统计");
}
```
---
## Daily logs


## Capture note

---
## 💡 想法与灵感
<!-- 快速捕捉想法、问题或灵感、临时任务，记得添加#idea标签和[[项目名称]] -->

- 
---
## 🚀 快速任务捕获
<!-- 快速记录任务，后续可以整理到项目笔记或专门的任务笔记中 -->
<!-- 格式示例: - [ ] 学习设置 [[项目名称]]  [due:: YYYY-MM-DD] High-⏫ Medium-🔼 Low-🔽   进行中🔄  🍅1 #重要/不重要-->


---
## 📥 待规划任务 (Review & Plan Today's Tasks)
*在这里查看今天到期或已过期的任务，然后去[[任务仪表盘]]更改任务*
```tasks
not done
(due on today OR due before today) 
sort by due
short
```
### 🕳️ 未分类任务（可选）
```tasks
not done
priority is none
due on today
sort by priority
short
```
## 📝 今日计划 (Today's Plan)

### 🔥 必做 (High-⏫)
```tasks
not done
priority is high
due on today
sort by priority
short
```

### 💪 有精力就做 (Medium-🔼)
```tasks
not done
priority is medium
due on today
sort by priority
short
```

### ✨ Bonus（Low-🔽）
```tasks
not done
priority is low
due on today
sort by priority
short
```

## ✅ 今日完成 (Done Today)
```tasks
done on <% tp.date.now("YYYY-MM-DD", 0, tp.file.title, "YYYY-MM-DD") %>
sort by done reverse
short
```

---
## 👀今日回顾
### 🌟 高光时刻 Highlights 
<!-- What important tasks did you complete? What progress is worth celebrating? -->
- 
### 🚧 突破障碍 Overcoming Obstacles
<!-- What difficulties or obstacles did you encounter? How did you address these challenges? -->
- 
### 🎁 知识获取 Knowledge Gains
<!-- What new knowledge or skills did you learn? What new inspirations or ideas did you have? -->
- 
### 😊 情绪地图 Emotional Map
<!-- How was your emotional state today? What influenced your emotions? -->
- 
### 🔧 优化空间 Room for Improvement
<!-- What could be improved today? How can you do better tomorrow? -->
-

---
## 📝 日复盘
> 基于复盘.txt的日复盘框架，帮助系统性地回顾和总结每一天

### 🎯 今日成就
**今天做了什么？做这些事情用了多少时间？**
-

### 🤔 今日反思
**今天做了哪些应该做的事情？做了哪些不应该做的事情？**
- 应该做的事：
- 不应该做的事：

**今天遇到了什么问题，问题的根本是？**
-

**今天在时间分配上，有什么可以改进？**
-

### 💡 今日收获
**今天有哪些收获，包括感受和启发？**
-

### 🌸 今日美好
**今天遇到了什么美好的事情？**
-

### 🙏 今日感恩
**今天遇到了什么值得感恩的事？**
-

---
## 今天创建的文件
```dataview
TABLE without id file.ctime AS Time, file.link AS Name, file.folder AS Folder
where file.ctime > this.created and file.ctime < (this.created + dur(1 day))
sort file.ctime desc
```
## 今天修改的文件
```dataview
TABLE without id file.mtime AS Time, file.link AS Name, file.folder AS Folder
WHERE file.mtime >= date(regexreplace(this.file.name, "^(\d{4}-\d{2}-\d{2}).*", "$1")) 
AND file.mtime < date(regexreplace(this.file.name, "^(\d{4}-\d{2}-\d{2}).*", "$1")) + dur(1 day)
SORT file.mtime DESC
```


