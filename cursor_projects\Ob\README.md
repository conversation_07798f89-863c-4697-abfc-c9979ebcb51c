# 🌟 精力记录查询系统 - 使用说明

> 📅 创建时间：2025-08-01 星期五
> 🎯 版本：v2.0 优化版
> 📊 功能：精力记录查询 + 月度统计报表 + 数据可视化

## 📋 项目概述

本项目是对原有《精力记录查询.md》文件的全面优化升级，新增了美观的HTML月度统计报表功能，提供了交互式图表和数据分析能力。

### 🎯 主要功能

1. **📊 月度统计报表** - 美观的HTML界面，展示精力记录的月度趋势
2. **🔍 智能查询系统** - 按类型查询和关键词搜索功能
3. **📈 数据可视化** - 交互式图表展示各种统计数据
4. **🏷️ 影响因素分析** - 智能识别和统计常见影响因素

## 📁 文件结构

```
cursor_projects/Ob/
├── energy_dashboard.html      # 月度统计报表HTML界面
├── energy_analyzer.py         # 数据分析脚本
├── energy_report_data.json    # 分析结果数据文件
└── README.md                  # 使用说明文档

根目录/
└── 精力记录查询_优化版.md      # 优化版Obsidian查询界面
```

## 🚀 使用方法

### 1. 数据分析和报表生成

首先运行数据分析脚本：

```bash
# 在项目根目录执行
python cursor_projects/Ob/energy_analyzer.py
```

脚本会：
- 扫描 `obsidian-vault/0_Bullet Journal/Daily Notes/` 目录下的所有日记文件
- 提取精力记录数据
- 按月份进行统计分析
- 生成 `energy_report_data.json` 数据文件

### 2. 查看月度统计报表

打开 `cursor_projects/Ob/energy_dashboard.html` 文件：

- **方式1**：直接双击HTML文件在浏览器中打开
- **方式2**：在Obsidian中通过优化版查询界面的"打开报表"按钮访问

### 3. 使用Obsidian查询功能

在Obsidian中打开 `精力记录查询_优化版.md` 文件，可以使用：

- **月度统计报表入口** - 快速访问HTML报表
- **按类型查询** - 筛选特定类型的精力记录
- **关键词搜索** - 搜索包含特定关键词的记录
- **影响因素分析** - 查看智能分析结果

## 📊 报表功能详解

### 统计概览卡片
- 总记录数
- 覆盖月份数
- 总活跃天数
- 月均记录数

### 交互式图表
1. **📈 月度记录趋势** - 折线图显示每月记录数量变化
2. **🏷️ 影响因素分布** - 饼图展示各影响因素占比
3. **📊 记录类型分布** - 柱状图显示不同类型记录数量
4. **📅 每月活跃天数** - 柱状图显示每月记录天数

### 月度详细统计表格
- 每月总记录数
- 每月活跃天数
- 主要影响因素（前5个）
- 记录类型分布

## 🔍 数据识别规则

### 精力记录识别
系统会识别包含以下标记的记录：
- `【健康】精力`
- `#精力/`

### 影响因素关键词
系统会自动识别以下影响因素：
- **生理因素**：SY、感冒、抽筋、拉肚子、MY、喉咙发炎、口腔溃疡、低烧、疲惫
- **补充剂**：维生素D、钙片
- **调理方式**：中药、调理、晒太阳、恢复
- **医疗相关**：医院、看病、检查

### 记录类型分类
- **补充剂** - 维生素、营养素等
- **中医** - 中药、调理等
- **西医** - 医院、检查、西药等
- **习惯** - SY、梦遗等
- **恢复** - 晒太阳、放松、冥想等
- **调理** - 感冒、抽筋等症状处理
- **其他** - 未分类的记录

## 🎨 界面特色

### 现代化设计
- 渐变色背景和卡片式布局
- 响应式设计，适配不同屏幕尺寸
- 悬停动效和交互反馈

### 数据可视化
- Chart.js 图表库提供专业图表
- 支持数据点悬停显示详细信息
- 颜色编码便于快速识别

### 用户体验
- 加载状态提示
- 错误处理和友好提示
- 直观的数据展示方式

## 🔧 技术实现

### 后端数据处理
- **Python脚本**：使用正则表达式解析Markdown文件
- **数据结构**：JSON格式存储分析结果
- **统计算法**：频次统计、月度聚合、趋势分析

### 前端界面
- **HTML5 + CSS3**：现代化界面设计
- **Chart.js**：专业图表库
- **JavaScript ES6**：异步数据加载和DOM操作

### Obsidian集成
- **DataviewJS**：动态查询和界面生成
- **内部链接**：无缝跳转到具体日记
- **样式定制**：与Obsidian主题协调的设计

## 📈 数据洞察示例

根据实际分析结果，系统提供以下洞察：

### 时间范围
- 数据覆盖：2025-03 至 2025-06
- 总记录数：258 条
- 覆盖月份：4 个月

### 主要发现
- **最常见影响因素**：中药 (108次)、维生素D (69次)
- **记录类型分布**：其他类型占主导地位
- **月度趋势**：4月份记录数量最高 (97条)

## 🚀 未来优化方向

1. **数据导出功能** - 支持Excel、PDF格式导出
2. **更多图表类型** - 热力图、散点图等
3. **智能建议** - 基于数据模式提供健康建议
4. **移动端优化** - 更好的移动设备体验
5. **实时更新** - 自动检测数据变化并更新报表

## 📞 技术支持

如遇到问题或需要功能改进，请：
1. 检查数据文件路径是否正确
2. 确认Python环境和依赖包
3. 验证Obsidian DataviewJS插件状态
4. 查看浏览器控制台错误信息

---

*📝 备注：本系统基于Obsidian的精力记录数据，需要确保数据格式符合识别规则才能获得准确的分析结果。*
