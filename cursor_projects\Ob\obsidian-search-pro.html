<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian搜索系统 Pro - 专业版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 25px;
            box-shadow: 0 25px 80px rgba(0,0,0,0.15);
            max-width: 1000px;
            width: 100%;
            margin: 0 auto;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 50px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px);
            background-size: 50px 50px;
            animation: float 20s infinite linear;
        }
        
        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }
        
        .header h1 {
            font-size: 3em;
            margin-bottom: 15px;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }
        
        .header .subtitle {
            font-size: 1.3em;
            opacity: 0.95;
            position: relative;
            z-index: 1;
            margin-bottom: 20px;
        }
        
        .pro-badge {
            background: linear-gradient(45deg, #ff6b6b, #feca57);
            padding: 8px 20px;
            border-radius: 25px;
            font-weight: bold;
            display: inline-block;
            position: relative;
            z-index: 1;
        }
        
        .content {
            padding: 50px 40px;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-bottom: 50px;
        }
        
        .feature-card {
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            border-left: 5px solid;
        }
        
        .feature-card:nth-child(1) { border-left-color: #007bff; }
        .feature-card:nth-child(2) { border-left-color: #28a745; }
        .feature-card:nth-child(3) { border-left-color: #ffc107; }
        .feature-card:nth-child(4) { border-left-color: #dc3545; }
        .feature-card:nth-child(5) { border-left-color: #6f42c1; }
        
        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }
        
        .feature-icon {
            font-size: 2.5em;
            margin-bottom: 15px;
            display: block;
        }
        
        .feature-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.4em;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            color: #6c757d;
            margin-bottom: 8px;
            padding-left: 20px;
            position: relative;
        }
        
        .feature-list li::before {
            content: '✨';
            position: absolute;
            left: 0;
        }
        
        .advanced-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
        }
        
        .advanced-section h2 {
            font-size: 2.2em;
            margin-bottom: 20px;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .tech-item {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
        }
        
        .tech-item h4 {
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .users-pro {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            gap: 25px;
            margin: 40px 0;
        }
        
        .user-pro {
            background: linear-gradient(145deg, #ffffff, #f8f9fa);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            border-top: 4px solid;
        }
        
        .user-pro:nth-child(1) { border-top-color: #007bff; }
        .user-pro:nth-child(2) { border-top-color: #28a745; }
        .user-pro:nth-child(3) { border-top-color: #ffc107; }
        .user-pro:nth-child(4) { border-top-color: #dc3545; }
        .user-pro:nth-child(5) { border-top-color: #6f42c1; }
        
        .user-pro h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2em;
        }
        
        .user-pro p {
            color: #6c757d;
            font-style: italic;
        }
        
        .comparison {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
        }
        
        .comparison h3 {
            color: #d63384;
            text-align: center;
            margin-bottom: 20px;
            font-size: 1.6em;
        }
        
        .vs-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .vs-item {
            background: white;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }
        
        .vs-item h4 {
            margin-bottom: 10px;
            color: #495057;
        }
        
        .vs-traditional {
            border-left: 4px solid #dc3545;
        }
        
        .vs-pro {
            border-left: 4px solid #28a745;
        }
        
        .cta-final {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            margin-top: 40px;
        }
        
        .cta-final h2 {
            font-size: 2.5em;
            margin-bottom: 20px;
        }
        
        .benefits {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .benefit {
            background: rgba(255,255,255,0.1);
            border-radius: 15px;
            padding: 20px;
            margin: 10px;
            min-width: 200px;
            backdrop-filter: blur(10px);
        }
        
        .benefit h4 {
            margin-bottom: 10px;
            font-size: 1.2em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚡ Obsidian搜索系统 Pro</h1>
            <p class="subtitle">专业级智能搜索 · 重新定义效率</p>
            <span class="pro-badge">🚀 终极解决方案</span>
        </div>
        
        <div class="content">
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 30px; font-size: 2em;">🧠 五大高级功能</h2>
            
            <div class="features-grid">
                <div class="feature-card">
                    <span class="feature-icon">🌍</span>
                    <h3>自适应目录检测</h3>
                    <ul class="feature-list">
                        <li>智能扫描vault结构</li>
                        <li>自动生成目录选项</li>
                        <li>动态配置系统</li>
                        <li>零配置使用</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">🎯</span>
                    <h3>智能相关性评分</h3>
                    <ul class="feature-list">
                        <li>TF-IDF算法加持</li>
                        <li>文件名权重优化</li>
                        <li>位置权重分析</li>
                        <li>科学计算匹配度</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">📁</span>
                    <h3>多种文件类型过滤</h3>
                    <ul class="feature-list">
                        <li>支持6大文件类型</li>
                        <li>精准类型识别</li>
                        <li>跨格式搜索</li>
                        <li>统一管理多媒体</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">📄</span>
                    <h3>智能上下文提取</h3>
                    <ul class="feature-list">
                        <li>多重匹配显示</li>
                        <li>上下文预览</li>
                        <li>行号精确定位</li>
                        <li>匹配位置展示</li>
                    </ul>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">⚡</span>
                    <h3>异步分批处理</h3>
                    <ul class="feature-list">
                        <li>50文件/批智能处理</li>
                        <li>实时进度显示</li>
                        <li>可取消搜索</li>
                        <li>大型vault流畅运行</li>
                    </ul>
                </div>
            </div>
            
            <div class="advanced-section">
                <h2>🔧 技术优势详解</h2>
                <div class="tech-grid">
                    <div class="tech-item">
                        <h4>🧮 算法优势</h4>
                        <p>TF-IDF相关性算法，异步处理架构，智能缓存机制</p>
                    </div>
                    <div class="tech-item">
                        <h4>🎨 体验优势</h4>
                        <p>现代化界面，实时进度反馈，多重降级方案</p>
                    </div>
                    <div class="tech-item">
                        <h4>🛡️ 稳定性优势</h4>
                        <p>完善错误处理，100%避免崩溃，大数据量稳定</p>
                    </div>
                </div>
            </div>
            
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 30px;">👥 专业用户群体</h2>
            <div class="users-pro">
                <div class="user-pro">
                    <h4>🏢 知识管理专家</h4>
                    <p>"管理数千份文档，效率提升300%"</p>
                </div>
                <div class="user-pro">
                    <h4>🔬 科研工作者</h4>
                    <p>"处理大量研究资料，加速科研进程"</p>
                </div>
                <div class="user-pro">
                    <h4>📊 项目经理</h4>
                    <p>"多类型文件统一搜索，决策更及时"</p>
                </div>
                <div class="user-pro">
                    <h4>💡 内容创作者</h4>
                    <p>"跨媒体素材检索，创作效率倍增"</p>
                </div>
                <div class="user-pro">
                    <h4>🔧 技术爱好者</h4>
                    <p>"追求极致的工具体验"</p>
                </div>
            </div>
            
            <div class="comparison">
                <h3>🆚 对比优势</h3>
                <div class="vs-grid">
                    <div class="vs-item vs-traditional">
                        <h4>传统搜索</h4>
                        <p>单一关键词<br>结果混乱<br>功能受限</p>
                    </div>
                    <div class="vs-item vs-pro">
                        <h4>Pro版搜索</h4>
                        <p>智能评分<br>精准排序<br>功能完整</p>
                    </div>
                    <div class="vs-item vs-traditional">
                        <h4>其他工具</h4>
                        <p>复杂配置<br>学习成本高<br>更新依赖</p>
                    </div>
                    <div class="vs-item vs-pro">
                        <h4>Pro版工具</h4>
                        <p>零配置<br>即插即用<br>开源免费</p>
                    </div>
                </div>
            </div>
            
            <div class="cta-final">
                <h2>🌟 立即体验专业级搜索</h2>
                <div class="benefits">
                    <div class="benefit">
                        <h4>💎 专业级功能</h4>
                        <p>零成本获得</p>
                    </div>
                    <div class="benefit">
                        <h4>🚀 一次安装</h4>
                        <p>终身受益</p>
                    </div>
                    <div class="benefit">
                        <h4>🛡️ 稳定可靠</h4>
                        <p>安全无忧</p>
                    </div>
                </div>
                <p style="font-size: 1.3em; margin-top: 20px;">
                    <strong>重新定义你的Obsidian搜索体验！</strong>
                </p>
            </div>
        </div>
    </div>
</body>
</html>
