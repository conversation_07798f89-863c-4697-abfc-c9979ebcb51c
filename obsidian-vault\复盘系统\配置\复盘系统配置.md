---
tags:
  - config
  - review-system
created: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
updated: <% tp.date.now("YYYY-MM-DDTHH:mm") %>
---
# ⚙️ 智能复盘系统配置

> 复盘系统的核心配置文件，定义复盘维度、提醒设置和个性化选项

## 📋 系统信息

- **版本**: MVP v1.0
- **创建时间**: <% tp.date.now("YYYY-MM-DD HH:mm") %>
- **配置类型**: 模板增强法
- **集成状态**: ✅ 已完成

## 🎯 复盘维度配置

### 日复盘维度
```yaml
daily_review_dimensions:
  - name: "今日成就"
    description: "今天做了什么？做这些事情用了多少时间？"
    required: true
    
  - name: "今日反思"
    description: "应该做的事、不应该做的事、问题根本原因、时间分配改进"
    required: true
    
  - name: "今日收获"
    description: "今天有哪些收获，包括感受和启发？"
    required: true
    
  - name: "今日美好"
    description: "今天遇到了什么美好的事情？"
    required: false
    
  - name: "今日感恩"
    description: "今天遇到了什么值得感恩的事？"
    required: false
```

### 周复盘维度
```yaml
weekly_review_dimensions:
  - name: "汇总本周事件"
    description: "本周结果、意外收获、重要会面、问题进展"
    required: true
    
  - name: "计划完成情况"
    description: "工作交付、未完成任务、生活状态"
    required: true
    
  - name: "本周反思"
    description: "问题分析、改进方式"
    required: true
    
  - name: "复盘收获"
    description: "发现问题、总结经验、学习内容"
    required: true
    
  - name: "下周调整与计划"
    description: "找原因、合理规划、工作生活平衡"
    required: true
```

### 月复盘维度
```yaml
monthly_review_dimensions:
  - name: "汇总本月成就"
    description: "家庭生活、工作进展、项目情况"
    required: true
    
  - name: "梳理月度大事件"
    description: "收支情况、健康状况"
    required: true
    
  - name: "月目标完成情况"
    description: "挑战与成就、目标完成率"
    required: true
    
  - name: "情绪管理"
    description: "高兴沮丧的事、情绪管理状态"
    required: true
    
  - name: "经验教训"
    description: "总结经验教训"
    required: true
    
  - name: "满意度评分"
    description: "工作、家庭、健康、财务、人际、成长多维度评价"
    required: true
    
  - name: "自我认识"
    description: "新发现、潜力挖掘"
    required: false
    
  - name: "下月目标"
    description: "个人、事业、家庭、社会目标"
    required: true
```

### 年复盘维度
```yaml
yearly_review_dimensions:
  - name: "汇总本年成就"
    description: "整体状态、关键成就、习惯养成"
    required: true
    
  - name: "目标完成情况"
    description: "年度目标完成率对比"
    required: true
    
  - name: "收获感悟"
    description: "各方面收获与感悟"
    required: true
    
  - name: "深刻领悟与教训"
    description: "最大教训、规避方法"
    required: true
    
  - name: "年满意度打分"
    description: "多维度满意度评价"
    required: true
    
  - name: "回到1.1你会怎么过"
    description: "重来一次的改进建议"
    required: false
    
  - name: "新年五个改变清单"
    description: "未来发展目标设置"
    required: true
    
  - name: "新一年目标愿望"
    description: "可量化的新年目标"
    required: true
```

## 🔔 提醒设置

### 复盘提醒配置
```yaml
reminder_settings:
  daily_review:
    enabled: true
    time: "21:00"
    message: "📝 今日复盘时间到了，回顾一下今天的收获吧！"
    
  weekly_review:
    enabled: true
    day: "Sunday"
    time: "20:00"
    message: "📊 周复盘时间，总结本周的成长和收获！"
    
  monthly_review:
    enabled: true
    day: "last_day"
    time: "19:00"
    message: "📈 月度复盘时间，深度回顾这个月的成长轨迹！"
    
  yearly_review:
    enabled: true
    date: "12-31"
    time: "18:00"
    message: "🎉 年度复盘时间，回顾这一年的成长历程！"
```

## 📊 数据展示配置

### 仪表盘设置
```yaml
dashboard_settings:
  show_completion_stats: true
  show_recent_reviews: true
  show_quality_analysis: true
  show_trend_analysis: true
  recent_days_limit: 7
  recent_weeks_limit: 4
  recent_months_limit: 12
```

### 可视化选项
```yaml
visualization_options:
  completion_chart: true
  frequency_trend: true
  quality_heatmap: false  # 暂未实现
  satisfaction_radar: false  # 暂未实现
```

## 🎨 个性化设置

### 用户偏好
```yaml
user_preferences:
  language: "zh-CN"
  date_format: "YYYY-MM-DD"
  time_format: "24h"
  theme: "default"
  
  # 复盘风格偏好
  review_style:
    detailed: true  # 详细模式 vs 简洁模式
    include_prompts: true  # 是否包含提示问题
    auto_fill_data: false  # 是否自动填充数据（暂未实现）
```

### 模板路径配置
```yaml
template_paths:
  daily: "Templates/5_BuJo - Daily Log.md"
  weekly: "Templates/5_BuJo - Weekly Log.md"
  monthly: "Templates/5_BuJo - Monthly Log.md"
  yearly: "Templates/5_BuJo - Yearly Log.md"
  
output_paths:
  daily: "0_Bullet Journal/Daily Notes"
  weekly: "0_Bullet Journal/Weekly Notes"
  monthly: "0_Bullet Journal/Monthly Notes"
  yearly: "0_Bullet Journal/Yearly Notes"
```

## 🔧 系统集成配置

### MCP工具集成
```yaml
mcp_integration:
  obsidian_mcp:
    enabled: true
    auto_sync: false
    
  memory_mcp:
    enabled: false  # 暂未实现
    knowledge_graph: false
    
  sequential_thinking:
    enabled: false  # 暂未实现
    analysis_depth: "basic"
```

### Dataview查询配置
```yaml
dataview_queries:
  enable_advanced_queries: true
  cache_results: false
  max_results_per_query: 100
  
  # 自定义查询
  custom_queries:
    recent_reviews: true
    completion_stats: true
    trend_analysis: true
```

## 📈 性能设置

### 缓存配置
```yaml
cache_settings:
  enable_template_cache: false
  enable_query_cache: false
  cache_duration_minutes: 60
```

### 限制设置
```yaml
limits:
  max_daily_reviews_display: 30
  max_weekly_reviews_display: 12
  max_monthly_reviews_display: 12
  max_search_results: 50
```

## 🚀 功能开关

### MVP功能状态
```yaml
features:
  # 已实现功能
  template_enhancement: true
  basic_dashboard: true
  dataview_integration: true
  
  # 计划功能（暂未实现）
  smart_analysis: false
  auto_data_collection: false
  trend_prediction: false
  export_reports: false
  mobile_optimization: false
```

---
## 📝 使用说明

### 如何修改配置
1. 直接编辑此文件中的YAML配置块
2. 修改后重新加载相关模板
3. 在仪表盘中验证配置是否生效

### 配置生效范围
- 模板配置：影响新创建的复盘笔记
- 提醒设置：需要配合提醒插件使用
- 仪表盘设置：立即生效
- 个性化设置：影响用户体验

### 故障排除
- 如果Dataview查询不工作，检查路径配置是否正确
- 如果模板内容不显示，确认模板文件是否存在
- 如果仪表盘数据异常，检查笔记文件的标签和结构

---
*配置文件版本：MVP v1.0*  
*最后更新：<% tp.date.now("YYYY-MM-DD HH:mm") %>*
