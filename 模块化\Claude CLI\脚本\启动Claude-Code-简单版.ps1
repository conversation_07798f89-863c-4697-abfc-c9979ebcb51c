# Claude Code 启动脚本 - 简单版
# 适合右键"使用 PowerShell 运行"

Write-Host "======================================" -ForegroundColor Cyan
Write-Host "        启动 Claude Code" -ForegroundColor Cyan  
Write-Host "======================================" -ForegroundColor Cyan
Write-Host ""

# 设置环境变量
$env:CLAUDE_CODE_MAX_OUTPUT_TOKENS = "64000"
$env:ANTHROPIC_BASE_URL = "https://code.wenwen-ai.com/v1"
$env:ANTHROPIC_AUTH_TOKEN = "sk-hzzntp11lud8ep8cT3UC7m2VNLsLpQWZvVAHQ4qS3GuKYxuU"

Write-Host "配置信息:" -ForegroundColor Green
Write-Host "  API 地址: $env:ANTHROPIC_BASE_URL" -ForegroundColor Yellow
Write-Host "  Token 限制: $env:CLAUDE_CODE_MAX_OUTPUT_TOKENS" -ForegroundColor Yellow
Write-Host ""

Write-Host "正在启动 Claude Code..." -ForegroundColor Green
Write-Host "提示: 如果出现选择界面，请按需选择主题和设置" -ForegroundColor Cyan
Write-Host ""

try {
    # 检查 Git Bash 是否存在
    if (Test-Path "C:\Program Files\Git\usr\bin\bash.exe") {
        & "C:\Program Files\Git\usr\bin\bash.exe" -c "export SHELL='/usr/bin/bash'; export CLAUDE_CODE_MAX_OUTPUT_TOKENS='$env:CLAUDE_CODE_MAX_OUTPUT_TOKENS'; export ANTHROPIC_BASE_URL='$env:ANTHROPIC_BASE_URL'; export ANTHROPIC_AUTH_TOKEN='$env:ANTHROPIC_AUTH_TOKEN'; claude"
    } else {
        Write-Host "错误: 未找到 Git Bash，请确保已安装 Git for Windows" -ForegroundColor Red
        Write-Host "下载地址: https://git-scm.com/download/win" -ForegroundColor Yellow
    }
} catch {
    Write-Host ""
    Write-Host "启动过程中出现错误:" -ForegroundColor Red
    Write-Host $_.Exception.Message -ForegroundColor Red
    Write-Host ""
    Write-Host "可能的解决方案:" -ForegroundColor Yellow
    Write-Host "1. 确保已安装 Git for Windows" -ForegroundColor White
    Write-Host "2. 确保 Claude Code 已正确安装 (npm install -g @anthropic-ai/claude-code)" -ForegroundColor White
    Write-Host "3. 检查网络连接" -ForegroundColor White
}

Write-Host ""
Write-Host "======================================" -ForegroundColor Cyan
Write-Host "按任意键关闭此窗口..." -ForegroundColor Green
Write-Host "======================================" -ForegroundColor Cyan
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
