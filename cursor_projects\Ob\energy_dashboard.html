<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>精力记录月度统计报表</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/date-fns@2.29.3/index.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            padding: 30px;
            background: #f8fafc;
        }

        .stat-card {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            color: #4facfe;
            margin-bottom: 10px;
        }

        .stat-label {
            color: #64748b;
            font-size: 1rem;
        }

        .dashboard-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .chart-container {
            background: white;
            padding: 25px;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .chart-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 20px;
            text-align: center;
        }

        .monthly-table {
            width: 100%;
            margin: 30px;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .table-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 1.3rem;
            font-weight: 600;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }

        th {
            background: #f1f5f9;
            font-weight: 600;
            color: #475569;
        }

        tr:hover {
            background: #f8fafc;
        }

        .factor-tag {
            display: inline-block;
            background: #e0f2fe;
            color: #0369a1;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            margin: 2px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #64748b;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #dc2626;
        }

        @media (max-width: 768px) {
            .dashboard-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats-overview {
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌟 精力记录月度统计报表</h1>
            <p id="dateRange">数据加载中...</p>
        </div>

        <div class="stats-overview" id="statsOverview">
            <div class="loading">正在加载统计数据...</div>
        </div>

        <div class="dashboard-grid">
            <div class="chart-container">
                <div class="chart-title">📈 月度记录趋势</div>
                <canvas id="monthlyTrendChart"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-title">🏷️ 影响因素分布</div>
                <canvas id="factorsChart"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-title">📊 记录类型分布</div>
                <canvas id="typesChart"></canvas>
            </div>

            <div class="chart-container">
                <div class="chart-title">📅 每月活跃天数</div>
                <canvas id="activeDaysChart"></canvas>
            </div>
        </div>

        <div class="monthly-table">
            <div class="table-header">📋 月度详细统计</div>
            <table id="monthlyDetailsTable">
                <thead>
                    <tr>
                        <th>月份</th>
                        <th>总记录数</th>
                        <th>活跃天数</th>
                        <th>主要影响因素</th>
                        <th>记录类型</th>
                    </tr>
                </thead>
                <tbody id="monthlyTableBody">
                    <tr>
                        <td colspan="5" class="loading">正在加载月度数据...</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        // 全局变量
        let reportData = null;
        let charts = {};

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadReportData();
        });

        // 加载报告数据
        async function loadReportData() {
            try {
                const response = await fetch('energy_report_data.json');
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                reportData = await response.json();
                
                // 渲染所有组件
                renderStatsOverview();
                renderCharts();
                renderMonthlyTable();
                updateDateRange();
                
            } catch (error) {
                console.error('加载数据失败:', error);
                showError('数据加载失败，请检查 energy_report_data.json 文件是否存在');
            }
        }

        // 显示错误信息
        function showError(message) {
            document.getElementById('statsOverview').innerHTML = 
                `<div class="error">${message}</div>`;
        }

        // 更新日期范围
        function updateDateRange() {
            if (reportData && reportData.overall_stats.date_range) {
                document.getElementById('dateRange').textContent = 
                    `数据范围：${reportData.overall_stats.date_range}`;
            }
        }

        // 渲染统计概览
        function renderStatsOverview() {
            const stats = reportData.overall_stats;
            const monthlyDetails = reportData.monthly_details;

            // 计算平均每月记录数
            const avgMonthlyRecords = Math.round(stats.total_records / reportData.months.length);

            // 计算总活跃天数
            const totalActiveDays = Object.values(monthlyDetails)
                .reduce((sum, month) => sum + month.unique_days, 0);

            const statsHTML = `
                <div class="stat-card">
                    <div class="stat-number">${stats.total_records}</div>
                    <div class="stat-label">总记录数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${reportData.months.length}</div>
                    <div class="stat-label">覆盖月份</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${totalActiveDays}</div>
                    <div class="stat-label">总活跃天数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${avgMonthlyRecords}</div>
                    <div class="stat-label">月均记录数</div>
                </div>
            `;

            document.getElementById('statsOverview').innerHTML = statsHTML;
        }

        // 渲染所有图表
        function renderCharts() {
            renderMonthlyTrendChart();
            renderFactorsChart();
            renderTypesChart();
            renderActiveDaysChart();
        }

        // 月度趋势图
        function renderMonthlyTrendChart() {
            const ctx = document.getElementById('monthlyTrendChart').getContext('2d');

            const monthlyData = reportData.months.map(month =>
                reportData.monthly_details[month].total_records
            );

            charts.monthlyTrend = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: reportData.months,
                    datasets: [{
                        label: '记录数量',
                        data: monthlyData,
                        borderColor: '#4facfe',
                        backgroundColor: 'rgba(79, 172, 254, 0.1)',
                        borderWidth: 3,
                        fill: true,
                        tension: 0.4,
                        pointBackgroundColor: '#4facfe',
                        pointBorderColor: '#fff',
                        pointBorderWidth: 2,
                        pointRadius: 6
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        x: {
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });
        }

        // 影响因素分布图
        function renderFactorsChart() {
            const ctx = document.getElementById('factorsChart').getContext('2d');

            const factors = reportData.overall_stats.most_common_factors;
            const labels = Object.keys(factors).slice(0, 8); // 取前8个
            const data = labels.map(label => factors[label]);

            const colors = [
                '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0',
                '#9966FF', '#FF9F40', '#FF6384', '#C9CBCF'
            ];

            charts.factors = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: data,
                        backgroundColor: colors,
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        }
                    }
                }
            });
        }

        // 记录类型分布图
        function renderTypesChart() {
            const ctx = document.getElementById('typesChart').getContext('2d');

            const types = reportData.overall_stats.most_common_types;
            const labels = Object.keys(types);
            const data = labels.map(label => types[label]);

            charts.types = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: '记录数量',
                        data: data,
                        backgroundColor: 'rgba(79, 172, 254, 0.8)',
                        borderColor: '#4facfe',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        // 每月活跃天数图
        function renderActiveDaysChart() {
            const ctx = document.getElementById('activeDaysChart').getContext('2d');

            const activeDaysData = reportData.months.map(month =>
                reportData.monthly_details[month].unique_days
            );

            charts.activeDays = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: reportData.months,
                    datasets: [{
                        label: '活跃天数',
                        data: activeDaysData,
                        backgroundColor: 'rgba(118, 75, 162, 0.8)',
                        borderColor: '#764ba2',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 31
                        }
                    }
                }
            });
        }

        // 渲染月度详细表格
        function renderMonthlyTable() {
            const tbody = document.getElementById('monthlyTableBody');
            let tableHTML = '';

            reportData.months.forEach(month => {
                const details = reportData.monthly_details[month];

                // 格式化主要影响因素
                const topFactors = Object.entries(details.factors)
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 5)
                    .map(([factor, count]) =>
                        `<span class="factor-tag">${factor} (${count})</span>`
                    ).join(' ');

                // 格式化记录类型
                const topTypes = Object.entries(details.types)
                    .sort((a, b) => b[1] - a[1])
                    .slice(0, 3)
                    .map(([type, count]) => `${type}: ${count}`)
                    .join(', ');

                tableHTML += `
                    <tr>
                        <td><strong>${month}</strong></td>
                        <td>${details.total_records}</td>
                        <td>${details.unique_days}</td>
                        <td>${topFactors || '无'}</td>
                        <td>${topTypes || '无'}</td>
                    </tr>
                `;
            });

            tbody.innerHTML = tableHTML;
        }

        // 工具函数：格式化数字
        function formatNumber(num) {
            return num.toLocaleString();
        }

        // 工具函数：获取月份中文名
        function getMonthName(monthStr) {
            const [year, month] = monthStr.split('-');
            return `${year}年${parseInt(month)}月`;
        }
    </script>
</body>
</html>
