#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精力记录数据分析器
分析Obsidian日记中的精力记录数据，生成月度统计报表
"""

import os
import re
import json
from datetime import datetime
from collections import defaultdict, Counter
from pathlib import Path

class EnergyAnalyzer:
    def __init__(self, daily_notes_path):
        self.daily_notes_path = Path(daily_notes_path)
        self.energy_records = []
        self.monthly_stats = defaultdict(lambda: {
            'total_records': 0,
            'factors': Counter(),
            'types': Counter(),
            'effects': [],
            'dates': []
        })
        
        # 定义影响因素关键词
        self.factor_keywords = {
            'SY': ['sy', 'SY'],
            '感冒': ['感冒', '流鼻涕', '打喷嚏', '鼻塞'],
            '抽筋': ['抽筋', '腿抽筋'],
            '拉肚子': ['拉肚子', '腹泻', '肚子疼'],
            'MY': ['my', 'MY', '梦遗'],
            '喉咙发炎': ['喉咙发炎', '喉咙痛', '咽喉炎'],
            '口腔溃疡': ['口腔溃疡', '溃疡'],
            '低烧': ['低烧', '发烧', '发热'],
            '疲惫': ['疲惫', '疲劳', '累'],
            '维生素D': ['维生素d', '维生素D'],
            '钙片': ['钙片'],
            '中药': ['中药', '中医', '调理'],
            '晒太阳': ['晒太阳', '阳光'],
            '医院': ['医院', '看病', '检查']
        }
        
        # 精力记录类型
        self.energy_types = ['补充剂', '调理', '中医', '西医', '习惯', '恢复', '其他']

    def extract_energy_records(self):
        """提取所有精力记录"""
        if not self.daily_notes_path.exists():
            print(f"路径不存在: {self.daily_notes_path}")
            return
            
        for file_path in self.daily_notes_path.glob("*.md"):
            if file_path.name.startswith('2025-'):
                self._process_daily_file(file_path)
        
        print(f"总共提取到 {len(self.energy_records)} 条精力记录")

    def _process_daily_file(self, file_path):
        """处理单个日记文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取日期
            date_match = re.search(r'(\d{4}-\d{2}-\d{2})', file_path.name)
            if not date_match:
                return
                
            date_str = date_match.group(1)
            month_key = date_str[:7]  # YYYY-MM
            
            # 查找精力记录
            lines = content.split('\n')
            for line in lines:
                if self._is_energy_record(line):
                    record = self._parse_energy_record(line, date_str, file_path.name)
                    if record:
                        self.energy_records.append(record)
                        self._update_monthly_stats(record, month_key)
                        
        except Exception as e:
            print(f"处理文件 {file_path} 时出错: {e}")

    def _is_energy_record(self, line):
        """判断是否为精力记录"""
        return ('【健康】精力' in line or '#精力/' in line) and line.strip().startswith('-')

    def _parse_energy_record(self, line, date_str, filename):
        """解析精力记录"""
        record = {
            'date': date_str,
            'filename': filename,
            'raw_text': line.strip(),
            'time': '',
            'type': '其他',
            'content': '',
            'factors': [],
            'effect_score': None
        }
        
        # 提取时间
        time_match = re.search(r'\(start::(\d+:\d+)\)', line)
        if time_match:
            record['time'] = time_match.group(1)
        
        # 提取类型
        type_match = re.search(r'精力/(\w+)', line)
        if type_match:
            record['type'] = type_match.group(1)
        
        # 提取效果评分
        effect_match = re.search(r'效果:(\d)', line)
        if effect_match:
            record['effect_score'] = int(effect_match.group(1))
        
        # 提取内容（去除格式标记）
        content = line
        content = re.sub(r'\(start::\d+:\d+\)', '', content)
        content = re.sub(r'【健康】精力/?[\w]*', '', content)
        content = re.sub(r'#精力/[\w]*', '', content)
        content = re.sub(r'效果:\d', '', content)
        content = re.sub(r'^-\s*：?\s*', '', content)
        record['content'] = content.strip()
        
        # 识别影响因素
        line_lower = line.lower()
        for factor, keywords in self.factor_keywords.items():
            if any(keyword.lower() in line_lower for keyword in keywords):
                record['factors'].append(factor)
        
        return record

    def _update_monthly_stats(self, record, month_key):
        """更新月度统计"""
        stats = self.monthly_stats[month_key]
        stats['total_records'] += 1
        stats['types'][record['type']] += 1
        stats['dates'].append(record['date'])
        
        for factor in record['factors']:
            stats['factors'][factor] += 1
            
        if record['effect_score'] is not None:
            stats['effects'].append(record['effect_score'])

    def generate_monthly_report(self):
        """生成月度报告数据"""
        report_data = {
            'months': [],
            'monthly_details': {},
            'overall_stats': {
                'total_records': len(self.energy_records),
                'date_range': '',
                'most_common_factors': [],
                'most_common_types': []
            }
        }
        
        # 按月份排序
        sorted_months = sorted(self.monthly_stats.keys())
        report_data['months'] = sorted_months
        
        if sorted_months:
            report_data['overall_stats']['date_range'] = f"{sorted_months[0]} 至 {sorted_months[-1]}"
        
        # 处理每月数据
        all_factors = Counter()
        all_types = Counter()
        
        for month in sorted_months:
            stats = self.monthly_stats[month]
            
            # 计算平均效果评分
            avg_effect = 0
            if stats['effects']:
                avg_effect = round(sum(stats['effects']) / len(stats['effects']), 1)
            
            month_data = {
                'total_records': stats['total_records'],
                'factors': dict(stats['factors'].most_common()),
                'types': dict(stats['types'].most_common()),
                'avg_effect': avg_effect,
                'unique_days': len(set(stats['dates']))
            }
            
            report_data['monthly_details'][month] = month_data
            
            # 累计统计
            all_factors.update(stats['factors'])
            all_types.update(stats['types'])
        
        # 总体统计
        report_data['overall_stats']['most_common_factors'] = dict(all_factors.most_common(10))
        report_data['overall_stats']['most_common_types'] = dict(all_types.most_common())
        
        return report_data

    def save_report_data(self, output_path):
        """保存报告数据为JSON"""
        report_data = self.generate_monthly_report()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2)
        
        print(f"报告数据已保存到: {output_path}")
        return report_data

def main():
    # 设置路径
    base_path = Path(__file__).parent.parent.parent
    daily_notes_path = base_path / "obsidian-vault" / "0_Bullet Journal" / "Daily Notes"
    output_path = base_path / "cursor_projects" / "Ob" / "energy_report_data.json"
    
    print(f"分析路径: {daily_notes_path}")
    print(f"输出路径: {output_path}")
    
    # 创建分析器并运行
    analyzer = EnergyAnalyzer(daily_notes_path)
    analyzer.extract_energy_records()
    
    if analyzer.energy_records:
        report_data = analyzer.save_report_data(output_path)
        print("\n=== 分析完成 ===")
        print(f"总记录数: {report_data['overall_stats']['total_records']}")
        print(f"时间范围: {report_data['overall_stats']['date_range']}")
        print(f"覆盖月份: {len(report_data['months'])}")
    else:
        print("未找到精力记录数据")

if __name__ == "__main__":
    main()
