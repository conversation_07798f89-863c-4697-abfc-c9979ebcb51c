# Augment Agent 快速参考卡片

## 🚀 快速开始

### 任务分类判断
```
复杂任务（创建issues）: 步骤 > 3 || 多技术栈 || 多验证点
简单任务（直接执行）: 步骤 ≤ 3 && 单一目标 && 标准操作
```

### 命名规范
```
格式：核心功能-YYYYMMDD
示例：MCP配置-20241201
```

## 📋 三阶段流程

### 阶段1：计划制定
1. 评估复杂度 → 决定是否创建issues
2. 生成任务名 → 使用标准格式
3. 创建计划文档 → 使用模板
4. 用户确认 → interactive-feedback

### 阶段2：执行反馈
1. 按计划执行 → 严格按步骤
2. 关键节点反馈 → 4个时机
3. 记录执行信息 → 问题和解决方案
4. 更新状态 → 实时跟踪

### 阶段3：复盘总结
1. 创建复盘文档 → 使用模板
2. 记录问题解决 → 详细过程
3. 总结经验教训 → 可复用知识
4. 提出优化建议 → 持续改进

## 🛠️ MCP服务速查

| 服务 | 用途 | 场景 |
|------|------|------|
| `interactive_feedback` | 用户交互 | 确认、咨询 |
| `Context7` | 文档查询 | API参考 |
| `sequential-thinking` | 任务分解 | 复杂分析 |
| `Playwright` | 浏览器操作 | 测试、抓取 |
| `together-image-gen` | 图像生成 | 免费创作 |

## 📝 反馈时机

### 必须反馈的4个时机
- ✅ 计划制定完成后
- ✅ 主要步骤完成后
- ✅ 遇到问题需确认时
- ✅ 任务完成时

## 📁 文件结构

```
./issues/任务名.md          # 任务计划文档
./rewind/任务名.md          # 任务复盘文档
./Templates/               # 标准模板
├── Issues任务计划模板.md
└── Rewind任务复盘模板.md
```

## 🎯 检查清单

### 开始前 ✅
- [ ] 分析复杂度
- [ ] 确定文档需求
- [ ] 生成任务名
- [ ] 创建计划文档

### 执行中 ✅
- [ ] 按计划执行
- [ ] 关键节点反馈
- [ ] 记录执行信息
- [ ] 更新状态

### 完成后 ✅
- [ ] 创建复盘文档
- [ ] 总结经验
- [ ] 提出建议
- [ ] 确认完成

## 🔧 常见问题

### Q: 如何判断任务复杂度？
A: 看步骤数(>3)、技术栈(多个)、验证点(多个)

### Q: 什么时候用interactive-feedback？
A: 计划确认、步骤完成、遇到问题、任务完成

### Q: 复盘文档何时创建？
A: 任务完成后立即创建，趁记忆清晰

## 📊 模板快速填写

### Issues模板要点
```markdown
# 任务名称
## 📋 任务背景 - 为什么做
## 🎯 任务目标 - 要达到什么
## 📊 任务分析 - 复杂度、依赖
## 📋 详细计划 - 具体步骤
## 🔄 执行状态 - 进度跟踪
## ⚠️ 风险预警 - 可能问题
```

### Rewind模板要点
```markdown
# 任务名称 复盘
## 📋 任务目标 - 重述目标
## 🔍 问题解决 - 详细过程
## ✅ 最终方案 - 采用方案
## 📊 方案对比 - 多方案分析
## 🎯 成功因素 - 关键要素
## 📝 经验总结 - 可复用知识
## 🔮 优化建议 - 改进方向
```

## 🎨 文档格式

### Emoji使用
- 📋 任务 🎯 目标 📊 分析 🔄 流程
- ✅ 完成 ⚠️ 警告 🛠️ 工具 📝 记录

### Markdown技巧
```markdown
**重要信息** *强调内容* `代码片段`
> 重要提示
- [ ] 待办 - [x] 完成
```

## 🚀 效率技巧

### 1. 模板化
- 常见任务建立专用模板
- 定期更新优化模板

### 2. 知识库
- 复盘文档整理成知识库
- 建立问题解决方案索引

### 3. 工具链
- 熟练掌握MCP服务
- 探索工具组合使用

---

*快速参考 v1.0 | 2024-12-01*
