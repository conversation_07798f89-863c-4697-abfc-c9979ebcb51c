<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Obsidian搜索系统 - 基础版</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 100%;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 40px;
            text-align: center;
            position: relative;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            position: relative;
            z-index: 1;
        }
        
        .header .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            position: relative;
            z-index: 1;
        }
        
        .content {
            padding: 40px;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }
        
        .feature {
            text-align: center;
            padding: 20px;
            border-radius: 15px;
            background: linear-gradient(145deg, #f8f9fa, #e9ecef);
            box-shadow: 0 8px 25px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .feature:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
            display: block;
        }
        
        .feature h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.3em;
        }
        
        .feature p {
            color: #6c757d;
            line-height: 1.6;
        }
        
        .highlight-section {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            text-align: center;
        }
        
        .highlight-section h2 {
            color: #d63384;
            font-size: 1.8em;
            margin-bottom: 15px;
        }
        
        .highlight-section p {
            color: #6f42c1;
            font-size: 1.1em;
            line-height: 1.6;
        }
        
        .users-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .user-type {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border-left: 4px solid #007bff;
        }
        
        .user-type h4 {
            color: #495057;
            margin-bottom: 8px;
        }
        
        .user-type p {
            color: #6c757d;
            font-size: 0.9em;
        }
        
        .cta-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin-top: 30px;
        }
        
        .cta-section h2 {
            font-size: 1.8em;
            margin-bottom: 15px;
        }
        
        .steps {
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        
        .step {
            background: rgba(255,255,255,0.1);
            border-radius: 10px;
            padding: 15px;
            margin: 5px;
            min-width: 150px;
            backdrop-filter: blur(10px);
        }
        
        .step-number {
            background: white;
            color: #667eea;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 auto 10px;
        }
        
        .badge {
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            display: inline-block;
            margin: 10px 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 Obsidian搜索系统</h1>
            <p class="subtitle">简单高效 · 即插即用 · 告别崩溃</p>
        </div>
        
        <div class="content">
            <div class="features">
                <div class="feature">
                    <span class="feature-icon">🎯</span>
                    <h3>智能搜索</h3>
                    <p>文件名、内容、全局搜索，三重覆盖无死角，快速定位目标信息</p>
                </div>
                
                <div class="feature">
                    <span class="feature-icon">⚙️</span>
                    <h3>灵活模式</h3>
                    <p>OR/AND/精确匹配，满足不同搜索需求，精准控制搜索范围</p>
                </div>
                
                <div class="feature">
                    <span class="feature-icon">🛡️</span>
                    <h3>安全稳定</h3>
                    <p>已解决崩溃问题，文件链接安全可靠，新标签页打开不影响工作</p>
                </div>
            </div>
            
            <div class="highlight-section">
                <h2>🌟 核心优势</h2>
                <p>通用兼容任何vault · 响应迅速秒级呈现 · 界面友好操作简单 · 轻量级无需插件</p>
                <div>
                    <span class="badge">✅ 零配置</span>
                    <span class="badge">✅ 即插即用</span>
                    <span class="badge">✅ 告别闪退</span>
                </div>
            </div>
            
            <h2 style="text-align: center; color: #2c3e50; margin-bottom: 20px;">👥 适用用户</h2>
            <div class="users-section">
                <div class="user-type">
                    <h4>📚 学术研究者</h4>
                    <p>快速检索论文笔记关键概念</p>
                </div>
                <div class="user-type">
                    <h4>💻 知识工作者</h4>
                    <p>项目文档快速定位相关内容</p>
                </div>
                <div class="user-type">
                    <h4>✍️ 写作爱好者</h4>
                    <p>创作笔记中搜索灵感片段</p>
                </div>
                <div class="user-type">
                    <h4>🎓 学生群体</h4>
                    <p>课程笔记快速复习重点</p>
                </div>
            </div>
            
            <div class="cta-section">
                <h2>📖 一分钟上手指南</h2>
                <div class="steps">
                    <div class="step">
                        <div class="step-number">1</div>
                        <p>复制代码到.md文件</p>
                    </div>
                    <div class="step">
                        <div class="step-number">2</div>
                        <p>输入关键词搜索</p>
                    </div>
                    <div class="step">
                        <div class="step-number">3</div>
                        <p>点击结果打开文件</p>
                    </div>
                    <div class="step">
                        <div class="step-number">4</div>
                        <p>享受高效搜索体验</p>
                    </div>
                </div>
                <p style="margin-top: 20px; font-size: 1.2em;">💡 <strong>一分钟上手，终身受益！</strong></p>
            </div>
        </div>
    </div>
</body>
</html>
