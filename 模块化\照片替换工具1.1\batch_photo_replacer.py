#!/usr/bin/env python3
"""
批量照片替换工具 v1.2.0

可以处理多个客户文件夹的通用工具。
只需要提供包含多个客户文件夹的父文件夹路径，脚本会：
1. 自动识别所有客户文件夹
2. 在每个客户文件夹中，找到修好的照片文件夹和需要替换照片的文件夹
3. 智能比较照片文件，只替换真正不同的照片
4. 生成综合报告并保存到logs文件夹

v1.1 更新内容：
- 扩展入盘文件夹识别范围到1-6号（支持"-(1) 入盘"到"-(6) 入盘"）
- 增强日志记录功能，详细记录所有跳过的文件夹信息
- 优化处理逻辑和用户反馈，提供更详细的跳过原因说明

v1.1.1 修复内容：
- 新增智能文件比较功能，使用MD5哈希值比较文件内容
- 只替换真正不同的照片文件，跳过相同的文件
- 改进日志记录，区分"替换"和"跳过相同文件"的操作

v1.2.0 用户体验改进：
- 增强控制台实时反馈，明确标注"该客户无需处理"
- 优化最终摘要显示，按处理结果分类显示客户
- 改进日志记录，为完全跳过的客户添加特殊标记
- 快速识别需要关注的客户和可以忽略的客户
"""

import os
import shutil
import argparse
import datetime
import logging
import sys
import re
import hashlib

# 全局变量用于跟踪客户处理信息
skipped_clients_info = []  # 跳过的客户（无有效文件夹结构）
processed_clients_info = []  # 成功处理的客户详细信息

def setup_logging(log_file=None):
    """设置日志配置"""
    # 创建logs文件夹（如果不存在）
    log_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 修改日志文件路径到logs文件夹
    if log_file:
        log_file = os.path.join(log_dir, os.path.basename(log_file))
    
    log_format = "%(asctime)s - %(levelname)s - %(message)s"
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(log_file) if log_file else logging.NullHandler()
        ]
    )
    return logging.getLogger()

def find_folders(main_folder, logger):
    """
    自动识别源文件夹和目标文件夹

    源文件夹: 以"-(1) 入盘"到"-(6) 入盘"开头的文件夹
    目标文件夹: 以"-("开头但不是源文件夹的其他文件夹
    """
    source_folder = None
    target_folders = []

    # 源文件夹的正则表达式模式，匹配以"-(1) 入盘"到"-(6) 入盘"开头的文件夹
    source_pattern = re.compile(r"^-\([123456]\) 入盘")
    
    try:
        # 列出主文件夹中的所有子文件夹
        all_items = os.listdir(main_folder)
        
        # 首先寻找源文件夹
        for item in all_items:
            item_path = os.path.join(main_folder, item)
            
            # 只处理文件夹
            if os.path.isdir(item_path):
                # 检查是否匹配源文件夹模式
                if source_pattern.match(item):
                    source_folder = item_path
                    logger.info(f"找到修好照片的文件夹: {item}")
                    break  # 找到第一个匹配的就跳出循环
        
        # 然后寻找目标文件夹（不包括已识别的源文件夹）
        for item in all_items:
            item_path = os.path.join(main_folder, item)
            
            # 只处理文件夹，且该文件夹不是源文件夹
            if os.path.isdir(item_path) and item_path != source_folder:
                # 识别目标文件夹 (以"-("开头的其他文件夹)
                if item.startswith("-("):
                    target_folders.append(item_path)
                    logger.info(f"找到需要替换照片的文件夹: {item}")
    
        if not source_folder:
            logger.error(f"在 {main_folder} 中未找到以'-(1) 入盘'到'-(6) 入盘'开头的源文件夹")
            return None, []
            
        if not target_folders:
            logger.error(f"在 {main_folder} 中未找到以'-('开头的目标文件夹")
            return source_folder, []
            
        return source_folder, target_folders
        
    except Exception as e:
        logger.error(f"查找文件夹时出错: {str(e)}")
        return None, []

def get_source_photos(source_dir, logger):
    """获取源文件夹中的所有照片"""
    source_photos = {}
    photo_extensions = ['.jpg', '.jpeg', '.png', '.tiff', '.gif', '.bmp']
    
    try:
        for root, _, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()
                
                if file_ext in photo_extensions:
                    source_photos[file] = file_path
                    logger.debug(f"找到源照片: {file}")
        
        logger.info(f"在源文件夹中找到 {len(source_photos)} 张照片")
        return source_photos
    
    except Exception as e:
        logger.error(f"获取源照片时出错: {str(e)}")
        return {}

def get_file_hash(file_path):
    """计算文件的MD5哈希值"""
    hash_md5 = hashlib.md5()
    try:
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()
    except Exception:
        return None

def files_are_identical(file1_path, file2_path):
    """检查两个文件是否完全相同"""
    # 首先检查文件大小
    try:
        if os.path.getsize(file1_path) != os.path.getsize(file2_path):
            return False
    except OSError:
        return False

    # 如果大小相同，再比较哈希值
    hash1 = get_file_hash(file1_path)
    hash2 = get_file_hash(file2_path)

    if hash1 is None or hash2 is None:
        return False

    return hash1 == hash2

def replace_photos(source_photos, target_folders, logger):
    """智能替换目标文件夹中的照片，只替换不同的文件"""
    replaced_count = 0
    skipped_count = 0
    error_count = 0
    photo_extensions = ['.jpg', '.jpeg', '.png', '.tiff', '.gif', '.bmp']

    # 处理每个目标文件夹
    for folder in target_folders:
        logger.info(f"正在处理文件夹: {folder}")

        for root, _, files in os.walk(folder):
            for file in files:
                file_path = os.path.join(root, file)
                file_ext = os.path.splitext(file)[1].lower()

                # 检查这是否是照片文件，且在源文件夹中存在
                if file_ext in photo_extensions and file in source_photos:
                    source_file_path = source_photos[file]

                    try:
                        # 检查文件是否相同
                        if files_are_identical(source_file_path, file_path):
                            logger.info(f"跳过相同文件: {file_path}")
                            skipped_count += 1
                        else:
                            # 只有当文件不同时才替换
                            shutil.copy2(source_file_path, file_path)
                            logger.info(f"已替换不同文件: {file_path}")
                            replaced_count += 1

                    except Exception as e:
                        logger.error(f"处理 {file_path} 时出错: {str(e)}")
                        error_count += 1

    return replaced_count, skipped_count, error_count

def process_client_folder(client_folder, logger):
    """处理单个客户文件夹"""
    global skipped_clients_info, processed_clients_info

    logger.info(f"开始处理客户文件夹: {client_folder}")
    client_name = os.path.basename(client_folder)

    # 查找源文件夹和目标文件夹
    source_folder, target_folders = find_folders(client_folder, logger)

    if not source_folder:
        skip_reason = "未找到符合条件的修好照片文件夹（需要以'-(1) 入盘'到'-(6) 入盘'开头）"
        logger.warning(f"在 {client_folder} 中{skip_reason}，跳过此客户")
        skipped_clients_info.append({
            'name': client_name,
            'reason': skip_reason,
            'path': client_folder
        })
        return 0, 0, 0, False, skip_reason

    if not target_folders:
        skip_reason = "未找到需要替换照片的文件夹（需要以'-('开头的其他文件夹）"
        logger.warning(f"在 {client_folder} 中{skip_reason}，跳过此客户")
        skipped_clients_info.append({
            'name': client_name,
            'reason': skip_reason,
            'path': client_folder
        })
        return 0, 0, 0, False, skip_reason

    # 获取源照片
    source_photos = get_source_photos(source_folder, logger)

    if not source_photos:
        skip_reason = f"在修好照片文件夹 {os.path.basename(source_folder)} 中未找到任何照片文件"
        logger.warning(f"在 {source_folder} 中未找到任何照片，跳过此客户")
        skipped_clients_info.append({
            'name': client_name,
            'reason': skip_reason,
            'path': client_folder
        })
        return 0, 0, 0, False, skip_reason

    # 替换照片
    replaced, skipped, errors = replace_photos(source_photos, target_folders, logger)

    # 记录客户处理信息
    client_info = {
        'name': client_name,
        'path': client_folder,
        'replaced': replaced,
        'skipped': skipped,
        'errors': errors,
        'total_photos': replaced + skipped,
        'needs_attention': replaced > 0  # 是否需要关注（有照片被替换）
    }
    processed_clients_info.append(client_info)

    # 根据处理结果添加特殊日志标记
    if replaced == 0 and skipped > 0:
        logger.info(f"[完全跳过] 客户 {client_name} - 所有 {skipped} 张照片都未修改")
    else:
        logger.info(f"客户 {client_name} 处理完成")
        logger.info(f"已替换照片: {replaced}")
        logger.info(f"已跳过照片: {skipped}")
        logger.info(f"错误数量: {errors}")

    return replaced, skipped, errors, True, None

def log_comprehensive_summary(logger):
    """在日志文件末尾记录完整的处理摘要"""
    global skipped_clients_info, processed_clients_info

    logger.info("=" * 80)
    logger.info("批量照片替换完整处理摘要")
    logger.info("=" * 80)

    # 1. 记录跳过的客户信息（无有效文件夹结构）
    if skipped_clients_info:
        logger.info("\n【跳过的客户文件夹】（无有效文件夹结构）")
        logger.info("-" * 50)

        # 按跳过原因分组统计
        reason_groups = {}
        for client_info in skipped_clients_info:
            reason = client_info['reason']
            if reason not in reason_groups:
                reason_groups[reason] = []
            reason_groups[reason].append(client_info)

        # 记录每种跳过原因的详细信息
        for reason, clients in reason_groups.items():
            logger.info(f"\n跳过原因: {reason}")
            logger.info(f"客户数量: {len(clients)}")
            logger.info("客户列表:")
            for client_info in clients:
                logger.info(f"  - 客户名称: {client_info['name']}")
                logger.info(f"    文件夹路径: {client_info['path']}")

    # 2. 记录处理的客户信息
    if processed_clients_info:
        logger.info("\n【处理的客户详细信息】")
        logger.info("-" * 50)

        # 分类统计
        clients_with_changes = [c for c in processed_clients_info if c['needs_attention']]
        clients_no_changes = [c for c in processed_clients_info if not c['needs_attention']]

        # 有照片被替换的客户（需要关注）
        if clients_with_changes:
            logger.info(f"\n[需要关注] 有照片被替换的客户: {len(clients_with_changes)} 个")
            for client in clients_with_changes:
                logger.info(f"  - {client['name']}: 替换 {client['replaced']} 张, 跳过 {client['skipped']} 张")

        # 完全跳过的客户（无需关注）
        if clients_no_changes:
            logger.info(f"\n[无需关注] 完全跳过的客户（所有照片未修改）: {len(clients_no_changes)} 个")
            for client in clients_no_changes:
                logger.info(f"  - {client['name']}: 跳过 {client['skipped']} 张照片（全部相同）")

    logger.info("=" * 80)

def main():
    # 解析命令行参数
    parser = argparse.ArgumentParser(
        description="批量处理多个客户文件夹的照片替换工具。"
    )
    parser.add_argument(
        "--parent", "-p", 
        required=True, 
        help="包含多个客户文件夹的父文件夹路径"
    )
    args = parser.parse_args()
    
    # 设置日志
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    log_file = f"batch_photo_replacer_{today}.log"
    logger = setup_logging(log_file)
    
    logger.info("批量照片替换工具启动")
    logger.info(f"父文件夹: {args.parent}")
    
    # 检查父文件夹是否存在
    if not os.path.isdir(args.parent):
        logger.error(f"父文件夹不存在: {args.parent}")
        print(f"错误: 文件夹 '{args.parent}' 不存在。请检查路径是否正确。")
        return
    
    # 查找所有客户文件夹
    client_folders = []
    try:
        for item in os.listdir(args.parent):
            item_path = os.path.join(args.parent, item)
            if os.path.isdir(item_path):
                client_folders.append(item_path)
        
        if not client_folders:
            logger.error(f"在 {args.parent} 中未找到任何客户文件夹")
            print(f"错误: 在 '{args.parent}' 中未找到任何客户文件夹。")
            return
            
        logger.info(f"找到 {len(client_folders)} 个客户文件夹")
        print(f"找到 {len(client_folders)} 个客户文件夹，开始处理...")
    
    except Exception as e:
        logger.error(f"查找客户文件夹时出错: {str(e)}")
        print(f"错误: 查找客户文件夹时出错: {str(e)}")
        return
    
    # 处理每个客户文件夹
    total_replaced = 0
    total_skipped = 0
    total_errors = 0
    processed_clients = 0
    skipped_clients = 0

    for client_folder in client_folders:
        client_name = os.path.basename(client_folder)
        print(f"\n处理客户: {client_name}")

        replaced, skipped, errors, success, skip_reason = process_client_folder(client_folder, logger)

        if success:
            processed_clients += 1
            total_replaced += replaced
            total_skipped += skipped
            total_errors += errors

            # 根据处理结果提供不同的控制台反馈
            if replaced == 0 and skipped > 0:
                print(f"  ✓ 该客户无需处理 - 所有 {skipped} 张照片都未修改")
            else:
                print(f"  - 已替换照片: {replaced}")
                print(f"  - 已跳过照片: {skipped}")
                if errors > 0:
                    print(f"  - 错误数量: {errors}")
        else:
            skipped_clients += 1
            print(f"  - 已跳过该客户: {skip_reason}")
    
    # 记录完整的处理摘要到日志文件
    log_comprehensive_summary(logger)

    # 记录和显示总结果
    logger.info("批量照片替换完成")
    logger.info(f"处理的客户数: {processed_clients}")
    logger.info(f"跳过的客户数: {skipped_clients}")
    logger.info(f"总替换照片: {total_replaced}")
    logger.info(f"总跳过照片: {total_skipped}")
    logger.info(f"总错误数量: {total_errors}")

    # 打印增强的总摘要
    print("\n" + "="*60)
    print("总处理摘要:")
    print("="*60)
    print(f"- 成功处理的客户: {processed_clients}")
    print(f"- 跳过的客户（无有效结构）: {skipped_clients}")
    print(f"- 总替换照片: {total_replaced}")
    print(f"- 总跳过照片: {total_skipped}")
    print(f"- 总错误数量: {total_errors}")

    # 按处理结果分类显示客户
    if processed_clients_info:
        # 分类统计
        clients_with_changes = [c for c in processed_clients_info if c['needs_attention']]
        clients_no_changes = [c for c in processed_clients_info if not c['needs_attention']]

        print(f"\n📋 客户处理结果分类:")
        print("-" * 40)

        # 需要关注的客户（有照片被替换）
        if clients_with_changes:
            print(f"\n★ 需要关注的客户（有照片被替换）: {len(clients_with_changes)} 个")
            for client in clients_with_changes:
                print(f"  • {client['name']}: 替换 {client['replaced']} 张, 跳过 {client['skipped']} 张")

        # 无需关注的客户（所有照片都相同）
        if clients_no_changes:
            print(f"\n✓ 无需关注的客户（所有照片未修改）: {len(clients_no_changes)} 个")
            # 只显示前10个，如果太多的话
            display_clients = clients_no_changes[:10]
            for client in display_clients:
                print(f"  • {client['name']}: 跳过 {client['skipped']} 张照片（全部相同）")

            if len(clients_no_changes) > 10:
                print(f"  ... 还有 {len(clients_no_changes) - 10} 个客户（详见日志文件）")

    # 显示跳过客户的详细信息（无有效文件夹结构）
    if skipped_clients_info:
        print(f"\n❌ 跳过的客户详细信息（无有效文件夹结构）:")
        print("-" * 40)

        # 按跳过原因分组显示
        reason_groups = {}
        for client_info in skipped_clients_info:
            reason = client_info['reason']
            if reason not in reason_groups:
                reason_groups[reason] = []
            reason_groups[reason].append(client_info['name'])

        for reason, client_names in reason_groups.items():
            print(f"\n原因: {reason}")
            print(f"客户数量: {len(client_names)}")
            print(f"客户列表: {', '.join(client_names)}")

    log_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "logs", log_file)
    if total_errors > 0:
        print(f"\n警告: 总共发生了 {total_errors} 个错误。请查看日志文件了解详细信息: {log_path}")
    else:
        print(f"\n日志文件保存在: {log_path}")

    print("="*50)

if __name__ == "__main__":
    try:
        main()
        print("\n操作完成。按Enter键退出...")
        input()
    except Exception as e:
        print(f"发生错误: {str(e)}")
        print("\n按Enter键退出...")
        input() 