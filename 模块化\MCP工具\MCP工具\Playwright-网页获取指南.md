# 使用Playwright MCP获取网页内容

## 概述

由于fetch MCP在Augment中存在兼容性问题，您可以使用已配置的Playwright MCP作为替代方案。

## 使用方法

### 基础网页获取
```
请使用playwright访问 https://example.com 并获取页面内容
```

### 获取特定元素
```
请使用playwright访问 https://news.example.com 并获取所有文章标题
```

### 截图 + 内容获取
```
请使用playwright访问 https://example.com，截图并获取页面文本内容
```

## 优势

1. **JavaScript支持**：可以处理动态加载的内容
2. **完整渲染**：获取完全渲染后的页面
3. **多功能**：支持截图、点击、表单填写等
4. **已配置**：您的Playwright MCP已经可用

## 示例命令

### 获取新闻网站内容
```
请使用playwright打开 https://news.ycombinator.com 并获取首页所有新闻标题和链接
```

### 获取技术文档
```
请使用playwright访问 https://docs.python.org 并获取主要导航菜单内容
```

### 获取搜索结果
```
请使用playwright访问 https://www.google.com，搜索"Python教程"，并获取前5个搜索结果
```

## 与fetch MCP的对比

| 功能 | Playwright MCP | fetch MCP |
|------|----------------|-----------|
| 静态内容获取 | ✅ | ✅ |
| JavaScript渲染 | ✅ | ❌ |
| 交互操作 | ✅ | ❌ |
| 截图功能 | ✅ | ❌ |
| Augment兼容性 | ✅ | ❌ |

## 注意事项

1. **性能**：Playwright比简单的fetch稍慢，因为需要启动浏览器
2. **资源消耗**：消耗更多内存和CPU
3. **功能丰富**：提供了远超fetch的功能

## 推荐使用场景

- 需要JavaScript渲染的现代网站
- 需要与页面交互的场景
- 需要截图记录的情况
- 作为fetch MCP的完美替代

---

**总结**：Playwright MCP不仅可以替代fetch MCP，还提供了更强大的功能。
