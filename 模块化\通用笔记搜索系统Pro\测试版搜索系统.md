---
tags:
  - type/dashboard
  - test/search-system
created: 2025-07-11T11:30
updated: 2025-07-11T11:30
---

# 🧪 测试版搜索系统

## 基础测试

```dataviewjs
// 测试版搜索系统 - 最简化版本
const container = this.container;

// 创建测试界面
const testDiv = document.createElement('div');
testDiv.style.cssText = 'padding: 20px; border: 1px solid #ccc; border-radius: 8px;';

testDiv.innerHTML = `
    <h3>🧪 搜索系统测试</h3>
    <div style="margin: 15px 0;">
        <input type="text" id="test-input" placeholder="输入关键词测试..." style="padding: 8px; width: 300px; margin-right: 10px;">
        <button id="test-btn" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">测试搜索</button>
    </div>
    <div id="test-results" style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 4px; min-height: 100px;">
        准备测试...
    </div>
`;

container.appendChild(testDiv);

// 测试函数
async function testSearch() {
    const input = document.getElementById('test-input');
    const results = document.getElementById('test-results');
    
    const keyword = input.value.trim();
    
    if (!keyword) {
        results.innerHTML = '请输入关键词';
        return;
    }
    
    results.innerHTML = '正在测试...';
    
    try {
        // 测试1：检查 dv 对象
        console.log('测试1 - dv对象:', typeof dv);
        results.innerHTML += '<br>✅ dv对象存在: ' + (typeof dv);
        
        // 测试2：获取所有页面
        const allPages = dv.pages();
        console.log('测试2 - 所有页面数量:', allPages.length);
        results.innerHTML += '<br>✅ 总页面数: ' + allPages.length;
        
        // 测试3：查找包含notes的页面
        const notesPages = allPages.filter(p => p.file.path.includes('notes'));
        console.log('测试3 - notes页面数量:', notesPages.length);
        results.innerHTML += '<br>✅ notes页面数: ' + notesPages.length;
        
        // 测试4：列出notes页面
        if (notesPages.length > 0) {
            results.innerHTML += '<br><br>📁 Notes文件列表:';
            notesPages.forEach((page, index) => {
                results.innerHTML += `<br>${index + 1}. ${page.file.name}`;
            });
        }
        
        // 测试5：搜索关键词
        let matchCount = 0;
        results.innerHTML += '<br><br>🔍 搜索结果:';
        
        for (const page of notesPages) {
            try {
                const content = await dv.io.load(page.file.path);
                const fileName = page.file.name.replace('.md', '');
                
                // 检查文件名
                if (fileName.toLowerCase().includes(keyword.toLowerCase())) {
                    matchCount++;
                    results.innerHTML += `<br>📄 ${fileName} (文件名匹配)`;
                }
                // 检查内容
                else if (content.toLowerCase().includes(keyword.toLowerCase())) {
                    matchCount++;
                    results.innerHTML += `<br>📄 ${fileName} (内容匹配)`;
                }
            } catch (error) {
                console.error('读取文件失败:', page.file.path, error);
            }
        }
        
        results.innerHTML += `<br><br>🎯 总共找到 ${matchCount} 个匹配项`;
        
    } catch (error) {
        console.error('测试失败:', error);
        results.innerHTML = '❌ 测试失败: ' + error.message;
    }
}

// 绑定事件
setTimeout(() => {
    const testBtn = document.getElementById('test-btn');
    const testInput = document.getElementById('test-input');
    
    if (testBtn) {
        testBtn.onclick = testSearch;
        console.log('✅ 测试按钮事件已绑定');
    }
    
    if (testInput) {
        testInput.onkeypress = function(e) {
            if (e.key === 'Enter') {
                testSearch();
            }
        };
        console.log('✅ 回车键事件已绑定');
    }
    
    // 自动测试基础功能
    setTimeout(() => {
        const results = document.getElementById('test-results');
        if (results) {
            results.innerHTML = '🔧 自动检测中...<br>';
            
            // 检查基础环境
            results.innerHTML += '✅ JavaScript运行正常<br>';
            results.innerHTML += '✅ DOM操作正常<br>';
            
            if (typeof dv !== 'undefined') {
                results.innerHTML += '✅ Dataview插件已加载<br>';
                
                try {
                    const pageCount = dv.pages().length;
                    results.innerHTML += `✅ 可访问 ${pageCount} 个页面<br>`;
                    
                    const notesCount = dv.pages().filter(p => p.file.path.includes('notes')).length;
                    results.innerHTML += `✅ 找到 ${notesCount} 个notes文件<br>`;
                    
                    results.innerHTML += '<br>💡 环境检测完成，可以开始测试搜索功能';
                } catch (error) {
                    results.innerHTML += '❌ Dataview查询失败: ' + error.message;
                }
            } else {
                results.innerHTML += '❌ Dataview插件未加载';
            }
        }
    }, 500);
    
}, 100);
```

## 📋 测试说明

### 🎯 测试目的
这个测试版本用于：
1. 验证 Dataview 插件是否正常工作
2. 检查文件查询和读取功能
3. 测试基础搜索逻辑
4. 诊断具体的错误原因

### 🔍 使用方法
1. 打开这个文件，等待自动检测完成
2. 在输入框中输入一个简单的关键词（如 "MCP"）
3. 点击"测试搜索"按钮或按回车键
4. 查看详细的测试结果和调试信息

### 📊 测试项目
- ✅ JavaScript 环境检查
- ✅ Dataview 插件状态
- ✅ 文件列表获取
- ✅ 文件内容读取
- ✅ 关键词匹配测试

### 🐛 故障排除
如果测试失败：
1. 确认 Dataview 插件已启用
2. 检查浏览器控制台的错误信息
3. 确认 notes 目录下有 Markdown 文件
4. 尝试刷新页面重新测试

---

*💡 这是一个诊断工具，帮助找出搜索系统的具体问题所在。*
