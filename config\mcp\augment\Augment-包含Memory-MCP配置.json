{"mcpServers": {"memory": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_FILE_PATH": "C:\\Users\\<USER>\\Desktop\\测试库\\memory.json"}}, "mcp-obsidian": {"command": "uvx", "args": ["mcp-obsidian"], "env": {"OBSIDIAN_API_KEY": "a712c31903d2c76f85bbf8acb44c4b5c6a497fbcd206557a9102175eebcef6b6", "OBSIDIAN_HOST": "https://127.0.0.1:27124/"}}, "mcp-feedback-enhanced": {"command": "uvx", "args": ["mcp-feedback-enhanced"]}, "context7": {"command": "npx", "args": ["context7"]}, "sequential-thinking": {"command": "npx", "args": ["sequential-thinking"]}, "playwright": {"command": "npx", "args": ["@executeautomation/playwright-mcp-server"], "env": {"PLAYWRIGHT_API_KEY": "07885b1c-a040-453f-8685-c0d6ee0336e8"}}, "shrimp-task-manager": {"command": "npx", "args": ["shrimp-task-manager"]}, "replicate-flux-mcp": {"command": "uvx", "args": ["replicate-flux-mcp"], "env": {"REPLICATE_API_TOKEN": "****************************************"}}, "together-image-gen": {"command": "uvx", "args": ["together-image-gen"], "env": {"TOGETHER_API_KEY": "aa188707-1019-4641-a6ea-ff43fa1daa9b"}}, "fetch": {"command": "uvx", "args": ["mcp-server-fetch"]}}}