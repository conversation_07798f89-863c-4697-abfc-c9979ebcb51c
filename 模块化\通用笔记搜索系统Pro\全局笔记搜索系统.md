---
tags:
  - type/dashboard
  - dashboard/global-search
  - obsidian/search
created: 2025-07-11T12:30
updated: 2025-07-11T12:30
---

# 🌐 全局笔记搜索系统

```dataviewjs
// 全局笔记搜索系统 - 搜索整个 vault
const container = this.container;

// 创建主容器
const mainDiv = document.createElement('div');
mainDiv.style.cssText = `
    font-family: var(--font-interface);
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
`;

// 搜索区域
const searchDiv = document.createElement('div');
searchDiv.style.cssText = `
    background: var(--background-secondary);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--background-modifier-border);
`;

// 标题
const title = document.createElement('h3');
title.textContent = '🌐 全局笔记搜索';
title.style.cssText = 'margin: 0 0 15px 0; color: var(--text-normal);';

// 输入区域
const inputDiv = document.createElement('div');
inputDiv.style.cssText = 'display: flex; gap: 10px; margin-bottom: 15px; flex-wrap: wrap;';

// 搜索输入框
const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.placeholder = '搜索整个 vault 中的笔记...';
searchInput.style.cssText = `
    flex: 1;
    min-width: 250px;
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
`;

// 模式选择
const modeSelect = document.createElement('select');
modeSelect.style.cssText = `
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
`;

const modes = [
    { value: 'OR', text: 'OR (任一)' },
    { value: 'AND', text: 'AND (所有)' },
    { value: 'EXACT', text: '精确匹配' }
];

modes.forEach(mode => {
    const option = document.createElement('option');
    option.value = mode.value;
    option.textContent = mode.text;
    modeSelect.appendChild(option);
});

// 目录过滤 - 自动检测目录结构
const dirSelect = document.createElement('select');
dirSelect.style.cssText = `
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
`;

// 自动检测目录结构
async function detectDirectories() {
    try {
        const allPages = dv.pages();
        const directories = new Set();

        allPages.forEach(page => {
            const pathParts = page.file.path.split('/');
            if (pathParts.length > 1) {
                const topDir = pathParts[0];
                // 排除系统目录
                if (!topDir.startsWith('.') && topDir !== 'Templates') {
                    directories.add(topDir);
                }
            }
        });

        return Array.from(directories).sort();
    } catch (error) {
        console.error('检测目录失败:', error);
        // 返回默认目录
        return ['notes', 'obsidian-vault', '模块化', 'docs', 'scripts'];
    }
}

// 初始化目录选择器
async function initDirectorySelect() {
    // 添加"全部目录"选项
    const allOption = document.createElement('option');
    allOption.value = 'all';
    allOption.textContent = '全部目录';
    dirSelect.appendChild(allOption);

    // 添加检测到的目录
    const detectedDirs = await detectDirectories();
    detectedDirs.forEach(dir => {
        const option = document.createElement('option');
        option.value = dir;
        option.textContent = dir;
        dirSelect.appendChild(option);
    });

    // 如果没有检测到目录，添加提示
    if (detectedDirs.length === 0) {
        const noOption = document.createElement('option');
        noOption.value = 'none';
        noOption.textContent = '未检测到子目录';
        noOption.disabled = true;
        dirSelect.appendChild(noOption);
    }
}

// 搜索范围选择
const scopeSelect = document.createElement('select');
scopeSelect.style.cssText = `
    padding: 8px 12px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
`;

const scopes = [
    { value: 'all', text: '全部内容' },
    { value: 'filename', text: '仅文件名' },
    { value: 'content', text: '仅文件内容' }
];

scopes.forEach(scope => {
    const option = document.createElement('option');
    option.value = scope.value;
    option.textContent = scope.text;
    scopeSelect.appendChild(option);
});

// 按钮区域
const buttonDiv = document.createElement('div');
buttonDiv.style.cssText = 'text-align: center;';

const searchBtn = document.createElement('button');
searchBtn.textContent = '🔍 搜索';
searchBtn.style.cssText = `
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
`;

const clearBtn = document.createElement('button');
clearBtn.textContent = '🗑️ 清空结果';
clearBtn.style.cssText = `
    background: var(--background-modifier-border);
    color: var(--text-normal);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
`;

const historyBtn = document.createElement('button');
historyBtn.textContent = '📚 搜索历史';
historyBtn.style.cssText = `
    background: var(--color-blue);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
`;

// 组装搜索区域
inputDiv.appendChild(searchInput);
inputDiv.appendChild(modeSelect);
inputDiv.appendChild(dirSelect);
inputDiv.appendChild(scopeSelect);
buttonDiv.appendChild(searchBtn);
buttonDiv.appendChild(clearBtn);
buttonDiv.appendChild(historyBtn);

searchDiv.appendChild(title);
searchDiv.appendChild(inputDiv);
searchDiv.appendChild(buttonDiv);

// 结果区域
const resultDiv = document.createElement('div');
resultDiv.style.cssText = `
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    padding: 20px;
    background: var(--background-primary);
    min-height: 200px;
`;

const resultTitle = document.createElement('h3');
resultTitle.textContent = '📋 搜索结果';
resultTitle.style.cssText = 'margin: 0 0 15px 0; color: var(--text-normal);';

const resultContent = document.createElement('div');
resultContent.innerHTML = `
    <div style="text-align: center; color: var(--text-muted); padding: 40px;">
        <div style="font-size: 48px; margin-bottom: 10px;">🌐</div>
        <div style="font-size: 18px;">准备搜索整个 vault</div>
    </div>
`;

resultDiv.appendChild(resultTitle);
resultDiv.appendChild(resultContent);

// 组装主界面
mainDiv.appendChild(searchDiv);
mainDiv.appendChild(resultDiv);
container.appendChild(mainDiv);

// 初始化目录选择器
initDirectorySelect();

// 搜索函数
async function performGlobalSearch() {
    const keyword = searchInput.value.trim();
    const mode = modeSelect.value;
    const dirFilter = dirSelect.value;
    const searchScope = scopeSelect.value;
    
    if (!keyword) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                <div>请输入搜索关键词</div>
            </div>
        `;
        return;
    }
    
    // 显示加载状态
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
            <div>正在搜索整个 vault...</div>
        </div>
    `;
    
    try {
        // 获取所有页面
        let allPages = dv.pages();
        
        // 根据目录过滤
        if (dirFilter !== 'all') {
            allPages = allPages.where(p => p.file.path.includes(dirFilter + '/'));
        }
        
        // 排除系统目录
        const excludeDirs = ['.obsidian'];
        allPages = allPages.where(p => 
            !excludeDirs.some(dir => p.file.path.includes(dir))
        );
        
        const keywords = keyword.split(/\s+/).filter(k => k.length > 0);
        const results = [];
        
        for (const page of allPages) {
            try {
                const content = await dv.io.load(page.file.path);
                const fileName = page.file.name.replace('.md', '');
                
                let matched = false;
                let matchType = '';
                let snippet = '';

                // 根据搜索范围进行匹配
                if (searchScope === 'all' || searchScope === 'filename') {
                    // 检查文件名匹配
                    if (isMatch(fileName, keywords, mode)) {
                        matched = true;
                        matchType = '文件名';
                        snippet = fileName;
                    }
                }

                if (!matched && (searchScope === 'all' || searchScope === 'content')) {
                    // 检查内容匹配
                    if (isMatch(content, keywords, mode)) {
                        matched = true;
                        matchType = '文件内容';
                        snippet = getSnippet(content, keywords);
                    }
                }
                
                if (matched) {
                    results.push({
                        name: fileName,
                        path: page.file.path,
                        link: page.file.link,
                        matchType: matchType,
                        snippet: snippet,
                        mtime: page.file.mtime
                    });
                }
            } catch (error) {
                // 跳过无法读取的文件
            }
        }
        
        // 添加到搜索历史
        addToHistory(keyword, mode, dirFilter, searchScope, results.length);

        displayGlobalResults(results, keywords, dirFilter, searchScope);
        
    } catch (error) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-error); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div>搜索失败，请重试</div>
            </div>
        `;
    }
}

// 匹配函数
function isMatch(text, keywords, mode) {
    if (!text || !keywords || keywords.length === 0) return false;
    
    const lowerText = text.toLowerCase();
    const lowerKeywords = keywords.map(k => k.toLowerCase());
    
    switch (mode) {
        case 'AND':
            return lowerKeywords.every(k => lowerText.includes(k));
        case 'OR':
            return lowerKeywords.some(k => lowerText.includes(k));
        case 'EXACT':
            return lowerText.includes(keywords.join(' ').toLowerCase());
        default:
            return lowerKeywords.some(k => lowerText.includes(k));
    }
}

// 提取片段
function getSnippet(content, keywords, maxLength = 150) {
    const lines = content.split('\n');
    const lowerKeywords = keywords.map(k => k.toLowerCase());
    
    for (const line of lines) {
        if (lowerKeywords.some(k => line.toLowerCase().includes(k))) {
            return line.length > maxLength ? line.substring(0, maxLength) + '...' : line;
        }
    }
    
    return content.substring(0, maxLength) + '...';
}

// 显示结果
function displayGlobalResults(results, keywords, dirFilter, searchScope) {
    if (results.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">未找到匹配的笔记</div>
                <div style="font-size: 14px;">尝试使用不同的关键词或目录</div>
            </div>
        `;
        return;
    }
    
    // 搜索范围描述
    let scopeDescription = '';
    switch (searchScope) {
        case 'filename':
            scopeDescription = '仅文件名';
            break;
        case 'content':
            scopeDescription = '仅文件内容';
            break;
        default:
            scopeDescription = '全部内容';
    }

    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: var(--background-secondary); border-radius: 6px; border-left: 4px solid var(--interactive-accent);">
            <strong>🎯 找到 ${results.length} 个匹配结果</strong><br>
            <span style="color: var(--text-muted); font-size: 14px;">
                关键词: ${keywords.join(', ')} |
                搜索范围: ${scopeDescription}
                ${dirFilter !== 'all' ? ` | 目录: ${dirFilter}` : ''}
            </span>
        </div>
    `;
    
    results.forEach(result => {
        const date = new Date(result.mtime).toLocaleDateString('zh-CN');
        const pathParts = result.path.split('/');
        const directory = pathParts.length > 1 ? pathParts[0] : '根目录';
        
        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 8px; margin-bottom: 15px; padding: 15px; background: var(--background-secondary);">
                <h4 style="margin: 0 0 8px 0; color: var(--text-normal);">
                    <a href="${result.link}" style="text-decoration: none; color: var(--link-color); font-weight: bold;">
                        📄 ${result.name}
                    </a>
                </h4>
                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 8px;">
                    <span style="margin-right: 15px;">📁 ${directory}</span>
                    <span style="margin-right: 15px;">📅 ${date}</span>
                    <span>🔍 匹配: ${result.matchType}</span>
                </div>
                <div style="background: var(--background-primary); padding: 10px; border-radius: 4px; border-left: 3px solid var(--color-green);">
                    <div style="font-size: 13px; color: var(--text-normal); line-height: 1.4;">
                        ${highlightText(result.snippet, keywords)}
                    </div>
                </div>
            </div>
        `;
    });
    
    resultContent.innerHTML = html;
}

// 高亮关键词
function highlightText(text, keywords) {
    let highlighted = text;
    keywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        highlighted = highlighted.replace(regex, '<mark style="background: var(--text-highlight-bg); color: var(--text-normal);">$1</mark>');
    });
    return highlighted;
}

// 搜索历史管理
let searchHistory = JSON.parse(localStorage.getItem('obsidian-search-history') || '[]');

function addToHistory(keyword, mode, dirFilter, searchScope, resultCount) {
    const historyItem = {
        keyword,
        mode,
        dirFilter,
        searchScope,
        resultCount,
        timestamp: new Date().toLocaleString('zh-CN')
    };

    // 避免重复
    searchHistory = searchHistory.filter(item =>
        !(item.keyword === keyword && item.mode === mode && item.dirFilter === dirFilter && item.searchScope === searchScope)
    );

    searchHistory.unshift(historyItem);
    searchHistory = searchHistory.slice(0, 20); // 保留最近20条
    localStorage.setItem('obsidian-search-history', JSON.stringify(searchHistory));
}

function showSearchHistory() {
    if (searchHistory.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📚</div>
                <div style="font-size: 18px;">暂无搜索历史</div>
            </div>
        `;
        return;
    }

    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: var(--background-secondary); border-radius: 6px; border-left: 4px solid var(--color-blue);">
            <strong>📚 搜索历史</strong>
            <span style="margin-left: 10px; color: var(--text-muted);">最近 ${searchHistory.length} 条记录</span>
        </div>
    `;

    searchHistory.forEach((item, index) => {
        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 8px; margin-bottom: 10px; padding: 12px; background: var(--background-secondary); cursor: pointer;"
                 onclick="restoreSearch('${item.keyword}', '${item.mode}', '${item.dirFilter}', '${item.searchScope}')">
                <div style="font-weight: bold; margin-bottom: 5px; color: var(--text-normal);">
                    🔍 "${item.keyword}"
                </div>
                <div style="font-size: 12px; color: var(--text-muted);">
                    模式: ${item.mode} | 范围: ${item.searchScope} | 目录: ${item.dirFilter} | 结果: ${item.resultCount}个 | ${item.timestamp}
                </div>
            </div>
        `;
    });

    resultContent.innerHTML = html;
}

function restoreSearch(keyword, mode, dirFilter, searchScope) {
    searchInput.value = keyword;
    modeSelect.value = mode;
    dirSelect.value = dirFilter;
    scopeSelect.value = searchScope;
    performGlobalSearch();
}

// 清空结果
function clearResults() {
    searchInput.value = '';
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 10px;">🌐</div>
            <div style="font-size: 18px;">准备搜索整个 vault</div>
        </div>
    `;
}

// 全局函数（供历史记录调用）
window.restoreSearch = restoreSearch;

// 事件绑定
setTimeout(() => {
    searchBtn.onclick = performGlobalSearch;
    clearBtn.onclick = clearResults;
    historyBtn.onclick = showSearchHistory;

    searchInput.onkeypress = function(e) {
        if (e.key === 'Enter') {
            performGlobalSearch();
        }
    };
}, 100);
```

## 📖 使用说明

### 🌐 全局搜索特点
- **搜索整个 vault**：不限制在特定目录
- **目录过滤**：可选择搜索特定目录
- **搜索范围选择**：可选择仅搜索文件名或文件内容
- **搜索历史**：自动保存搜索记录，支持快速重复搜索
- **路径显示**：显示文件所在目录
- **排除系统文件**：自动排除 .obsidian 等系统目录

### 🔍 使用方法
1. 输入关键词（如 "任务管理"）
2. 选择搜索模式、目录过滤和搜索范围
3. 点击搜索或按回车键
4. 查看结果，点击文件名跳转

### 📁 目录说明
- **全部目录**：搜索整个 vault
- **notes**：仅搜索 notes 目录
- **obsidian-vault**：仅搜索 obsidian-vault 目录
- **模块化**：仅搜索模块化目录
- **docs**：仅搜索文档目录
- **scripts**：仅搜索脚本目录

### 🎯 搜索范围说明
- **全部内容**：同时搜索文件名和文件内容
- **仅文件名**：只搜索文件标题，适合查找特定文件
- **仅文件内容**：只搜索文件内容，适合查找提及某个概念的文件

### 📚 搜索历史功能
- 自动保存最近20次搜索记录
- 点击"搜索历史"查看历史记录
- 点击历史记录可快速重复搜索
- 显示每次搜索的参数和结果数量

---

*💡 现在支持更精确的搜索范围控制和搜索历史功能！*
