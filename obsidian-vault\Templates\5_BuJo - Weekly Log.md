---
tags:
  - type/structure
  - structure/bujo/weekly
start_date: 
end_date: ""
template: "[[5_BuJo - Weekly Log]]"
created: 2024-12-29T03:48
updated: 2025-05-05T11:28
---
# 📅 一周回顾 WEEKLY REVIEW 

### 周精华
```dataviewjs
// 获取当前周记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 查询日记文件
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        // 提取日期部分
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            // 检查日期是否在当前周内
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 创建表格来显示精华内容
const rows = [];

for (const page of pages) {
    // 获取文件内容
    const content = await dv.io.load(page.file.path);
    
    // 查找包含 #精华 标签的行
    const lines = content.split("\n");
    for (let i = 0; i < lines.length; i++) {
        if (lines[i].includes("#精华")) {
            // 提取文件名部分
            const fileName = page.file.name.replace(".md", ""); // 移除.md扩展名
            
            // 提取内容（去除时间戳和标签）
            let highlightContent = lines[i];
            
            // 移除时间戳部分 (start::XX:XX)
            highlightContent = highlightContent.replace(/\(start::\d+:\d+\)/g, "");
            
            // 移除 #精华 标签
            highlightContent = highlightContent.replace(/#精华/g, "");
            
            // 清理多余空格
            highlightContent = highlightContent.trim();
            
            // 添加到结果数组
            rows.push({
                fileName: fileName,
                content: highlightContent,
                link: page.file.link
            });
        }
    }
}

// 显示结果
if (rows.length > 0) {
    dv.table(["日期", "内容"], 
        rows.map(row => [
            `[[${row.fileName}|${row.fileName}]]`, 
            row.content
        ])
    );
} else {
    dv.paragraph("本周暂无精华内容");
}
```
---  
## 😴本周睡眠统计

```dataviewjs
// 获取包含测试数据的日期范围
const startOfWeek = dv.date("2025-04-28");  // 固定为包含测试数据的周
const endOfWeek = dv.date("2025-05-05");    // 结束日期

// 查询特定日期范围内的所有日记
const pages = dv.pages('"0_Bullet Journal/Daily Notes"')
  .where(p => {
    const dateMatch = p.file.name.split(" ")[0];
    if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
      const fileDate = dv.date(dateMatch);
      return fileDate >= startOfWeek && fileDate < endOfWeek;
    }
    return false;
  })
  .sort(p => p.file.name);

// 调试信息 - 显示找到的文件
dv.paragraph(`查询到的文件: ${pages.map(p => p.file.name).join(", ")}`);

// 准备数据
let totalSleepTime = 0;
let totalTimeInBed = 0;
let totalSleepQuality = 0;
let daysWithSleepData = 0;
let sleepData = [];

// 处理每一天的数据
for (let page of pages) {
  // 调试信息 - 显示每个文件的睡眠数据
  dv.paragraph(`检查文件 ${page.file.name}: 睡眠数据 - 上床时间: ${page.sleep_bedtime}, 入睡时间: ${page.sleep_fallasleep}, 醒来时间: ${page.sleep_wakeup}, 起床时间: ${page.sleep_outofbed}`);
  
  if (page.sleep_bedtime && page.sleep_fallasleep && page.sleep_wakeup && page.sleep_outofbed) {
    daysWithSleepData++;
    
    // 转换时间字符串为分钟数
    function timeToMinutes(timeStr) {
      const [hours, minutes] = timeStr.split(':').map(Number);
      return hours * 60 + minutes;
    }
    
    // 处理跨天的情况
    function calculateDuration(start, end) {
      let startMin = timeToMinutes(start);
      let endMin = timeToMinutes(end);
      
      // 如果结束时间小于开始时间，假设跨天
      if (endMin < startMin) {
        endMin += 24 * 60;
      }
      
      return endMin - startMin;
    }
    
    // 计算各项指标
    const totalSleepTimeToday = calculateDuration(page.sleep_fallasleep, page.sleep_wakeup);
    const timeInBedToday = calculateDuration(page.sleep_bedtime, page.sleep_outofbed);
    
    // 计算实际睡眠时间（减去醒来的时间）
    const wakeDuration = page.sleep_wakeups_duration || 0;
    const actualSleepTimeToday = totalSleepTimeToday - wakeDuration;
    
    totalSleepTime += actualSleepTimeToday;
    totalTimeInBed += timeInBedToday;
    totalSleepQuality += page.sleep_quality || 3;
    
    // 提取日期用于显示
    const dateStr = page.file.name.split(" ")[0];
    
    // 存储每天的数据用于表格显示
    sleepData.push({
      date: dateStr,
      sleepTime: actualSleepTimeToday,
      timeInBed: timeInBedToday,
      quality: page.sleep_quality || "-",
      wakeups: page.sleep_wakeups || 0,
      wakeDuration: wakeDuration
    });
  }
}

// 调试信息
dv.paragraph(`找到 ${pages.length} 个日记文件，其中 ${daysWithSleepData} 个有睡眠数据`);

// 计算平均值
const avgSleepTime = daysWithSleepData ? totalSleepTime / daysWithSleepData : 0;
const avgTimeInBed = daysWithSleepData ? totalTimeInBed / daysWithSleepData : 0;
const avgSleepQuality = daysWithSleepData ? totalSleepQuality / daysWithSleepData : 0;
const avgSleepEfficiency = avgTimeInBed ? Math.round((avgSleepTime / avgTimeInBed) * 100) : 0;

// 格式化时间的函数
function formatDuration(minutes) {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return `${hours}h${String(mins).padStart(2, '0')}m`;
}

// 显示汇总数据
dv.header(3, "睡眠汇总");
dv.paragraph(`- **实际睡眠时长**: ${formatDuration(avgSleepTime)}`);
dv.paragraph(`- **平均卧床时长**: ${formatDuration(avgTimeInBed)}`);
dv.paragraph(`- **平均睡眠质量**: ${avgSleepQuality.toFixed(1)}/5`);
dv.paragraph(`- **平均睡眠效率**: ${avgSleepEfficiency}%`);

// 显示每日数据表格
if (sleepData.length > 0) {
  dv.header(3, "每日睡眠数据");
  dv.table(["日期", "实际睡眠", "卧床时长", "质量", "醒来次数", "醒来时长"], 
    sleepData.map(d => [
      d.date,
      formatDuration(d.sleepTime),
      formatDuration(d.timeInBed),
      d.quality,
      d.wakeups,
      `${d.wakeDuration}m`
    ])
  );
} else {
  dv.paragraph("本周暂无睡眠数据");
}
```
--- 
# 🍚 精神食粮 Spiritual Nourishment
### 🤔 每日斯多葛 Stoic
```dataview
LIST without id Stoic
FROM "0_Bullet Journal/Daily Notes"
WHERE date(split(file.name, " ")[0]) > date(this.end_date) - dur(7 days)
    AND date(split(file.name, " ")[0]) <= date(this.end_date)
    AND Stoic
```
### 📖 书籍 Book
```dataview
LIST without id "《" + book + "》" + " 本周共计📖时长" + sum(rows.Reading_min) + "min " 
FROM "0_Bullet Journal/Daily Notes"
WHERE date(split(file.name, " ")[0]) > date(this.end_date) - dur(7 days)
    AND date(split(file.name, " ")[0]) <= date(this.end_date)
    AND book
GROUP BY book
```
```dataview
LIST 
FROM "2_Literature notes/2_2_Book"
WHERE
  file.ctime >= date(this.start_date) AND
  file.ctime < date(this.end_date) + dur(1 day)
```
### 👂播客 Podcast
```dataview
LIST without id podcast
FROM "0_Bullet Journal/Daily Notes"
	WHERE date(split(file.name, " ")[0]) > date(this.end_date) - dur(7 days)
	AND date(split(file.name, " ")[0]) <= date(this.end_date)
	AND podcast != null
```
### 📑 文章 Airticle
```dataview
LIST
FROM "2_Literature notes/2_1_Article"
WHERE
  file.ctime >= date(this.start_date) AND
  file.ctime < date(this.end_date) + dur(1 day)
```
### 🎬 电影电视 Movie & TV
```dataview
LIST
FROM "2_Literature notes/2_4_Movie TV"
WHERE
  file.ctime >= date(this.start_date) AND
  file.ctime < date(this.end_date) + dur(1 day)
```
### 📱 视频 Vedio
```dataview
LIST 
FROM "2_Literature notes/2_5_Vedio"
WHERE
  file.ctime >= date(this.start_date) AND
  file.ctime < date(this.end_date) + dur(1 day)
```
### 🎵 音乐 Music
```dataview
LIST 
FROM "2_Literature notes/2_6_Music"
WHERE
  file.ctime >= date(this.start_date) AND
  file.ctime < date(this.end_date) + dur(1 day)
```
### 📃小说Fiction
```dataview
LIST 
FROM "2_Literature notes/2_7_Fiction"
WHERE
  file.ctime >= date(this.start_date) AND
  file.ctime < date(this.end_date) + dur(1 day)
```
# ？ 思考与发问 Thinking & Question
```dataview
LIST 
FROM "3_Permanent notes/3_3_Questions"
WHERE
  file.ctime >= date(this.start_date) AND
  file.ctime < date(this.end_date) + dur(1 day)
```
---

# 📍 本周项目全景 Weekly Project Overview

```dataviewjs
// 获取当前周记的开始和结束日期
const startDate = dv.current().start_date;
const endDate = dv.current().end_date;

// 1. 从日记收集活跃项目
const dailyPages = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => {
        const dateMatch = p.file.name.split(" ")[0];
        if (dateMatch && dateMatch.match(/\d{4}-\d{2}-\d{2}/)) {
            const fileDate = dv.date(dateMatch);
            return fileDate >= startDate && fileDate <= endDate;
        }
        return false;
    })
    .sort(p => p.file.name);

// 收集所有项目并去重
const projectMap = new Map();

for (const page of dailyPages) {
    if (page.project) {
        // 确保project是数组
        const projects = Array.isArray(page.project) ? page.project : [page.project];
        
        for (const project of projects) {
            // 安全地处理项目名称，确保它是字符串并且不为null/undefined
            if (project == null) continue; // 跳过null或undefined值
            
            // 转换为字符串并标准化
            const projectStr = String(project);
            const normalizedName = projectStr.trim();
            
            // 跳过空字符串
            if (normalizedName === "") continue;
            
            // 使用标准化的项目名称作为键来去重
            if (!projectMap.has(normalizedName)) {
                projectMap.set(normalizedName, {
                    name: projectStr, // 保留原始名称以显示
                    isActive: true,
                    isNew: false,
                    dates: [page.file.name.split(" ")[0]],
                    link: null,
                    tasks: { active: 0, completed: 0 }
                });
            } else {
                // 添加新的日期，如果该日期尚未添加
                const existing = projectMap.get(normalizedName);
                if (!existing.dates.includes(page.file.name.split(" ")[0])) {
                    existing.dates.push(page.file.name.split(" ")[0]);
                }
            }
        }
    }
}

// 2. 从项目文件夹收集新建项目
const newProjects = dv.pages('"6_Project Notes"')
    .where(p => p.file.ctime >= startDate && p.file.ctime <= endDate)
    .sort(p => p.file.ctime);

for (const project of newProjects) {
    // 检查文件名是否存在
    if (!project.file.name) continue;
    
    const projectName = String(project.file.name).trim();
    
    // 跳过空字符串
    if (projectName === "") continue;
    
    if (projectMap.has(projectName)) {
        // 如果项目已经存在于活跃项目中，标记为新建
        const existing = projectMap.get(projectName);
        existing.isNew = true;
        existing.link = project.file.link;
    } else {
        // 否则添加为新建项目
        projectMap.set(projectName, {
            name: projectName,
            isActive: false,
            isNew: true,
            dates: [],
            link: project.file.link,
            tasks: { active: 0, completed: 0 }
        });
    }
}

// 将Map转换为数组以便显示
const allProjects = Array.from(projectMap.values());

// 显示结果
if (allProjects.length > 0) {
    dv.header(3, "📊 项目活动总览");
    
    // 创建统一的项目表格
    dv.table(
        ["项目", "状态", "活跃天数", "详情"],
        allProjects.map(p => {
            // 构建状态标识
            let status = [];
            if (p.isNew) status.push("✨ 新建");
            if (p.isActive) status.push("🔄 活跃");
            
            // 构建项目名称显示
            const projectDisplay = p.link ? p.link : p.name;
            
            // 构建详情信息
            let details = [];
            if (p.dates.length > 0) {
                details.push(`活跃日期: ${p.dates.join(", ")}`);
            }
            
            return [
                projectDisplay,
                status.join(" + "),
                p.dates.length || "-",
                details.join("<br>")
            ];
        })
    );
    
    // 项目活动统计
    dv.header(4, "📈 项目统计");
    const newAndActiveCount = allProjects.filter(p => p.isNew && p.isActive).length;
    const onlyActiveCount = allProjects.filter(p => p.isActive && !p.isNew).length;
    const onlyNewCount = allProjects.filter(p => p.isNew && !p.isActive).length;
    
    dv.paragraph(`- 本周共有 **${allProjects.length}** 个项目活动`);
    dv.paragraph(`- 其中 **${newAndActiveCount}** 个新建且活跃的项目`);
    dv.paragraph(`- **${onlyActiveCount}** 个持续进行的项目`);
    dv.paragraph(`- **${onlyNewCount}** 个新建待启动的项目`);
    
    // 可选：按活跃度排序的项目列表
    dv.header(4, "🔥 项目热度排行");
    const sortedProjects = [...allProjects].sort((a, b) => b.dates.length - a.dates.length);
    for (let i = 0; i < Math.min(sortedProjects.length, 5); i++) {
        const p = sortedProjects[i];
        if (p.dates.length > 0) {
            const projectDisplay = p.link ? p.link : p.name;
            dv.paragraph(`${i+1}. ${projectDisplay} - **${p.dates.length}** 天活跃度`);
        }
    }
} else {
    dv.paragraph("*本周暂无项目活动*");
}
```
### 🧰 重点行动项 Key Tasks
<!-- 本周截止的任务 -->
```tasks
due on this week
sort by due reverse
short
```
### ✅ 本周已完成项 Done
<!-- 本周已完成的任务 -->
```tasks
done
done after <% moment(tp.date.weekday("YYYY-MM-DD", 0, tp.file.title, "YYYY-[W]WW")).subtract(1, 'days').format("YYYY-MM-DD") %>
done before <% tp.date.weekday("YYYY-MM-DD", 7, tp.file.title, "YYYY-[W]WW") %>
sort by done reverse
short
```
---
# 🤔 收获与思考 Harvest & Thinking
### 🏆 本周亮点 Weekly Highlights
<!-- What important tasks did you complete? What progress is worth celebrating? -->
- 
### 🧗 克服的挑战 Challenges Overcome
<!-- What difficulties or obstacles did you encounter? How did you address these challenges? -->
- 
### 💡 关键洞察 Key Insights
<!-- What new knowledge or skills did you learn? What new inspirations or ideas did you have? -->
- 
### 🛠️ 改进空间 Areas for Improvement
<!-- What could be improved today? How can you do better tomorrow? -->
- 

### 🌱 成长轨迹 Growth Trajectory
<!-- This week's progress in relation to long-term goals -->
-

---
## 📅 周复盘
> 基于复盘.txt的周复盘框架，系统性地回顾和规划每一周

### 📊 汇总本周事件
**本周取得什么结果，有什么意外收获？**
-

**本周完成了什么计划，发生了什么事情？**
-

**本周见了什么重要的人，谈了什么？**
-

**本周遇到了什么问题，进展怎样？**
-

### ✅ 计划完成情况
**工作交付了什么，任务还有什么没完成？**
- 工作交付：
- 未完成任务：

**这一周生活状态怎样，家庭氛围怎样？**
-

### 🤔 本周反思
**没完成的事情问题出在哪，怎么解决？**
-

**完成的事情有没有更好的完成方式？**
-

### 💎 复盘收获
**发现了什么问题，总结出了什么经验？**
-

**本周学到了什么？**
-

### 🎯 下周调整与计划
**别列太多未完成，重点是找原因；合理规划时间，保持工作与生活的平衡**
- 继续坚持长期主义，良好的习惯继续坚持✊
-

---
# 💻 知识库维护 Knowledge System
### ➕ 本周创建的笔记 Created
```dataview
TABLE without id file.cday AS Date, file.link AS Name, file.folder AS Folder
WHERE file.cday > date(this.end_date) - dur(7 days) 
	AND file.cday <= date(this.end_date)
	AND contains(file.folder,"Templates") = False 
	AND contains(file.folder,"5") = False
SORT file.cday DESC,file.folder ASC
//LIMIT 20
```

### 🔧 本周修改的笔记 Updated
```dataview
TABLE without id file.mday AS Date, file.link AS Name, file.folder AS Folder
WHERE file.mday > date(this.end_date) - dur(7 days)
	AND file.mday <= date(this.end_date)
	AND contains(file.folder,"Templates") = False 
	AND contains(file.folder,"5") = False
SORT file.mday DESC,file.folder ASC
```

