/**
 * 复盘快速入口系统
 * 
 * 为Obsidian创建便捷的复盘启动入口，支持快速创建各种类型的复盘笔记
 * 可以通过QuickAdd插件或命令面板调用
 */

// 复盘类型配置
const REVIEW_TYPES = {
    beginner: {
        name: "🌱 入门级日复盘",
        template: "Templates/复盘引导/入门级日复盘.md",
        folder: "0_Bullet Journal/Daily Notes",
        description: "3个简单问题，2-3分钟完成",
        experience: 10
    },
    standard: {
        name: "🌿 标准级日复盘", 
        template: "Templates/复盘引导/标准级日复盘.md",
        folder: "0_Bullet Journal/Daily Notes",
        description: "5个核心问题，5-8分钟完成",
        experience: 15
    },
    advanced: {
        name: "🌳 深度复盘模式",
        template: "Templates/5_BuJo - Daily Log.md",
        folder: "0_Bullet Journal/Daily Notes", 
        description: "完整问题集，10-15分钟完成",
        experience: 25
    },
    weekly: {
        name: "📊 周复盘",
        template: "Templates/5_BuJo - Weekly Log.md",
        folder: "0_Bullet Journal/Weekly Notes",
        description: "周度回顾与规划",
        experience: 50
    },
    monthly: {
        name: "📈 月复盘",
        template: "Templates/5_BuJo - Monthly Log.md", 
        folder: "0_Bullet Journal/Monthly Notes",
        description: "月度深度分析",
        experience: 200
    },
    yearly: {
        name: "🎉 年复盘",
        template: "Templates/5_BuJo - Yearly Log.md",
        folder: "0_Bullet Journal/Yearly Notes", 
        description: "年度全面回顾",
        experience: 500
    }
};

/**
 * 主入口函数 - 显示复盘类型选择界面
 */
async function quickReviewEntry(params) {
    const quickAdd = params.quickAdd;
    const app = params.app || window.app;
    
    try {
        // 获取用户复盘经验，智能推荐
        const recommendation = await getRecommendation(app);
        
        // 构建选择选项
        const choices = Object.entries(REVIEW_TYPES).map(([key, config]) => {
            const isRecommended = key === recommendation.level;
            const prefix = isRecommended ? "⭐ [推荐] " : "";
            return {
                label: `${prefix}${config.name}`,
                description: config.description,
                value: key
            };
        });
        
        // 添加智能推荐说明
        choices.unshift({
            label: `💡 智能推荐：${recommendation.message}`,
            description: "基于您的复盘经验智能推荐",
            value: "info"
        });
        
        // 显示选择界面
        const selectedType = await quickAdd.suggester(
            choices.map(c => `${c.label}\n${c.description}`),
            choices.map(c => c.value),
            false,
            "🎯 选择复盘类型"
        );
        
        if (!selectedType || selectedType === "info") {
            return;
        }
        
        // 创建复盘笔记
        await createReviewNote(app, selectedType, quickAdd);
        
    } catch (error) {
        console.error("复盘快速入口错误:", error);
        new Notice("❌ 创建复盘笔记失败，请检查模板文件是否存在");
    }
}

/**
 * 获取智能推荐
 */
async function getRecommendation(app) {
    try {
        // 统计用户复盘经验
        const stats = await getUserReviewStats(app);
        
        let recommendedLevel = "beginner";
        let message = "建议从入门级开始建立复盘习惯";
        
        if (stats.totalExperience >= 500 && stats.standardReviews >= 20) {
            recommendedLevel = "advanced";
            message = "您已经是复盘专家，推荐使用深度模式";
        } else if (stats.totalExperience >= 100 && stats.beginnerReviews >= 7) {
            recommendedLevel = "standard";
            message = "您已建立基本习惯，可以尝试标准级复盘";
        }
        
        return { level: recommendedLevel, message, stats };
    } catch (error) {
        console.error("获取推荐失败:", error);
        return { level: "beginner", message: "建议从入门级开始", stats: {} };
    }
}

/**
 * 统计用户复盘数据
 */
async function getUserReviewStats(app) {
    const files = app.vault.getMarkdownFiles();
    
    let beginnerReviews = 0;
    let standardReviews = 0;
    let totalReviews = 0;
    
    for (const file of files) {
        try {
            const cache = app.metadataCache.getFileCache(file);
            if (cache?.frontmatter?.tags) {
                const tags = Array.isArray(cache.frontmatter.tags) 
                    ? cache.frontmatter.tags 
                    : [cache.frontmatter.tags];
                
                if (tags.includes('beginner-level') && cache.frontmatter.review_type === 'daily') {
                    beginnerReviews++;
                    totalReviews++;
                } else if (tags.includes('standard-level') && cache.frontmatter.review_type === 'daily') {
                    standardReviews++;
                    totalReviews++;
                }
            }
        } catch (error) {
            // 忽略单个文件的错误
            continue;
        }
    }
    
    const totalExperience = beginnerReviews * 10 + standardReviews * 15;
    
    return {
        beginnerReviews,
        standardReviews,
        totalReviews,
        totalExperience
    };
}

/**
 * 创建复盘笔记
 */
async function createReviewNote(app, reviewType, quickAdd) {
    const config = REVIEW_TYPES[reviewType];
    if (!config) {
        new Notice("❌ 未知的复盘类型");
        return;
    }
    
    try {
        // 生成文件名
        const fileName = await generateFileName(reviewType);
        const filePath = `${config.folder}/${fileName}`;
        
        // 检查文件是否已存在
        const existingFile = app.vault.getAbstractFileByPath(filePath);
        if (existingFile) {
            const shouldOpen = await quickAdd.yesNoPrompt(
                `📝 复盘笔记已存在：${fileName}\n\n是否直接打开现有笔记？`,
                "打开现有笔记"
            );
            
            if (shouldOpen) {
                await app.workspace.openLinkText(filePath, "", false);
                new Notice(`📖 已打开现有复盘笔记`);
                return;
            } else {
                return;
            }
        }
        
        // 读取模板内容
        const templateFile = app.vault.getAbstractFileByPath(config.template);
        if (!templateFile) {
            new Notice(`❌ 模板文件不存在：${config.template}`);
            return;
        }
        
        const templateContent = await app.vault.read(templateFile);
        
        // 处理模板变量（简化版本）
        const processedContent = await processTemplate(templateContent, reviewType);
        
        // 创建文件
        const newFile = await app.vault.create(filePath, processedContent);
        
        // 打开新创建的文件
        await app.workspace.openLinkText(filePath, "", false);
        
        // 显示成功消息
        new Notice(`✅ 已创建${config.name}：${fileName}`);
        
        // 记录创建事件（可选）
        console.log(`复盘笔记创建成功: ${filePath}`);
        
    } catch (error) {
        console.error("创建复盘笔记失败:", error);
        new Notice(`❌ 创建失败：${error.message}`);
    }
}

/**
 * 生成文件名
 */
async function generateFileName(reviewType) {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const weekday = ['日', '一', '二', '三', '四', '五', '六'][now.getDay()];
    
    switch (reviewType) {
        case 'beginner':
        case 'standard':
        case 'advanced':
            return `${year}-${month}-${day} 星期${weekday} Daily Log.md`;
        case 'weekly':
            const weekNum = getWeekNumber(now);
            return `${year}-W${String(weekNum).padStart(2, '0')} Weekly Log.md`;
        case 'monthly':
            return `${year}-${month} Monthly Log.md`;
        case 'yearly':
            return `${year} Yearly Log.md`;
        default:
            return `${year}-${month}-${day} Review.md`;
    }
}

/**
 * 获取周数
 */
function getWeekNumber(date) {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date - firstDayOfYear) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
}

/**
 * 处理模板变量（简化版本）
 */
async function processTemplate(content, reviewType) {
    const now = new Date();
    
    // 替换基本的日期变量
    const replacements = {
        '<% tp.date.now("YYYY-MM-DDTHH:mm") %>': now.toISOString().slice(0, 16),
        '<% tp.date.now("YYYY-MM-DD") %>': now.toISOString().slice(0, 10),
        '<% tp.date.now("MM月DD日") %>': `${now.getMonth() + 1}月${now.getDate()}日`,
        '<% tp.date.now("YYYY-MM-DD HH:mm") %>': now.toISOString().slice(0, 16).replace('T', ' '),
        '<% tp.date.now("YYYY") %>': now.getFullYear().toString()
    };
    
    let processedContent = content;
    for (const [placeholder, replacement] of Object.entries(replacements)) {
        processedContent = processedContent.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), replacement);
    }
    
    return processedContent;
}

// 导出主函数供QuickAdd使用
module.exports = quickReviewEntry;
