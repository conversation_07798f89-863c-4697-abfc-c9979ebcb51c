{"name": "测试库", "version": "1.0.0", "description": "基于Obsidian的综合性知识管理和开发工具集成系统", "main": "index.js", "scripts": {"install-deps": "python scripts/install-dependencies.py", "setup": "npm run install-deps && npm run setup-config", "setup-config": "python scripts/setup-config.py", "test": "python -m pytest tests/", "lint": "eslint . && python -m pylint **/*.py", "format": "prettier --write . && python -m black **/*.py", "start": "python scripts/start-services.py", "build": "python scripts/build-project.py", "clean": "python scripts/cleanup.py", "dev": "npm run start", "mcp:start": "python scripts/start-mcp.py", "mcp:test": "python scripts/test-mcp.py", "mcp:diagnose": "python scripts/diagnose-mcp.py", "obsidian:setup": "python scripts/setup-obsidian.py", "promo:generate": "python scripts/generate-promo.py", "data:export": "python scripts/export-data.py", "backup": "python scripts/backup-project.py", "restore": "python scripts/restore-project.py"}, "keywords": ["obsidian", "knowledge-management", "mcp", "ai-tools", "automation", "python", "productivity", "content-generation", "data-processing", "workflow"], "author": "测试库项目团队", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-username/测试库.git"}, "bugs": {"url": "https://github.com/your-username/测试库/issues"}, "homepage": "https://github.com/your-username/测试库#readme", "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "dependencies": {"@types/styled-components": "^5.1.34", "styled-components": "^6.1.18"}, "devDependencies": {"eslint": "^8.57.0", "prettier": "^3.1.0", "@types/node": "^20.10.0", "typescript": "^5.3.0", "nodemon": "^3.0.0", "concurrently": "^8.2.0"}, "peerDependencies": {"python": ">=3.8.0"}, "config": {"python_version": ">=3.8.0", "obsidian_version": ">=1.0.0"}, "directories": {"doc": "docs", "test": "tests", "lib": "tools"}, "files": ["scripts/", "tools/", "config/", "docs/", "README.md", "requirements.txt", ".giti<PERSON>re"]}