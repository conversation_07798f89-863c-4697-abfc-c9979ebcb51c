---
tags:
  - quick-launcher
  - review-system
  - navigation
created: 2025-08-07
updated: 2025-08-07
---
# 🚀 复盘快速启动器

> 无需插件的复盘快速入口，通过简单点击即可开始复盘

## 🎯 智能推荐

```dataviewjs
// 智能推荐系统
const beginnerReviews = dv.pages('#beginner-level').where(p => p.review_type === 'daily').length;
const standardReviews = dv.pages('#standard-level').where(p => p.review_type === 'daily').length;
const totalReviews = beginnerReviews + standardReviews;
const totalExperience = beginnerReviews * 10 + standardReviews * 15;

let recommendedLevel = "beginner";
let recommendation = "";
let nextSteps = "";

if (totalExperience >= 500 && standardReviews >= 20) {
    recommendedLevel = "advanced";
    recommendation = "🌳 **推荐：深度复盘模式** - 您已经是复盘专家！";
    nextSteps = "继续使用深度模式，探索更多复盘技巧";
} else if (totalExperience >= 100 && beginnerReviews >= 7) {
    recommendedLevel = "standard";
    recommendation = "🌿 **推荐：标准级复盘** - 您已建立基本习惯！";
    nextSteps = "尝试更深入的5个问题，提升复盘质量";
} else {
    recommendedLevel = "beginner";
    recommendation = "🌱 **推荐：入门级复盘** - 从简单开始建立习惯！";
    nextSteps = "坚持完成7次入门级复盘，解锁标准模式";
}

dv.paragraph("## 🎯 为您推荐");
dv.paragraph(recommendation);
dv.paragraph(`**您的数据**: ${totalReviews}次复盘 | ${totalExperience}经验值`);
dv.paragraph(`**下一步**: ${nextSteps}`);
```

---
## 📅 快速创建复盘

### 🌱 入门级复盘（推荐新手）
**特点**: 3个简单问题 | 2-3分钟 | +10经验值

```button
name 🌱 创建入门级日复盘
type template
action Templates/复盘引导/入门级日复盘.md
templater true
```

**适合人群**: 刚开始复盘的用户
**升级条件**: 完成7次入门级复盘

---
### 🌿 标准级复盘（推荐进阶）
**特点**: 5个核心问题 | 5-8分钟 | +15经验值

```button
name 🌿 创建标准级日复盘
type template
action Templates/复盘引导/标准级日复盘.md
templater true
```

**适合人群**: 有一定复盘经验的用户
**升级条件**: 完成30次标准级复盘

---
### 🌳 深度复盘（专家模式）
**特点**: 完整问题集 | 10-15分钟 | +25经验值

```button
name 🌳 创建深度复盘
type template
action Templates/5_BuJo - Daily Log.md
templater true
```

**适合人群**: 复盘习惯稳定的用户
**特色功能**: 自动数据预填充、完整分析框架

---
## 📊 其他复盘类型

### 📅 周期性复盘

```button
name 📊 创建周复盘
type template
action Templates/5_BuJo - Weekly Log.md
templater true
```

```button
name 📈 创建月复盘
type template
action Templates/5_BuJo - Monthly Log.md
templater true
```

```button
name 🎉 创建年复盘
type template
action Templates/5_BuJo - Yearly Log.md
templater true
```

---
## 🎮 复盘进度

### 📊 当前状态
```dataviewjs
// 复盘进度统计
const beginnerReviews = dv.pages('#beginner-level').where(p => p.review_type === 'daily').length;
const standardReviews = dv.pages('#standard-level').where(p => p.review_type === 'daily').length;
const totalReviews = beginnerReviews + standardReviews;
const totalExperience = beginnerReviews * 10 + standardReviews * 15;

// 计算等级
let currentLevel = "新手";
let levelEmoji = "🌱";
if (totalExperience >= 500) {
    currentLevel = "专家";
    levelEmoji = "🌳";
} else if (totalExperience >= 100) {
    currentLevel = "进阶";
    levelEmoji = "🌿";
}

dv.table(
    ["指标", "数值", "状态"],
    [
        ["🏆 当前等级", `${levelEmoji} ${currentLevel}`, ""],
        ["📊 总复盘次数", `${totalReviews} 次`, totalReviews >= 30 ? "🌟 优秀" : totalReviews >= 10 ? "👍 良好" : "🌱 起步"],
        ["⭐ 累计经验值", `${totalExperience} 点`, totalExperience >= 500 ? "🏆 专家" : totalExperience >= 100 ? "🎯 进阶" : "🌱 新手"],
        ["🌱 入门级", `${beginnerReviews} 次`, beginnerReviews >= 7 ? "✅ 完成" : "🔄 进行中"],
        ["🌿 标准级", `${standardReviews} 次`, standardReviews >= 20 ? "✅ 熟练" : standardReviews > 0 ? "🔄 进行中" : "⏸️ 未开始"]
    ]
);
```

### 🏆 成就系统
```dataviewjs
// 成就统计
const beginnerReviews = dv.pages('#beginner-level').where(p => p.review_type === 'daily').length;
const standardReviews = dv.pages('#standard-level').where(p => p.review_type === 'daily').length;
const totalReviews = beginnerReviews + standardReviews;

const achievements = [
    { name: "初次复盘", emoji: "🌟", threshold: 1, current: totalReviews },
    { name: "坚持三天", emoji: "🔥", threshold: 3, current: totalReviews },
    { name: "一周达人", emoji: "🏆", threshold: 7, current: totalReviews },
    { name: "进阶复盘者", emoji: "🌿", threshold: 1, current: standardReviews },
    { name: "标准模式熟练者", emoji: "🎯", threshold: 10, current: standardReviews },
    { name: "复盘专家", emoji: "🏅", threshold: 30, current: standardReviews }
];

dv.paragraph("**🏆 成就收集：**");
achievements.forEach(achievement => {
    const unlocked = achievement.current >= achievement.threshold;
    const status = unlocked ? "✅" : "⏸️";
    const progress = unlocked ? "已解锁" : `${achievement.current}/${achievement.threshold}`;
    dv.paragraph(`${status} ${achievement.emoji} ${achievement.name} (${progress})`);
});
```

---
## 💡 使用技巧

### 🎯 快速上手
1. **新手用户**: 从入门级开始，每天花2-3分钟完成3个简单问题
2. **有经验用户**: 直接使用标准级，享受更深入的思考体验
3. **专家用户**: 使用深度模式，利用自动数据预填充功能

### ⚡ 效率提升
- **固定时间**: 建议每天睡前进行复盘
- **模板定制**: 可以根据个人需求调整复盘问题
- **数据追踪**: 定期查看复盘数据仪表盘了解进展

### 🔄 习惯养成
- **21天计划**: 坚持21天建立稳定的复盘习惯
- **渐进提升**: 从入门级逐步升级到标准级和深度级
- **成就激励**: 通过解锁成就保持动力

---
## 🔗 相关功能

### 📊 数据分析
- [[复盘系统/复盘数据仪表盘|📊 复盘数据仪表盘]] - 查看详细统计
- [[复盘系统/复盘引导中心|🎯 复盘引导中心]] - 完整导航

### ⚙️ 系统配置
- [[复盘系统/配置/复盘引导配置|⚙️ 引导系统配置]]
- [[复盘系统/配置/复盘系统配置|📋 系统配置]]

### 🚀 高级功能
- [[Attachment/scripts/QuickAdd配置指南|🚀 QuickAdd快速入口]] - 插件版本
- [[Attachment/scripts/复盘快速入口.js|🔧 脚本文件]] - 技术实现

---
## 📱 移动端支持

### 移动端使用
- 所有按钮在移动端Obsidian中都可正常使用
- 建议将此页面添加到收藏夹，方便快速访问
- 复盘模板在移动端也有良好的显示效果

### 快速访问
- 可以将此页面设为主页或添加到侧边栏
- 使用Obsidian的搜索功能快速找到此页面
- 建议创建快捷方式到桌面（移动端）

---
## 🎉 开始您的复盘之旅

选择适合您的复盘级别，点击上方按钮即可开始！记住：
- 🌱 **坚持比完美更重要**
- 🎯 **每天进步一点点**
- 🌟 **让复盘成为习惯**

---
*🚀 复盘快速启动器 v1.0 | 简单点击，开始成长*
