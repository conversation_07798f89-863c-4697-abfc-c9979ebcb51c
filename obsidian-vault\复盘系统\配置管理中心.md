---
tags:
  - config-manager
  - dynamic-config
  - review-system
created: 2025-08-07
updated: 2025-08-07
# 用户配置数据
user_config:
  daily_dimensions:
    - name: "今日成就"
      description: "今天做了什么？做这些事情用了多少时间？"
      required: true
      enabled: true
    - name: "今日反思"
      description: "应该做的事、不应该做的事、问题根本原因、时间分配改进"
      required: true
      enabled: true
    - name: "今日收获"
      description: "今天有哪些收获，包括感受和启发？"
      required: true
      enabled: true
    - name: "今日美好"
      description: "今天遇到了什么美好的事情？"
      required: false
      enabled: true
    - name: "今日感恩"
      description: "今天遇到了什么值得感恩的事？"
      required: false
      enabled: true
  
  weekly_dimensions:
    - name: "汇总本周事件"
      description: "本周结果、意外收获、重要会面、问题进展"
      required: true
      enabled: true
    - name: "计划完成情况"
      description: "工作交付、未完成任务、生活状态"
      required: true
      enabled: true
    - name: "本周反思"
      description: "问题分析、改进方式"
      required: true
      enabled: true
    - name: "复盘收获"
      description: "发现问题、总结经验、学习内容"
      required: true
      enabled: true
    - name: "下周调整与计划"
      description: "找原因、合理规划、工作生活平衡"
      required: true
      enabled: true
  
  display_settings:
    theme: "default"
    language: "zh-CN"
    date_format: "YYYY-MM-DD"
    time_format: "24h"
    show_advanced_features: true
    
  review_style:
    detailed: true
    include_prompts: true
    auto_fill_data: true
    template_style: "standard"
    
  reminder_settings:
    daily_enabled: true
    daily_time: "21:00"
    weekly_enabled: true
    weekly_day: "Sunday"
    weekly_time: "20:00"
---
# ⚙️ 复盘配置管理中心

> 动态配置管理系统，实时调整复盘系统的各项设置

## 🎯 当前配置概览

```dataviewjs
// 读取当前页面的配置数据
const config = dv.current().user_config;

if (config) {
    dv.paragraph("## 📊 配置状态总览");
    
    // 日复盘维度统计
    const dailyEnabled = config.daily_dimensions?.filter(d => d.enabled).length || 0;
    const dailyTotal = config.daily_dimensions?.length || 0;
    
    // 周复盘维度统计
    const weeklyEnabled = config.weekly_dimensions?.filter(d => d.enabled).length || 0;
    const weeklyTotal = config.weekly_dimensions?.length || 0;
    
    dv.table(
        ["配置项", "当前值", "状态"],
        [
            ["📅 日复盘维度", `${dailyEnabled}/${dailyTotal} 已启用`, dailyEnabled > 0 ? "✅ 正常" : "⚠️ 需配置"],
            ["📊 周复盘维度", `${weeklyEnabled}/${weeklyTotal} 已启用`, weeklyEnabled > 0 ? "✅ 正常" : "⚠️ 需配置"],
            ["🎨 界面主题", config.display_settings?.theme || "default", "✅ 正常"],
            ["🌐 语言设置", config.display_settings?.language || "zh-CN", "✅ 正常"],
            ["⏰ 日复盘提醒", config.reminder_settings?.daily_enabled ? `${config.reminder_settings.daily_time}` : "已关闭", config.reminder_settings?.daily_enabled ? "✅ 已启用" : "⏸️ 已关闭"],
            ["📅 周复盘提醒", config.reminder_settings?.weekly_enabled ? `${config.reminder_settings.weekly_day} ${config.reminder_settings.weekly_time}` : "已关闭", config.reminder_settings?.weekly_enabled ? "✅ 已启用" : "⏸️ 已关闭"]
        ]
    );
} else {
    dv.paragraph("⚠️ 配置数据未找到，请检查YAML front matter");
}
```

---
## 🛠️ 配置编辑器

### 📝 日复盘维度配置
```dataviewjs
// 显示日复盘维度配置
const config = dv.current().user_config;

if (config?.daily_dimensions) {
    dv.paragraph("**📅 日复盘维度设置：**");
    
    config.daily_dimensions.forEach((dimension, index) => {
        const status = dimension.enabled ? "✅" : "⏸️";
        const required = dimension.required ? "🔒 必需" : "🔓 可选";
        
        dv.paragraph(`\n**${index + 1}. ${dimension.name}** ${status} ${required}`);
        dv.paragraph(`*${dimension.description}*`);
        
        // 提供启用/禁用的说明
        if (dimension.enabled) {
            dv.paragraph(`📝 当前已启用，在复盘模板中会显示此维度`);
        } else {
            dv.paragraph(`⏸️ 当前已禁用，在复盘模板中不会显示此维度`);
        }
    });
    
    dv.paragraph("\n**💡 修改方法：**");
    dv.paragraph("1. 编辑本页面的YAML front matter中的 `user_config.daily_dimensions`");
    dv.paragraph("2. 修改 `enabled: true/false` 来启用或禁用维度");
    dv.paragraph("3. 修改 `description` 来自定义问题描述");
    dv.paragraph("4. 保存文件后配置立即生效");
} else {
    dv.paragraph("⚠️ 日复盘维度配置未找到");
}
```

### 📊 周复盘维度配置
```dataviewjs
// 显示周复盘维度配置
const config = dv.current().user_config;

if (config?.weekly_dimensions) {
    dv.paragraph("**📊 周复盘维度设置：**");
    
    config.weekly_dimensions.forEach((dimension, index) => {
        const status = dimension.enabled ? "✅" : "⏸️";
        const required = dimension.required ? "🔒 必需" : "🔓 可选";
        
        dv.paragraph(`\n**${index + 1}. ${dimension.name}** ${status} ${required}`);
        dv.paragraph(`*${dimension.description}*`);
    });
    
    dv.paragraph("\n**💡 修改方法：**");
    dv.paragraph("同日复盘维度，编辑 `user_config.weekly_dimensions` 配置");
} else {
    dv.paragraph("⚠️ 周复盘维度配置未找到");
}
```

---
## 🎨 显示设置

### 🖼️ 界面配置
```dataviewjs
// 显示界面配置
const config = dv.current().user_config;

if (config?.display_settings) {
    const settings = config.display_settings;
    
    dv.paragraph("**🎨 当前界面设置：**");
    dv.table(
        ["设置项", "当前值", "可选值"],
        [
            ["🎨 主题", settings.theme, "default, dark, light"],
            ["🌐 语言", settings.language, "zh-CN, en-US"],
            ["📅 日期格式", settings.date_format, "YYYY-MM-DD, MM/DD/YYYY, DD-MM-YYYY"],
            ["⏰ 时间格式", settings.time_format, "24h, 12h"],
            ["🔧 高级功能", settings.show_advanced_features ? "显示" : "隐藏", "true, false"]
        ]
    );
    
    dv.paragraph("\n**💡 修改方法：**");
    dv.paragraph("编辑 `user_config.display_settings` 中的对应值");
} else {
    dv.paragraph("⚠️ 显示设置配置未找到");
}
```

### 📝 复盘风格配置
```dataviewjs
// 显示复盘风格配置
const config = dv.current().user_config;

if (config?.review_style) {
    const style = config.review_style;
    
    dv.paragraph("**📝 当前复盘风格：**");
    dv.table(
        ["设置项", "当前值", "说明"],
        [
            ["📖 详细模式", style.detailed ? "启用" : "禁用", "是否显示详细的复盘问题"],
            ["💡 包含提示", style.include_prompts ? "启用" : "禁用", "是否显示问题提示和指导"],
            ["🤖 自动填充", style.auto_fill_data ? "启用" : "禁用", "是否自动填充已有数据"],
            ["🎨 模板风格", style.template_style, "模板的视觉风格"]
        ]
    );
} else {
    dv.paragraph("⚠️ 复盘风格配置未找到");
}
```

---
## ⏰ 提醒设置

### 🔔 提醒配置
```dataviewjs
// 显示提醒配置
const config = dv.current().user_config;

if (config?.reminder_settings) {
    const reminders = config.reminder_settings;
    
    dv.paragraph("**⏰ 当前提醒设置：**");
    dv.table(
        ["提醒类型", "状态", "时间", "操作建议"],
        [
            ["📅 日复盘提醒", 
             reminders.daily_enabled ? "✅ 已启用" : "⏸️ 已禁用", 
             reminders.daily_time || "未设置",
             reminders.daily_enabled ? "可在设置中调整时间" : "建议启用以养成习惯"],
            ["📊 周复盘提醒", 
             reminders.weekly_enabled ? "✅ 已启用" : "⏸️ 已禁用", 
             `${reminders.weekly_day || "未设置"} ${reminders.weekly_time || ""}`,
             reminders.weekly_enabled ? "可调整提醒日期和时间" : "建议启用以保持连续性"]
        ]
    );
    
    dv.paragraph("\n**💡 提醒设置说明：**");
    dv.paragraph("- 提醒功能需要配合其他插件（如Calendar、Reminder等）使用");
    dv.paragraph("- 修改 `user_config.reminder_settings` 来调整提醒配置");
    dv.paragraph("- 时间格式使用24小时制，如 '21:00'");
} else {
    dv.paragraph("⚠️ 提醒设置配置未找到");
}
```

---
## 🔧 配置管理工具

### 📋 配置验证
```dataviewjs
// 配置验证功能
const config = dv.current().user_config;
let validationErrors = [];
let validationWarnings = [];

// 验证日复盘维度
if (!config?.daily_dimensions || config.daily_dimensions.length === 0) {
    validationErrors.push("日复盘维度配置为空");
} else {
    const enabledDaily = config.daily_dimensions.filter(d => d.enabled);
    if (enabledDaily.length === 0) {
        validationWarnings.push("所有日复盘维度都已禁用");
    }
    
    // 检查必需维度
    const requiredDaily = config.daily_dimensions.filter(d => d.required && !d.enabled);
    if (requiredDaily.length > 0) {
        validationErrors.push(`必需的日复盘维度被禁用: ${requiredDaily.map(d => d.name).join(", ")}`);
    }
}

// 验证周复盘维度
if (!config?.weekly_dimensions || config.weekly_dimensions.length === 0) {
    validationErrors.push("周复盘维度配置为空");
} else {
    const enabledWeekly = config.weekly_dimensions.filter(d => d.enabled);
    if (enabledWeekly.length === 0) {
        validationWarnings.push("所有周复盘维度都已禁用");
    }
}

// 验证显示设置
if (!config?.display_settings) {
    validationWarnings.push("显示设置配置缺失，将使用默认值");
}

// 验证提醒设置
if (!config?.reminder_settings) {
    validationWarnings.push("提醒设置配置缺失，提醒功能将不可用");
}

// 显示验证结果
dv.paragraph("**🔍 配置验证结果：**");

if (validationErrors.length === 0 && validationWarnings.length === 0) {
    dv.paragraph("✅ **配置验证通过** - 所有配置项都正常");
} else {
    if (validationErrors.length > 0) {
        dv.paragraph("❌ **发现配置错误：**");
        validationErrors.forEach(error => {
            dv.paragraph(`- ${error}`);
        });
    }
    
    if (validationWarnings.length > 0) {
        dv.paragraph("⚠️ **配置警告：**");
        validationWarnings.forEach(warning => {
            dv.paragraph(`- ${warning}`);
        });
    }
}

// 提供修复建议
if (validationErrors.length > 0 || validationWarnings.length > 0) {
    dv.paragraph("\n**🔧 修复建议：**");
    dv.paragraph("1. 检查YAML front matter中的配置格式是否正确");
    dv.paragraph("2. 确保必需的维度已启用");
    dv.paragraph("3. 补充缺失的配置项");
    dv.paragraph("4. 保存文件后重新验证");
}
```

### 💾 配置备份与恢复
```dataviewjs
// 配置备份功能
const config = dv.current().user_config;

dv.paragraph("**💾 配置备份与恢复：**");

if (config) {
    // 生成配置备份
    const backupData = {
        backup_date: new Date().toISOString(),
        version: "v1.0",
        config: config
    };
    
    const backupJson = JSON.stringify(backupData, null, 2);
    
    dv.paragraph("**📋 当前配置备份（JSON格式）：**");
    dv.paragraph("```json");
    dv.paragraph(backupJson);
    dv.paragraph("```");
    
    dv.paragraph("\n**💡 备份使用方法：**");
    dv.paragraph("1. 复制上面的JSON内容");
    dv.paragraph("2. 保存到安全的地方作为备份");
    dv.paragraph("3. 需要恢复时，将JSON内容转换回YAML格式");
    dv.paragraph("4. 替换本页面YAML front matter中的user_config部分");
} else {
    dv.paragraph("⚠️ 无法生成配置备份，配置数据不完整");
}
```

---
## 🚀 配置应用

### 📝 生成自定义模板
```dataviewjs
// 基于当前配置生成自定义模板预览
const config = dv.current().user_config;

if (config?.daily_dimensions) {
    dv.paragraph("**📝 基于当前配置的日复盘模板预览：**");
    
    dv.paragraph("```markdown");
    dv.paragraph("# 📅 日复盘 - {{date}}");
    dv.paragraph("");
    
    const enabledDimensions = config.daily_dimensions.filter(d => d.enabled);
    
    enabledDimensions.forEach((dimension, index) => {
        dv.paragraph(`## ${index + 1}. ${dimension.name}`);
        if (config.review_style?.include_prompts) {
            dv.paragraph(`**${dimension.description}**`);
        }
        dv.paragraph("- ");
        dv.paragraph("");
    });
    
    dv.paragraph("```");
    
    dv.paragraph(`\n**📊 模板统计：** 包含 ${enabledDimensions.length} 个维度`);
    
    if (config.review_style?.auto_fill_data) {
        dv.paragraph("🤖 **自动填充已启用** - 模板将包含数据预填充功能");
    }
} else {
    dv.paragraph("⚠️ 无法生成模板预览，日复盘维度配置缺失");
}
```

### 🔄 配置同步状态
```dataviewjs
// 显示配置同步状态
dv.paragraph("**🔄 配置同步状态：**");

const config = dv.current().user_config;
const lastUpdated = dv.current().updated;

dv.table(
    ["项目", "状态", "说明"],
    [
        ["📄 配置文件", config ? "✅ 已加载" : "❌ 加载失败", "当前页面的配置数据"],
        ["⏰ 最后更新", lastUpdated || "未知", "配置文件的最后修改时间"],
        ["🔄 实时生效", "✅ 支持", "修改配置后立即生效"],
        ["💾 自动保存", "✅ 支持", "Obsidian自动保存配置更改"],
        ["🔗 模板集成", "🔄 开发中", "与复盘模板的集成状态"],
        ["📱 跨设备同步", "✅ 支持", "通过Obsidian同步功能"]
    ]
);

dv.paragraph("\n**💡 同步说明：**");
dv.paragraph("- 配置更改会立即在当前页面生效");
dv.paragraph("- 新的复盘笔记将使用最新的配置");
dv.paragraph("- 已创建的复盘笔记不会自动更新");
dv.paragraph("- 跨设备同步依赖Obsidian的同步功能");
```

---
## 📚 使用指南

### 🎯 快速配置指南
1. **修改复盘维度**：
   - 编辑YAML front matter中的 `daily_dimensions` 或 `weekly_dimensions`
   - 设置 `enabled: true/false` 来启用或禁用维度
   - 修改 `description` 来自定义问题描述

2. **调整显示设置**：
   - 修改 `display_settings` 中的主题、语言等选项
   - 调整 `review_style` 来改变复盘风格

3. **设置提醒**：
   - 配置 `reminder_settings` 来启用提醒功能
   - 需要配合其他插件使用

### 🔧 高级配置
- **自定义维度**：可以添加新的复盘维度
- **模板集成**：配置会影响新创建的复盘笔记
- **数据验证**：系统会自动验证配置的正确性

---
## 🔗 相关功能

- [[复盘系统/复盘引导中心|🎯 复盘引导中心]] - 系统导航
- [[复盘系统/复盘快速启动器|🚀 快速启动器]] - 创建复盘
- [[复盘系统/复盘数据仪表盘|📊 数据仪表盘]] - 数据统计

---
*⚙️ 复盘配置管理中心 v1.0 | 让复盘系统更加个性化*
