# Augment IDE Obsidian MCP 配置指南

## 📋 配置文件

**文件名**: `Augment-Obsidian-MCP配置.json`

```json
{
  "mcpServers": {
    "mcp-obsidian": {
      "command": "uvx",
      "args": ["mcp-obsidian"],
      "env": {
        "OBSIDIAN_API_KEY": "6f5525ea6f0a39901dda68c553a85ac9cd7a58dda2f036899f28c416deb646d3",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```

## 🎯 配置步骤

### 1. 找到Augment配置目录
Augment IDE的MCP配置文件通常位于：
- **Windows**: `%APPDATA%\Augment\mcp_config.json`
- **macOS**: `~/Library/Application Support/Augment/mcp_config.json`
- **Linux**: `~/.config/Augment/mcp_config.json`

### 2. 配置方法

#### 方法A：新建配置文件
如果没有现有配置，直接复制 `Augment-Obsidian-MCP配置.json` 的内容到Augment配置文件。

#### 方法B：合并到现有配置
如果已有MCP配置，将 `mcp-obsidian` 部分添加到现有的 `mcpServers` 对象中：

```json
{
  "mcpServers": {
    "现有的MCP服务": {
      // 现有配置...
    },
    "mcp-obsidian": {
      "command": "uvx",
      "args": ["mcp-obsidian"],
      "env": {
        "OBSIDIAN_API_KEY": "6f5525ea6f0a39901dda68c553a85ac9cd7a58dda2f036899f28c416deb646d3",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```

### 3. 重启Augment
保存配置后，重启Augment IDE使配置生效。

## 🔧 配置参数说明

- **OBSIDIAN_API_KEY**: 你的Obsidian Local REST API插件密钥
- **OBSIDIAN_HOST**: Obsidian API服务地址（默认127.0.0.1）
- **OBSIDIAN_PORT**: Obsidian API服务端口（默认27124）

## 🧪 测试功能

配置完成后，在Augment中尝试：

1. **列出文件**: "列出我的Obsidian知识库中的所有文件"
2. **搜索内容**: "搜索包含'项目'关键词的笔记"
3. **读取笔记**: "读取我的Homepage.md文件内容"
4. **创建笔记**: "创建一个新的笔记文件"

## ⚠️ 注意事项

1. **确保Obsidian运行**: 配置前确保Obsidian正在运行且Local REST API插件已启用
2. **网络连接**: 确保127.0.0.1:27124端口可访问
3. **uvx工具**: 确保系统已安装uv工具（`pip install uv`）

## 🆘 故障排除

### 连接失败
- 检查Obsidian是否运行
- 检查Local REST API插件是否启用
- 验证API Key是否正确

### 命令不存在
- 安装uv工具：`pip install uv`
- 或使用完整路径：`python -m uv`

### 权限错误
- 确保Augment有文件访问权限
- 在管理员模式下运行Augment

## 📞 技术支持

如遇问题，请检查：
1. Augment控制台日志
2. Obsidian插件状态
3. 网络连接状态
4. MCP配置文件格式

---

**配置完成后，你就可以在Augment IDE中通过AI助手直接操作Obsidian知识库了！** 🎉
