# MCPObsidian功能详解-20250621

## 基础功能使用

### 1. 文件浏览功能

#### 列出笔记库根目录
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_list_files_in_vault
// 参数
{"random_string":"null"}
```

#### 列出特定目录
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_list_files_in_dir
// 参数
{"dirpath": "目录路径"}
```

### 2. 读取笔记内容

#### 获取单个文件内容
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_get_file_contents
// 参数
{"filepath": "文件路径"}
```

#### 批量获取多个文件
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_batch_get_file_contents
// 参数
{"filepaths": ["文件路径1", "文件路径2", "文件路径3"]}
```

### 3. 搜索功能

#### 简单文本搜索
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_simple_search
// 参数
{
  "query": "搜索关键词",
  "context_length": 150  // 可选参数，设置返回上下文长度
}
```

#### 复杂条件搜索
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_complex_search
// 参数示例（查找所有markdown文件）
{
  "query": {"glob": ["*.md", {"var": "path"}]}
}
```

### 4. 编辑笔记

#### 添加内容到文件
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_append_content
// 参数
{
  "filepath": "文件路径", 
  "content": "要添加的内容"
}
```

#### 修改特定部分内容
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_patch_content
// 参数
{
  "filepath": "文件路径",
  "operation": "append/prepend/replace", // 操作类型
  "target_type": "heading/block/frontmatter", // 目标类型
  "target": "目标标识符", // 如标题路径、块引用或frontmatter字段
  "content": "新内容"
}
```

#### 删除文件
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_delete_file
// 参数
{
  "filepath": "文件路径", 
  "confirm": true // 确认删除
}
```

### 5. 周期性笔记

#### 获取当前周期笔记
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_get_periodic_note
// 参数
{
  "period": "daily/weekly/monthly/quarterly/yearly" // 周期类型
}
```

#### 获取近期周期笔记
```javascript
// 使用方法
mcp_mcp-obsidian_obsidian_get_recent_periodic_notes
// 参数
{
  "period": "daily/weekly/monthly/quarterly/yearly", // 周期类型
  "limit": 5, // 可选，默认5条
  "include_content": false // 可选，是否包含内容
}
```

## 高级功能使用案例

### 案例1：自动日记生成

```javascript
// 1. 获取今日日记文件
let todayNote = mcp_mcp-obsidian_obsidian_get_periodic_note({"period": "daily"});

// 2. 如果没有今日日记，则创建
if (!todayNote) {
  let today = new Date().toISOString().split('T')[0];
  mcp_mcp-obsidian_obsidian_append_content({
    "filepath": `日记/${today}.md`,
    "content": `# ${today} 日记\n\n## 今日目标\n\n- [ ] 待办事项1\n- [ ] 待办事项2\n\n## 笔记\n\n## 总结`
  });
}
```

### 案例2：项目知识库搜索整合

```javascript
// 1. 搜索与特定项目相关的所有笔记
let results = mcp_mcp-obsidian_obsidian_simple_search({
  "query": "项目名称",
  "context_length": 200
});

// 2. 提取相关文件路径
let relatedFiles = results.matches.map(match => match.file.path);

// 3. 批量获取内容
let contents = mcp_mcp-obsidian_obsidian_batch_get_file_contents({
  "filepaths": relatedFiles
});

// 4. 整合到项目汇总文档
mcp_mcp-obsidian_obsidian_append_content({
  "filepath": "项目/项目知识汇总.md",
  "content": `# 项目知识汇总\n\n${contents}`
});
```

### 案例3：笔记结构分析

```javascript
// 使用复杂搜索查找所有深度超过3级的标题结构
let complexQuery = {
  "query": {
    "some": [
      {"var": "headings"},
      {">=": [{"var": "level"}, 4]}
    ]
  }
};

let deepStructureNotes = mcp_mcp-obsidian_obsidian_complex_search(complexQuery);
```

## 故障排除

### 常见错误

1. **连接超时**
   - 确认Obsidian已启动
   - 检查本地API服务是否运行
   - 验证MCP插件配置是否正确

2. **文件路径错误**
   - 使用相对于vault根目录的路径
   - 注意区分大小写
   - 确认文件存在

3. **操作权限问题**
   - 检查Obsidian的文件系统权限
   - 确认API权限设置

## 最佳实践

1. **缓存常用数据**：避免频繁请求相同资源
2. **批量操作**：使用batch_get_file_contents代替多次单文件获取
3. **错误处理**：实施适当的错误捕获和重试机制
4. **路径规范化**：统一路径格式，避免混合使用相对和绝对路径 