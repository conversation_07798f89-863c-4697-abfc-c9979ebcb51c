# Obsidian MCP 修复工具套件

这是一套用于修复和配置 Obsidian MCP (Machine Communication Protocol) 与 Cursor IDE 集成的工具套件。它可以帮助您诊断和解决 Obsidian 与 Cursor IDE 通过 MCP 集成时的各种问题。

## 📋 主要问题和解决方案

Obsidian MCP 功能不工作的常见原因：

1. **环境变量问题**：MCP 服务需要正确的环境变量（API 密钥、主机、端口）
2. **配置文件位置不正确**：Cursor 需要在指定位置找到 MCP 配置文件
3. **mcp-obsidian 未正确安装**：依赖包未安装或版本不兼容
4. **Obsidian API 连接问题**：Obsidian Local REST API 插件未启用或配置错误
5. **服务启动问题**：asyncio 相关的问题导致服务无法正确启动

## 🛠 工具说明

本套件包含以下工具：

1. **一键修复Obsidian-MCP.bat**：主入口程序，提供所有功能的一键式访问
2. **obsidian_mcp_diagnostic.py**：诊断工具，检查环境、依赖和连接问题
3. **fix_cursor_mcp_config.py**：配置修复工具，自动修复 Cursor MCP 配置
4. **start_obsidian_mcp.bat**：简单的启动脚本，一键启动 MCP 服务
5. **Start-ObsidianMCP.ps1**：高级 PowerShell 启动脚本，提供更多选项和功能
6. **test_obsidian_mcp_tools.py**：测试工具，可以测试各种 Obsidian MCP 功能

## 🚀 快速开始

### 先决条件

- Python 3.8 或更高版本
- Obsidian 已安装并启用 Local REST API 插件
- Cursor IDE 已安装

### 使用指南

1. **一键式解决方案**：
   - 双击运行 `一键修复Obsidian-MCP.bat`
   - 按照菜单提示进行操作

2. **诊断问题**：
   - 运行 `python obsidian_mcp_diagnostic.py`
   - 查看检测结果和建议

3. **修复配置**：
   - 运行 `python fix_cursor_mcp_config.py --install`
   - 将自动安装 mcp-obsidian 并修复配置

4. **启动服务**：
   - 简单方式：运行 `start_obsidian_mcp.bat`
   - 高级方式：运行 `powershell -ExecutionPolicy Bypass -File .\Start-ObsidianMCP.ps1`

5. **测试功能**：
   - 运行 `python test_obsidian_mcp_tools.py`
   - 按照交互式菜单测试各种功能

## 📝 配置详情

### Obsidian API 设置

默认配置使用以下设置，你可以根据需要修改：

```
API 密钥: a712c31903d2c76f85bbf8acb44c4b5c6a497fbcd206557a9102175eebcef6b6
主机: 127.0.0.1
端口: 27124
```

### Cursor 配置文件位置

Cursor MCP 配置文件应位于以下位置：

- Windows: `%USERPROFILE%\.cursor\mcp-config.json`
- macOS: `~/Library/Application Support/cursor/mcp-config.json`
- Linux: `~/.config/cursor/mcp-config.json`

## ⚠️ 故障排除

如果遇到问题，请尝试以下步骤：

1. **运行诊断**：`python obsidian_mcp_diagnostic.py` 查看详细的错误报告
2. **检查 Obsidian**：确保 Obsidian 正在运行且 Local REST API 插件已启用
3. **检查 API 密钥**：验证 API 密钥是否与 Obsidian 中的设置匹配
4. **重启服务**：尝试使用不同的方法启动 MCP 服务
5. **重启 Cursor**：有时候重启 Cursor IDE 可以解决问题

常见错误：

- `ValueError: OBSIDIAN_API_KEY environment variable required`：环境变量未正确设置
- `Connection refused`：Obsidian API 连接失败，检查 Obsidian 是否运行
- `503 Service Unavailable`：MCP 服务未正确启动或注册

## 💡 高级使用

### 使用命令行参数

```
python fix_cursor_mcp_config.py --api-key YOUR_API_KEY --vault-path "C:\path\to\vault" --install
```

### 自定义 MCP 配置

您可以直接编辑 `mcp-config.json` 文件来自定义配置：

```json
{
  "mcpServers": {
    "mcp-obsidian": {
      "command": "uv",
      "args": ["tool", "run", "mcp-obsidian", "--vault-path", "C:\\path\\to\\vault"],
      "env": {
        "OBSIDIAN_API_KEY": "your_api_key",
        "OBSIDIAN_HOST": "127.0.0.1",
        "OBSIDIAN_PORT": "27124"
      }
    }
  }
}
```

## 📄 许可证

MIT

## 🙏 致谢

感谢 Cursor IDE 和 Obsidian 开发团队创造了这些优秀的工具。 