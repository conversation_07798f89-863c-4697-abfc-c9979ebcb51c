---
tags:
  - search-system
  - data-analysis
  - review-system
created: 2025-08-07
updated: 2025-08-07
---
# 🔍 复盘搜索系统

> 智能搜索和关联分析，让历史复盘数据发挥最大价值

## 🎯 快速搜索

### 🔍 关键词搜索
```dataview
TABLE without id
    "📅 " + file.link as "复盘记录",
    file.ctime as "创建时间",
    choice(contains(string(file), "成就"), "🎯", "") + 
    choice(contains(string(file), "挑战"), "🚧", "") + 
    choice(contains(string(file), "学习"), "💡", "") + 
    choice(contains(string(file), "感恩"), "🙏", "") as "内容类型"
FROM "0_Bullet Journal"
WHERE contains(string(file), this.search_keyword) AND 
      (contains(file.name, "Daily Log") OR contains(file.name, "Weekly Log") OR contains(file.name, "Monthly Log"))
SORT file.ctime DESC
LIMIT 20
```

**使用方法**: 在本页面的YAML front matter中添加 `search_keyword: "你要搜索的关键词"`

---
## 📊 主题分析

### 🏷️ 标签云分析
```dataviewjs
// 分析复盘内容中的高频词汇和主题
const reviewFiles = dv.pages('"0_Bullet Journal"')
    .where(p => p.file.name.includes("Daily Log") || 
                p.file.name.includes("Weekly Log") || 
                p.file.name.includes("Monthly Log"))
    .limit(50); // 限制分析范围，提升性能

// 定义主题关键词
const themes = {
    "工作": ["工作", "项目", "任务", "会议", "同事", "客户", "业务"],
    "学习": ["学习", "阅读", "课程", "技能", "知识", "书籍", "教程"],
    "健康": ["运动", "锻炼", "健身", "跑步", "瑜伽", "睡眠", "饮食"],
    "人际": ["朋友", "家人", "社交", "聚会", "沟通", "关系", "团队"],
    "情绪": ["开心", "快乐", "焦虑", "压力", "放松", "满足", "困扰"],
    "成长": ["反思", "改进", "目标", "计划", "习惯", "进步", "突破"]
};

let themeStats = {};
Object.keys(themes).forEach(theme => {
    themeStats[theme] = 0;
});

// 简化的主题统计（基于文件名和标签，避免全文搜索）
reviewFiles.forEach(file => {
    const fileName = file.file.name.toLowerCase();
    const tags = file.file.tags || [];
    
    Object.entries(themes).forEach(([theme, keywords]) => {
        keywords.forEach(keyword => {
            if (fileName.includes(keyword) || tags.some(tag => tag.includes(keyword))) {
                themeStats[theme]++;
            }
        });
    });
});

// 显示主题统计
dv.paragraph("**📊 复盘主题分布：**");
Object.entries(themeStats)
    .sort(([,a], [,b]) => b - a)
    .forEach(([theme, count]) => {
        if (count > 0) {
            const percentage = Math.round(count / reviewFiles.length * 100);
            const bar = "█".repeat(Math.floor(percentage / 10)) + "░".repeat(10 - Math.floor(percentage / 10));
            dv.paragraph(`${theme}: ${bar} ${count}次 (${percentage}%)`);
        }
    });
```

### 📈 时间线分析
```dataviewjs
// 复盘频率时间线分析
const now = new Date();
const last30Days = [];

for (let i = 29; i >= 0; i--) {
    const date = new Date(now);
    date.setDate(date.getDate() - i);
    const dateStr = date.toISOString().split('T')[0];
    
    // 检查该日期的复盘记录
    const dailyReviews = dv.pages('"0_Bullet Journal/Daily Notes"')
        .where(p => p.file.name.includes(dateStr))
        .length;
    
    last30Days.push({
        date: dateStr,
        count: dailyReviews,
        dayOfWeek: date.getDay()
    });
}

// 生成时间线可视化
dv.paragraph("**📈 最近30天复盘时间线：**");
const timeline = last30Days.map(day => {
    const dayName = ['日', '一', '二', '三', '四', '五', '六'][day.dayOfWeek];
    const symbol = day.count > 0 ? '✅' : '⏸️';
    return `${day.date.split('-')[2]}(${dayName}):${symbol}`;
}).join(' | ');

dv.paragraph(timeline);

// 统计分析
const totalDays = last30Days.length;
const reviewDays = last30Days.filter(day => day.count > 0).length;
const frequency = Math.round(reviewDays / totalDays * 100);

dv.paragraph(`**统计**: ${reviewDays}/${totalDays}天有复盘记录，频率${frequency}%`);
```

---
## 🔗 智能关联

### 📋 相关复盘推荐
```dataviewjs
// 基于当前页面内容推荐相关复盘
const currentFile = dv.current();
const currentContent = currentFile.file.name.toLowerCase();

// 定义关联规则
const associationRules = [
    {
        keywords: ["工作", "项目", "任务"],
        description: "工作相关复盘",
        emoji: "💼"
    },
    {
        keywords: ["学习", "阅读", "技能"],
        description: "学习成长复盘", 
        emoji: "📚"
    },
    {
        keywords: ["运动", "健康", "锻炼"],
        description: "健康生活复盘",
        emoji: "🏃"
    },
    {
        keywords: ["情绪", "心情", "感受"],
        description: "情绪管理复盘",
        emoji: "😊"
    }
];

// 查找相关复盘
let recommendations = [];

associationRules.forEach(rule => {
    const hasKeyword = rule.keywords.some(keyword => 
        currentContent.includes(keyword) || 
        (currentFile.search_keyword && currentFile.search_keyword.includes(keyword))
    );
    
    if (hasKeyword) {
        const relatedFiles = dv.pages('"0_Bullet Journal"')
            .where(p => {
                const fileName = p.file.name.toLowerCase();
                return rule.keywords.some(keyword => fileName.includes(keyword)) &&
                       p.file.path !== currentFile.file.path;
            })
            .sort(p => p.file.ctime, 'desc')
            .limit(3);
        
        if (relatedFiles.length > 0) {
            recommendations.push({
                rule: rule,
                files: relatedFiles
            });
        }
    }
});

// 显示推荐
if (recommendations.length > 0) {
    dv.paragraph("**🔗 相关复盘推荐：**");
    recommendations.forEach(rec => {
        dv.paragraph(`\n**${rec.rule.emoji} ${rec.rule.description}：**`);
        rec.files.forEach(file => {
            const date = file.file.ctime.toFormat('MM-dd');
            dv.paragraph(`- [[${file.file.path}|${date} ${file.file.name}]]`);
        });
    });
} else {
    dv.paragraph("*暂无相关复盘推荐，尝试添加搜索关键词或创建更多复盘记录*");
}
```

### 🎯 模式识别
```dataviewjs
// 识别复盘中的行为模式和趋势
const recentReviews = dv.pages('"0_Bullet Journal/Daily Notes"')
    .where(p => p.file.ctime >= dv.date('today') - dv.duration('30 days'))
    .sort(p => p.file.ctime, 'desc')
    .limit(20);

if (recentReviews.length >= 5) {
    // 分析模式
    const patterns = {
        "高频成就": { count: 0, description: "经常出现的成就类型" },
        "常见挑战": { count: 0, description: "反复遇到的挑战" },
        "学习主题": { count: 0, description: "持续关注的学习领域" },
        "情绪波动": { count: 0, description: "情绪变化的规律" }
    };
    
    // 简化的模式识别（基于文件元数据）
    recentReviews.forEach(review => {
        const tags = review.file.tags || [];
        const fileName = review.file.name.toLowerCase();
        
        // 基于标签和文件名进行简单的模式识别
        if (tags.includes('achievement') || fileName.includes('成就')) {
            patterns["高频成就"].count++;
        }
        if (tags.includes('challenge') || fileName.includes('挑战')) {
            patterns["常见挑战"].count++;
        }
        if (tags.includes('learning') || fileName.includes('学习')) {
            patterns["学习主题"].count++;
        }
        if (review.mood && review.mood < 6) {
            patterns["情绪波动"].count++;
        }
    });
    
    dv.paragraph("**🎯 识别到的模式：**");
    Object.entries(patterns).forEach(([pattern, data]) => {
        if (data.count > 0) {
            const frequency = Math.round(data.count / recentReviews.length * 100);
            dv.paragraph(`- **${pattern}**: ${data.count}次出现 (${frequency}%) - ${data.description}`);
        }
    });
    
    // 提供改进建议
    dv.paragraph("\n**💡 改进建议：**");
    if (patterns["常见挑战"].count > patterns["高频成就"].count) {
        dv.paragraph("- 考虑制定策略来应对反复出现的挑战");
    }
    if (patterns["学习主题"].count > 5) {
        dv.paragraph("- 您在学习方面很积极，可以考虑总结学习方法");
    }
    if (patterns["情绪波动"].count > 3) {
        dv.paragraph("- 注意情绪管理，可以分析情绪波动的原因");
    }
} else {
    dv.paragraph("*需要更多复盘数据来进行模式识别，建议至少积累5次复盘记录*");
}
```

---
## 🔍 高级搜索

### 📅 按时间范围搜索
```dataview
TABLE without id
    "📅 " + file.link as "复盘记录",
    file.ctime as "创建时间",
    choice(file.review_level = "beginner", "🌱", 
           file.review_level = "standard", "🌿", 
           file.review_level = "advanced", "🌳", "📝") as "级别"
FROM "0_Bullet Journal"
WHERE file.ctime >= date(2025-08-01) AND file.ctime <= date(2025-08-31)
    AND (contains(file.name, "Daily Log") OR contains(file.name, "Weekly Log") OR contains(file.name, "Monthly Log"))
SORT file.ctime DESC
```

### 🏷️ 按标签分类搜索
```dataview
TABLE without id
    "📅 " + file.link as "复盘记录",
    file.tags as "标签",
    file.ctime as "创建时间"
FROM "0_Bullet Journal"
WHERE contains(file.tags, "beginner-level") OR contains(file.tags, "standard-level") OR contains(file.tags, "daily-review")
SORT file.ctime DESC
LIMIT 15
```

### 📊 按复盘级别搜索
```dataviewjs
// 按复盘级别分组显示
const reviewLevels = {
    "beginner": { name: "🌱 入门级", files: [] },
    "standard": { name: "🌿 标准级", files: [] },
    "advanced": { name: "🌳 深度级", files: [] }
};

const allReviews = dv.pages('"0_Bullet Journal"')
    .where(p => p.review_type === 'daily')
    .sort(p => p.file.ctime, 'desc')
    .limit(30);

allReviews.forEach(review => {
    const level = review.review_level || "advanced";
    if (reviewLevels[level]) {
        reviewLevels[level].files.push(review);
    }
});

Object.entries(reviewLevels).forEach(([level, data]) => {
    if (data.files.length > 0) {
        dv.paragraph(`\n**${data.name} (${data.files.length}篇)：**`);
        data.files.slice(0, 5).forEach(file => {
            const date = file.file.ctime.toFormat('MM-dd');
            dv.paragraph(`- [[${file.file.path}|${date} ${file.file.name}]]`);
        });
        if (data.files.length > 5) {
            dv.paragraph(`  *...还有${data.files.length - 5}篇记录*`);
        }
    }
});
```

---
## 📈 数据可视化

### 📊 复盘统计图表
```dataviewjs
// 生成复盘数据的统计图表
const stats = {
    daily: dv.pages('#daily-review').length,
    weekly: dv.pages('"0_Bullet Journal/Weekly Notes"').length,
    monthly: dv.pages('"0_Bullet Journal/Monthly Notes"').length,
    beginner: dv.pages('#beginner-level').length,
    standard: dv.pages('#standard-level').length
};

dv.paragraph("**📊 复盘数据统计：**");
dv.table(
    ["类型", "数量", "占比", "可视化"],
    [
        ["📅 日复盘", stats.daily, `${Math.round(stats.daily/(stats.daily+stats.weekly+stats.monthly)*100)}%`, "█".repeat(Math.floor(stats.daily/10))],
        ["📊 周复盘", stats.weekly, `${Math.round(stats.weekly/(stats.daily+stats.weekly+stats.monthly)*100)}%`, "█".repeat(Math.floor(stats.weekly/2))],
        ["📈 月复盘", stats.monthly, `${Math.round(stats.monthly/(stats.daily+stats.weekly+stats.monthly)*100)}%`, "█".repeat(stats.monthly)],
        ["🌱 入门级", stats.beginner, `${Math.round(stats.beginner/(stats.beginner+stats.standard)*100)}%`, "█".repeat(Math.floor(stats.beginner/5))],
        ["🌿 标准级", stats.standard, `${Math.round(stats.standard/(stats.beginner+stats.standard)*100)}%`, "█".repeat(Math.floor(stats.standard/5))]
    ]
);
```

---
## 🛠️ 搜索工具

### 🔧 自定义搜索
**使用方法**：
1. 在本页面的YAML front matter中添加搜索参数
2. 刷新页面查看搜索结果

**可用参数**：
- `search_keyword: "关键词"` - 全文搜索
- `search_date_start: "2025-08-01"` - 开始日期
- `search_date_end: "2025-08-31"` - 结束日期
- `search_level: "beginner"` - 复盘级别
- `search_tags: ["tag1", "tag2"]` - 标签搜索

### 📋 搜索历史
```dataview
LIST
FROM "复盘系统"
WHERE contains(file.name, "搜索") OR contains(string(file), "search_keyword")
SORT file.mtime DESC
LIMIT 5
```

---
## 💡 使用技巧

### 🎯 搜索优化建议
1. **使用具体关键词**：如"项目管理"而不是"工作"
2. **组合搜索条件**：时间范围+关键词+标签
3. **定期整理标签**：保持标签体系的一致性
4. **利用模式识别**：关注反复出现的主题

### 🔍 高效搜索流程
1. **明确搜索目标** - 想要找什么类型的复盘？
2. **选择搜索方式** - 关键词、时间、标签或组合搜索
3. **分析搜索结果** - 查看相关推荐和模式识别
4. **应用搜索洞察** - 将发现的模式应用到未来的复盘中

---
## 🔗 相关功能

### 📊 数据分析
- [[复盘系统/复盘数据仪表盘|📊 复盘数据仪表盘]] - 综合数据统计
- [[复盘系统/复盘引导中心|🎯 复盘引导中心]] - 系统导航

### 🚀 快速操作
- [[复盘系统/复盘快速启动器|🚀 复盘快速启动器]] - 创建新复盘
- [[复盘系统/快速入口使用指南|📖 使用指南]] - 操作说明

---
*🔍 复盘搜索系统 v1.0 | 让历史复盘数据发挥最大价值*
