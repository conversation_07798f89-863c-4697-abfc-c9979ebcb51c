---
tags:
  - type/dashboard
  - dashboard/universal-search-opus
  - obsidian/search
created: 2025-07-11T16:30
updated: 2025-07-12T12:24
---

# 🌍 通用笔记搜索系统 opus版

```dataviewjs
// 通用笔记搜索系统 opus版 - 专业级智能搜索引擎
const container = this.container;

// ===== 全局状态管理 =====
let searchState = {
    isSearching: false,
    currentSearch: null,
    searchAbortController: null,
    searchCache: new Map(),
    wordIndex: new Map(),
    fileStats: new Map()
};

// ===== 增强配置区域 =====
const CONFIG = {
    // 排除的目录（不参与搜索）
    excludeDirs: ['.obsidian', 'Templates', '.trash'],
    
    // 手动指定的重要目录
    manualDirs: [],
    
    // 搜索历史保存的键名
    historyKey: 'obsidian-search-history-opus-' + (dv.current()?.file?.path?.split('/')[0] || 'default'),

    // 排除设置保存的键名
    excludeSettingsKey: 'obsidian-search-exclude-opus-' + (dv.current()?.file?.path?.split('/')[0] || 'default'),
    
    // 性能配置
    performance: {
        batchSize: 50,           // 每批处理文件数
        maxCacheSize: 1000,      // 最大缓存文件数
        searchDelay: 300,        // 搜索防抖延迟(ms)
        previewDelay: 500        // 预览显示延迟(ms)
    },
    
    // 文件类型支持
    fileTypes: {
        'markdown': ['.md', '.markdown'],
        'image': ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'],
        'document': ['.pdf', '.doc', '.docx', '.txt', '.rtf'],
        'media': ['.mp4', '.mp3', '.avi', '.mov', '.wav', '.flac'],
        'code': ['.js', '.py', '.css', '.html', '.json', '.xml', '.yaml'],
        'other': []
    },
    
    // 搜索配置
    search: {
        maxHistory: 20,
        maxSuggestions: 10,
        contextLines: 2,         // 上下文行数
        maxSnippetLength: 200,   // 最大片段长度
        fuzzyThreshold: 0.7      // 模糊匹配阈值
    }
};

// 创建主容器
const mainDiv = document.createElement('div');
mainDiv.style.cssText = `
    font-family: var(--font-interface);
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
`;

// 搜索区域
const searchDiv = document.createElement('div');
searchDiv.style.cssText = `
    background: var(--background-secondary);
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid var(--background-modifier-border);
`;

// 标题
const title = document.createElement('h3');
title.textContent = '🌍 通用笔记搜索系统 opus版';
title.style.cssText = 'margin: 0 0 15px 0; color: var(--text-normal);';

// 搜索选项区域（移到输入框上方）
const optionsDiv = document.createElement('div');
optionsDiv.style.cssText = 'display: flex; gap: 12px; margin-bottom: 15px; flex-wrap: wrap; align-items: center; width: 100%;';

// 输入区域
const inputDiv = document.createElement('div');
inputDiv.style.cssText = 'margin-bottom: 15px;';

// 搜索输入框容器（支持建议下拉）
const searchInputContainer = document.createElement('div');
searchInputContainer.style.cssText = 'position: relative; width: 100%;';

// 搜索输入框
const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.placeholder = '专业级智能搜索引擎...';
searchInput.style.cssText = `
    width: 100%;
    padding: 10px 14px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--text-normal);
    box-sizing: border-box;
    font-size: 14px;
`;

// 搜索建议下拉框
const suggestionsDiv = document.createElement('div');
suggestionsDiv.style.cssText = `
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
`;

searchInputContainer.appendChild(searchInput);
searchInputContainer.appendChild(suggestionsDiv);

// 优化的选择框样式
const selectStyle = `
    /* 统一选择框基础样式，增强可读性与响应性 */
    padding: 6px 14px; /* 调整垂直内边距，避免文字被边框遮挡 */
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-size: 14px;
    line-height: 1.2; /* 降低行高，确保文本垂直居中显示 */
    box-sizing: border-box;
    min-width: 140px;
    max-width: 100%;
    cursor: pointer;
    /* 自适应布局：在 flex 容器中按照最小 140px 伸缩 */
    flex: 1 1 140px;
`;

// 1. 目录过滤选择框（第一个）
const dirSelect = document.createElement('select');
dirSelect.style.cssText = selectStyle;

// 2. 搜索范围选择框（第二个）
const scopeSelect = document.createElement('select');
scopeSelect.style.cssText = selectStyle;

const scopes = [
    { value: 'all', text: '全部内容' },
    { value: 'filename', text: '仅文件名' },
    { value: 'content', text: '仅文件内容' }
];

scopes.forEach(scope => {
    const option = document.createElement('option');
    option.value = scope.value;
    option.textContent = scope.text;
    scopeSelect.appendChild(option);
});

// 3. 排序方式选择框（第三个）
const sortSelect = document.createElement('select');
sortSelect.style.cssText = selectStyle;

const sortOptions = [
    { value: 'relevance', text: '按相关性' },
    { value: 'modified', text: '按修改时间' },
    { value: 'created', text: '按创建时间' },
    { value: 'size', text: '按文件大小' },
    { value: 'name', text: '按文件名' }
];

sortOptions.forEach(sort => {
    const option = document.createElement('option');
    option.value = sort.value;
    option.textContent = sort.text;
    sortSelect.appendChild(option);
});

// 4. 文件类型过滤选择框（第四个）
const fileTypeSelect = document.createElement('select');
fileTypeSelect.style.cssText = selectStyle;

const fileTypes = [
    { value: 'all', text: '所有类型' },
    { value: 'markdown', text: 'Markdown' },
    { value: 'image', text: '图片' },
    { value: 'document', text: '文档' },
    { value: 'media', text: '媒体' },
    { value: 'code', text: '代码' }
];

fileTypes.forEach(type => {
    const option = document.createElement('option');
    option.value = type.value;
    option.textContent = type.text;
    fileTypeSelect.appendChild(option);
});

// 5. 搜索模式选择框（第五个）
const modeSelect = document.createElement('select');
modeSelect.style.cssText = selectStyle;

const modes = [
    { value: 'OR', text: 'OR (任一)' },
    { value: 'AND', text: 'AND (所有)' },
    { value: 'EXACT', text: '精确匹配' }
];

modes.forEach(mode => {
    const option = document.createElement('option');
    option.value = mode.value;
    option.textContent = mode.text;
    modeSelect.appendChild(option);
});

// 6. 排除目录选择框（第六个）
const excludeDirSelect = document.createElement('select');
excludeDirSelect.style.cssText = selectStyle;
excludeDirSelect.title = '选择要排除的系统目录';

const excludeDirOptions = [
    { value: 'none', text: '不排除系统目录' },
    { value: 'basic', text: '排除基础系统目录' },
    { value: 'full', text: '排除所有系统目录' }
];

excludeDirOptions.forEach(option => {
    const opt = document.createElement('option');
    opt.value = option.value;
    opt.textContent = option.text;
    excludeDirSelect.appendChild(opt);
});

// 默认选择基础排除
excludeDirSelect.value = 'basic';

// 7. 自定义排除输入框
const customExcludeInput = document.createElement('input');
customExcludeInput.type = 'text';
customExcludeInput.placeholder = '自定义排除目录 (用逗号分隔)';
customExcludeInput.title = '输入要排除的目录名称，用逗号分隔多个目录';
customExcludeInput.style.cssText = `
    padding: 6px 14px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-size: 14px;
    line-height: 1.2;
    box-sizing: border-box;
    min-width: 200px;
    max-width: 100%;
    flex: 1 1 200px;
`;

// 按钮区域
const buttonDiv = document.createElement('div');
buttonDiv.style.cssText = 'text-align: center; margin-top: 15px;';

const searchBtn = document.createElement('button');
searchBtn.textContent = '🔍 搜索';
searchBtn.style.cssText = `
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
`;

const clearBtn = document.createElement('button');
clearBtn.textContent = '🗑️ 清空结果';
clearBtn.style.cssText = `
    background: var(--background-modifier-border);
    color: var(--text-normal);
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
`;

const historyBtn = document.createElement('button');
historyBtn.textContent = '📚 搜索历史';
historyBtn.style.cssText = `
    background: var(--color-blue);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 10px;
`;

const refreshBtn = document.createElement('button');
refreshBtn.textContent = '🔄 刷新目录';
refreshBtn.style.cssText = `
    background: var(--color-green);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
`;

const cancelBtn = document.createElement('button');
cancelBtn.textContent = '⏹️ 取消搜索';
cancelBtn.style.cssText = `
    background: var(--color-red);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    display: none;
`;

// 汇总导出按钮（只有在有搜索结果时才显示）
const exportBtn = document.createElement('button');
exportBtn.textContent = '📄 生成汇总';
exportBtn.title = '将当前搜索结果导出为汇总文档';
exportBtn.style.cssText = `
    background: var(--color-purple);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 10px;
    display: none;
`;



// 进度显示区域
const progressContainer = document.createElement('div');
progressContainer.style.cssText = 'margin-top: 15px; display: none;';

const progressBar = document.createElement('div');
progressBar.style.cssText = `
    width: 100%;
    height: 6px;
    background: var(--background-modifier-border);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 5px;
`;

const progressFill = document.createElement('div');
progressFill.style.cssText = `
    height: 100%;
    background: var(--interactive-accent);
    width: 0%;
    transition: width 0.3s ease;
`;

const progressText = document.createElement('div');
progressText.style.cssText = 'font-size: 12px; color: var(--text-muted); text-align: center;';

progressBar.appendChild(progressFill);
progressContainer.appendChild(progressBar);
progressContainer.appendChild(progressText);

// 状态显示
const statusDiv = document.createElement('div');
statusDiv.style.cssText = 'font-size: 12px; color: var(--text-muted); margin-top: 10px; text-align: center;';
statusDiv.textContent = '高级搜索系统准备就绪...';

// 组装搜索选项区域（按新的顺序）
optionsDiv.appendChild(dirSelect);      // 1. 目录过滤
optionsDiv.appendChild(scopeSelect);    // 2. 搜索范围
optionsDiv.appendChild(sortSelect);     // 3. 排序方式
optionsDiv.appendChild(fileTypeSelect); // 4. 文件类型
optionsDiv.appendChild(modeSelect);     // 5. 搜索模式
optionsDiv.appendChild(excludeDirSelect); // 6. 排除目录
optionsDiv.appendChild(customExcludeInput); // 7. 自定义排除

// 组装输入区域
inputDiv.appendChild(searchInputContainer);

// 组装按钮区域
buttonDiv.appendChild(searchBtn);
buttonDiv.appendChild(clearBtn);
buttonDiv.appendChild(historyBtn);
buttonDiv.appendChild(refreshBtn);
buttonDiv.appendChild(cancelBtn);
buttonDiv.appendChild(exportBtn);

// 组装搜索区域（选项在上，输入框在下）
searchDiv.appendChild(title);
searchDiv.appendChild(optionsDiv);      // 选项区域在上方
searchDiv.appendChild(inputDiv);        // 输入区域在下方
searchDiv.appendChild(buttonDiv);
searchDiv.appendChild(progressContainer);
searchDiv.appendChild(statusDiv);

// 结果区域
const resultDiv = document.createElement('div');
resultDiv.style.cssText = `
    border: 1px solid var(--background-modifier-border);
    border-radius: 8px;
    padding: 20px;
    background: var(--background-primary);
    min-height: 200px;
`;

const resultTitle = document.createElement('h3');
resultTitle.textContent = '📋 搜索结果';
resultTitle.style.cssText = 'margin: 0 0 15px 0; color: var(--text-normal);';

const resultContent = document.createElement('div');
resultContent.innerHTML = `
    <div style="text-align: center; color: var(--text-muted); padding: 40px;">
        <div style="font-size: 48px; margin-bottom: 10px;">🌍</div>
        <div style="font-size: 18px;">专业级智能搜索引擎准备就绪</div>
    </div>
`;

resultDiv.appendChild(resultTitle);
resultDiv.appendChild(resultContent);

// 组装主界面
mainDiv.appendChild(searchDiv);
mainDiv.appendChild(resultDiv);
container.appendChild(mainDiv);

// ===== 核心增强功能函数 =====

// 排除设置管理
let excludeSettings = JSON.parse(localStorage.getItem(CONFIG.excludeSettingsKey) || '{"systemLevel": "basic", "customDirs": ""}');

function saveExcludeSettings() {
    excludeSettings.systemLevel = excludeDirSelect.value;
    excludeSettings.customDirs = customExcludeInput.value;
    localStorage.setItem(CONFIG.excludeSettingsKey, JSON.stringify(excludeSettings));
}

function loadExcludeSettings() {
    excludeDirSelect.value = excludeSettings.systemLevel || 'basic';
    customExcludeInput.value = excludeSettings.customDirs || '';
}

function getExcludedDirectories() {
    const excluded = [];

    // 系统目录排除
    const systemLevel = excludeDirSelect.value;
    if (systemLevel === 'basic') {
        excluded.push('.obsidian', 'Templates', '.trash');
    } else if (systemLevel === 'full') {
        excluded.push('.obsidian', 'Templates', '.trash', '.git', 'node_modules', '.vscode', 'Attachments', 'Archive');
    }

    // 自定义目录排除
    const customDirs = customExcludeInput.value.trim();
    if (customDirs) {
        const customList = customDirs.split(',').map(dir => dir.trim()).filter(dir => dir);
        excluded.push(...customList);
    }

    return excluded;
}

function isFileExcluded(filePath) {
    const excludedDirs = getExcludedDirectories();
    return excludedDirs.some(dir => filePath.includes(dir));
}

// 【关键修复】安全的文件打开函数 - 在新标签页中打开（移植自MVP优化版）
function safeOpenFile(filePath) {
    try {
        if (!app || !app.workspace) {
            throw new Error('Obsidian app 不可用');
        }

        // 方法1：使用getLeaf('tab')在新标签页中打开
        if (app.vault) {
            const file = app.vault.getAbstractFileByPath(filePath);
            if (file) {
                // 创建新的标签页
                const newLeaf = app.workspace.getLeaf('tab');
                newLeaf.openFile(file);

                // 确保新标签页获得焦点
                app.workspace.setActiveLeaf(newLeaf);
                return;
            }
        }

        // 方法2：使用splitActiveLeaf创建新标签页
        if (app.workspace.splitActiveLeaf && app.vault) {
            const file = app.vault.getAbstractFileByPath(filePath);
            if (file) {
                const newLeaf = app.workspace.splitActiveLeaf();
                newLeaf.openFile(file);
                return;
            }
        }

        // 方法3：使用openLinkText在新标签页中打开
        if (app.workspace.openLinkText) {
            // 先创建新标签页，然后在其中打开文件
            const newLeaf = app.workspace.getLeaf('tab');
            app.workspace.setActiveLeaf(newLeaf);
            app.workspace.openLinkText(filePath, '', false);
            return;
        }

        // 方法4：降级处理 - 使用Obsidian URI在新窗口打开
        if (app.vault && app.vault.getName) {
            const vaultName = app.vault.getName();
            const obsidianUri = `obsidian://open?vault=${encodeURIComponent(vaultName)}&file=${encodeURIComponent(filePath)}`;

            // 在新窗口中打开
            window.open(obsidianUri, '_blank');
            return;
        }

        throw new Error('所有打开方法都失败');

    } catch (error) {
        console.error('打开文件时出错:', error);

        // 用户友好的错误提示
        const fileName = filePath.split('/').pop();
        const message = `无法在新标签页中打开文件 "${fileName}"。\n\n文件路径：${filePath}\n\n请尝试手动在Obsidian中打开此文件。`;

        // 显示更详细的错误信息
        if (confirm(message + '\n\n点击"确定"复制文件路径到剪贴板')) {
            // 尝试复制路径到剪贴板
            try {
                navigator.clipboard.writeText(filePath);
            } catch (clipError) {
                console.error('无法复制到剪贴板:', clipError);
            }
        }
    }
}

// 相关性评分算法（基于TF-IDF）
function calculateRelevanceScore(content, fileName, keywords, fileStats) {
    let score = 0;
    const lowerContent = content.toLowerCase();
    const lowerFileName = fileName.toLowerCase();

    keywords.forEach(keyword => {
        const lowerKeyword = keyword.toLowerCase();

        // 文件名匹配权重更高（10倍）
        if (lowerFileName.includes(lowerKeyword)) {
            score += 100;
        }

        // 计算TF（词频）
        const matches = (lowerContent.match(new RegExp(lowerKeyword, 'g')) || []);
        const tf = matches.length;

        if (tf > 0) {
            // 位置权重：出现在开头的权重更高
            const firstPosition = lowerContent.indexOf(lowerKeyword);
            const positionWeight = firstPosition < 200 ? 2 : 1;

            // 长度权重：较短的文档中出现关键词权重更高
            const lengthWeight = Math.max(1, Math.log(1000 / Math.max(content.length, 100)));

            // 计算最终分数
            score += tf * positionWeight * lengthWeight * 10;
        }
    });

    return score;
}

// 文件类型检测
function getFileType(filePath) {
    const extension = '.' + filePath.split('.').pop().toLowerCase();

    for (const [type, extensions] of Object.entries(CONFIG.fileTypes)) {
        if (extensions.includes(extension)) {
            return type;
        }
    }
    return 'other';
}

// 智能上下文提取
function getEnhancedSnippet(content, keywords) {
    const lines = content.split('\n');
    const contextLines = CONFIG.search.contextLines;
    const maxLength = CONFIG.search.maxSnippetLength;
    const matchedSections = [];

    lines.forEach((line, index) => {
        const lowerLine = line.toLowerCase();
        const hasMatch = keywords.some(k => lowerLine.includes(k.toLowerCase()));

        if (hasMatch) {
            const start = Math.max(0, index - contextLines);
            const end = Math.min(lines.length, index + contextLines + 1);

            const contextSection = lines.slice(start, end);
            const highlightedSection = contextSection.map((contextLine, contextIndex) => {
                const actualIndex = start + contextIndex;
                const isMatchLine = actualIndex === index;

                let processedLine = contextLine;
                if (isMatchLine) {
                    // 高亮匹配的关键词
                    keywords.forEach(keyword => {
                        const regex = new RegExp(`(${keyword})`, 'gi');
                        processedLine = processedLine.replace(regex, '**$1**');
                    });
                }

                return {
                    content: processedLine,
                    lineNumber: actualIndex + 1,
                    isMatch: isMatchLine
                };
            });

            matchedSections.push({
                lines: highlightedSection,
                startLine: start + 1,
                endLine: end,
                matchLine: index + 1
            });
        }
    });

    // 合并相邻的匹配段落
    const mergedSections = [];
    matchedSections.forEach(section => {
        const lastSection = mergedSections[mergedSections.length - 1];
        if (lastSection && section.startLine <= lastSection.endLine + 2) {
            // 合并相邻段落
            lastSection.endLine = section.endLine;
            lastSection.lines = lastSection.lines.concat(section.lines);
        } else {
            mergedSections.push(section);
        }
    });

    return mergedSections.slice(0, 3); // 最多返回3个匹配段落
}

// 自动检测目录结构
async function detectDirectories() {
    try {
        const allPages = dv.pages();
        const directories = new Set();
        let totalFiles = 0;

        allPages.forEach(page => {
            totalFiles++;
            const pathParts = page.file.path.split('/');
            if (pathParts.length > 1) {
                const topDir = pathParts[0];
                // 排除配置中指定的目录
                if (!CONFIG.excludeDirs.some(exclude => topDir.includes(exclude))) {
                    directories.add(topDir);
                }
            }
        });

        let detectedDirs = Array.from(directories).sort();

        // 如果有手动配置的目录，优先使用
        if (CONFIG.manualDirs.length > 0) {
            detectedDirs = CONFIG.manualDirs;
        }

        statusDiv.textContent = `检测到 ${detectedDirs.length} 个目录，共 ${totalFiles} 个文件 - opus版智能搜索`;

        return detectedDirs;
    } catch (error) {
        console.error('检测目录失败:', error);
        statusDiv.textContent = '目录检测失败，使用默认配置';
        return ['notes', 'docs'];
    }
}

// 初始化目录选择器
async function initDirectorySelect() {
    // 清空现有选项
    dirSelect.innerHTML = '';

    // 添加"全部目录"选项
    const allOption = document.createElement('option');
    allOption.value = 'all';
    allOption.textContent = '全部目录';
    dirSelect.appendChild(allOption);

    // 添加检测到的目录
    const detectedDirs = await detectDirectories();
    detectedDirs.forEach(dir => {
        const option = document.createElement('option');
        option.value = dir;
        option.textContent = dir;
        dirSelect.appendChild(option);
    });

    // 如果没有检测到目录，添加提示
    if (detectedDirs.length === 0) {
        const noOption = document.createElement('option');
        noOption.value = 'none';
        noOption.textContent = '未检测到子目录';
        noOption.disabled = true;
        dirSelect.appendChild(noOption);
    }
}

// 搜索历史管理
let searchHistory = JSON.parse(localStorage.getItem(CONFIG.historyKey) || '[]');

function addToHistory(keyword, mode, dirFilter, searchScope, fileTypeFilter, sortBy, resultCount) {
    const historyItem = {
        keyword,
        mode,
        dirFilter,
        searchScope,
        fileTypeFilter,
        sortBy,
        resultCount,
        timestamp: new Date().toLocaleString('zh-CN')
    };

    // 避免重复
    searchHistory = searchHistory.filter(item =>
        !(item.keyword === keyword && item.mode === mode && item.dirFilter === dirFilter && item.searchScope === searchScope)
    );

    searchHistory.unshift(historyItem);
    searchHistory = searchHistory.slice(0, CONFIG.search.maxHistory);
    localStorage.setItem(CONFIG.historyKey, JSON.stringify(searchHistory));
}

// 批处理函数
async function processBatch(batch, keywords, mode, searchScope, results) {
    for (const page of batch) {
        try {
            // 检查文件是否被排除
            if (isFileExcluded(page.file.path)) {
                continue;
            }

            const fileName = page.file.name.replace(/\.[^/.]+$/, ''); // 移除扩展名
            let content = '';
            let matched = false;
            let matchType = '';
            let snippet = '';
            let relevanceScore = 0;

            // 根据搜索范围决定是否需要读取文件内容
            if (searchScope === 'all' || searchScope === 'content') {
                content = await dv.io.load(page.file.path);
            }

            // 根据搜索范围进行匹配
            if (searchScope === 'all' || searchScope === 'filename') {
                if (isEnhancedMatch(fileName, keywords, mode)) {
                    matched = true;
                    matchType = '文件名';
                    snippet = fileName;
                    relevanceScore += 100; // 文件名匹配给予高分
                }
            }

            if (!matched && (searchScope === 'all' || searchScope === 'content')) {
                if (isEnhancedMatch(content, keywords, mode)) {
                    matched = true;
                    matchType = '文件内容';
                    const enhancedSnippets = getEnhancedSnippet(content, keywords);
                    snippet = enhancedSnippets;
                }
            }

            if (matched) {
                // 计算相关性分数
                const fullContent = content || fileName;
                relevanceScore += calculateRelevanceScore(fullContent, fileName, keywords);

                results.push({
                    name: fileName,
                    path: page.file.path,
                    link: page.file.link,
                    matchType: matchType,
                    snippet: snippet,
                    mtime: page.file.mtime,
                    ctime: page.file.ctime,
                    size: page.file.size,
                    relevanceScore: relevanceScore,
                    fileType: getFileType(page.file.path)
                });
            }
        } catch (error) {
            // 跳过无法读取的文件
            console.warn(`无法处理文件: ${page.file.path}`, error);
        }
    }
}

// 进度管理函数
function showSearchProgress() {
    progressContainer.style.display = 'block';
    searchBtn.style.display = 'none';
    cancelBtn.style.display = 'inline-block';
    progressFill.style.width = '0%';
    progressText.textContent = '准备搜索...';
}

function updateSearchProgress(processed, total) {
    const percentage = Math.round((processed / total) * 100);
    progressFill.style.width = percentage + '%';
    progressText.textContent = `正在搜索... ${processed}/${total} (${percentage}%)`;
}

function hideSearchProgress() {
    progressContainer.style.display = 'none';
    searchBtn.style.display = 'inline-block';
    cancelBtn.style.display = 'none';
}

function cancelCurrentSearch() {
    if (searchState.searchAbortController) {
        searchState.searchAbortController.abort();
    }
    searchState.isSearching = false;
    hideSearchProgress();
}

// 汇总导出功能
async function exportSearchSummary() {
    if (!searchState.currentSearch || !searchState.currentSearch.results || searchState.currentSearch.results.length === 0) {
        showUserMessage('没有搜索结果可以导出', 'warning');
        return;
    }

    showUserMessage('正在生成汇总...', 'info');

    const { results, keywords, settings } = searchState.currentSearch;
    const timestamp = new Date();
    const dateStr = timestamp.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = timestamp.toTimeString().slice(0, 8).replace(/:/g, '');
    const keywordStr = keywords.join('-').replace(/[^\w\u4e00-\u9fa5]/g, '').slice(0, 20);
    const finalFileName = `搜索汇总-${keywordStr}-${dateStr}-${timeStr}.md`;

    let content;

    try {
        // 生成汇总内容
        content = `# ${keywords.join('、')}搜索结果汇总\n\n`;
        content += `> 生成时间: ${timestamp.toLocaleString('zh-CN')}\n\n`;

        // 搜索条件
        content += `## 📋 搜索条件\n\n`;
        content += `- 关键词: ${keywords.join(', ')}\n`;
        content += `- 搜索模式: ${settings?.mode || 'OR'}\n`;
        content += `- 搜索范围: ${settings?.searchScope || 'all'}\n`;
        content += `- 排序方式: ${settings?.sortBy || 'relevance'}\n`;

        // 排除设置
        const excludedDirs = getExcludedDirectories();
        if (excludedDirs.length > 0) {
            content += `- 排除目录: ${excludedDirs.join(', ')}\n`;
        }

        // 统计信息
        content += `\n## 📊 统计信息\n\n`;
        content += `- 结果总数: ${results.length} 个文件\n`;

        // 文件类型统计
        const fileTypes = {};
        results.forEach(r => {
            const type = getFileType(r.path || '');
            fileTypes[type] = (fileTypes[type] || 0) + 1;
        });
        content += `- 文件类型分布: ${Object.entries(fileTypes).map(([type, count]) => `${type}(${count})`).join(', ')}\n`;

        // 搜索结果列表
        content += `\n## 📝 搜索结果\n\n`;

        // 生成双链格式的结果列表
        results.forEach((result, index) => {
            const fileName = result.name || (result.path ? result.path.split('/').pop().replace('.md', '') : `文件${index + 1}`);
            const filePath = result.path || result.name || fileName;

            content += `${index + 1}. [[${filePath}|${fileName}]]`;

            if (result.matchType && result.matchType !== 'content') {
                content += ` (${result.matchType})`;
            }

            if (result.relevanceScore && result.relevanceScore > 0) {
                content += ` - ${result.relevanceScore.toFixed(1)}分`;
            }

            content += `\n`;
        });

        // 添加关联标签
        if (keywords.length > 0) {
            content += `\n---\n\n`;
            content += `关联: ${keywords.map(k => `[[${k}]]`).join(', ')}\n`;
        }

    } catch (contentError) {
        content = `# 搜索结果汇总\n\n`;
        content += `> 生成时间：${timestamp.toLocaleString('zh-CN')}\n\n`;
        content += `⚠️ 生成详细内容时出现错误：${contentError.message}\n\n`;
        content += `## 基础信息\n\n`;
        content += `- 搜索关键词：${keywords.join(', ')}\n`;
        content += `- 结果数量：${results.length}\n\n`;
    }

    try {
        // 创建新页面并在新标签页中打开
        const newFile = await app.vault.create(finalFileName, content);
        const leaf = app.workspace.getLeaf('tab');
        await leaf.openFile(newFile);
        showUserMessage(`汇总已导出到: ${finalFileName}`, 'success');

        // 询问是否移动到专门的文件夹
        setTimeout(() => {
            if (confirm('是否将汇总文件移动到 search-results 文件夹中？')) {
                moveToSearchResultsFolder(newFile, finalFileName);
            }
        }, 2000);

    } catch (obsidianError) {
        // 降级处理：创建临时文件
        try {
            const tempFileName = `临时汇总-${Date.now()}.md`;
            const tempFile = await app.vault.create(tempFileName, content);
            const leaf = app.workspace.getLeaf('tab');
            await leaf.openFile(tempFile);
            showUserMessage(`汇总已导出到临时文件: ${tempFileName}，请手动重命名`, 'warning');
        } catch (fallbackError) {
            showUserMessage('导出失败: ' + fallbackError.message, 'error');
        }
    }
}

// 移动文件到search-results文件夹
async function moveToSearchResultsFolder(file, fileName) {
    try {
        // 确保search-results文件夹存在
        const searchResultsDir = app.vault.getAbstractFileByPath('search-results');
        if (!searchResultsDir) {
            await app.vault.createFolder('search-results');
        }

        // 移动文件
        const newPath = `search-results/${fileName}`;
        await app.vault.rename(file, newPath);
        showUserMessage(`✅ 文件已移动到：${newPath}`, 'success');

    } catch (error) {
        showUserMessage(`⚠️ 移动文件失败：${error.message}`, 'warning');
    }
}



// 用户消息显示函数
function showUserMessage(message, type = 'info') {
    const messageDiv = document.createElement('div');

    let bgColor, textColor;
    switch (type) {
        case 'success':
            bgColor = 'var(--color-green)';
            textColor = 'white';
            break;
        case 'warning':
            bgColor = 'var(--color-orange)';
            textColor = 'white';
            break;
        case 'error':
            bgColor = 'var(--color-red)';
            textColor = 'white';
            break;
        default: // info
            bgColor = 'var(--color-blue)';
            textColor = 'white';
    }

    messageDiv.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${bgColor};
        color: ${textColor};
        padding: 12px 20px;
        border-radius: 6px;
        z-index: 10000;
        font-size: 14px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        max-width: 400px;
        word-wrap: break-word;
    `;
    messageDiv.innerHTML = message;
    document.body.appendChild(messageDiv);

    // 自动移除消息
    const duration = type === 'error' ? 5000 : 3000;
    setTimeout(() => {
        if (document.body.contains(messageDiv)) {
            document.body.removeChild(messageDiv);
        }
    }, duration);
}

// 格式化文件大小显示
function formatFileSize(bytes) {
    if (!bytes) return '未知';
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
}

// 在当前页面显示汇总内容的简化fallback函数
function displaySummaryInCurrentPage(content, fileName) {
    // 在搜索结果区域显示汇总内容
    const summaryHtml = `
        <div style="margin-bottom: 20px; padding: 20px; background: var(--background-secondary); border-radius: 8px; border-left: 4px solid var(--color-purple);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="margin: 0; color: var(--text-normal);">📄 搜索结果汇总</h3>
                <div>
                    <button onclick="copySummaryToClipboard()" style="background: var(--color-blue); color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-right: 8px;">📋 复制</button>
                    <button onclick="downloadSummaryAsFile()" style="background: var(--color-green); color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">💾 下载</button>
                </div>
            </div>
            <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 10px;">
                文件名：${fileName} | 生成时间：${new Date().toLocaleString('zh-CN')}
            </div>
            <div style="max-height: 400px; overflow-y: auto; background: var(--background-primary); padding: 15px; border-radius: 4px; font-family: var(--font-monospace); font-size: 12px; line-height: 1.4; white-space: pre-wrap;">${content}</div>
            <div style="margin-top: 10px; font-size: 12px; color: var(--text-muted);">
                💡 提示：点击"复制"按钮复制内容，然后手动创建文件保存
            </div>
        </div>
    `;

    // 将汇总内容插入到结果区域的顶部
    resultContent.innerHTML = summaryHtml + resultContent.innerHTML;

    // 滚动到顶部显示汇总
    resultContent.scrollTop = 0;

    // 保存内容到全局变量，供复制和下载功能使用
    window.currentSummaryContent = content;
    window.currentSummaryFileName = fileName;
}

// 复制汇总到剪贴板
window.copySummaryToClipboard = async function() {
    try {
        await navigator.clipboard.writeText(window.currentSummaryContent);
        showUserMessage('✅ 汇总内容已复制到剪贴板', 'success');
    } catch (error) {
        console.error('复制失败:', error);
        showUserMessage('❌ 复制失败，请手动选择文本复制', 'error');
    }
};

// 下载汇总为文件
window.downloadSummaryAsFile = function() {
    try {
        const blob = new Blob([window.currentSummaryContent], { type: 'text/markdown' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = window.currentSummaryFileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        showUserMessage('✅ 汇总文件已下载', 'success');
    } catch (error) {
        console.error('下载失败:', error);
        showUserMessage('❌ 下载失败，请手动复制内容', 'error');
    }
};

// 在当前页面显示汇总内容和错误报告
function displaySummaryWithErrorReport(content, fileName, errorReport) {
    const summaryHtml = `
        <div style="margin-bottom: 20px; padding: 20px; background: var(--background-secondary); border-radius: 8px; border-left: 4px solid var(--color-red);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                <h3 style="margin: 0; color: var(--text-normal);">⚠️ 汇总导出失败 - 内容预览</h3>
                <div>
                    <button onclick="copySummaryToClipboard()" style="background: var(--color-blue); color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; margin-right: 8px;">📋 复制汇总</button>
                    <button onclick="copyErrorReport()" style="background: var(--color-red); color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer;">🔍 复制错误报告</button>
                </div>
            </div>

            <div style="margin-bottom: 15px;">
                <h4 style="margin: 0 0 10px 0; color: var(--text-normal);">错误报告:</h4>
                <div style="max-height: 200px; overflow-y: auto; background: var(--background-primary); padding: 15px; border-radius: 4px; font-family: var(--font-monospace); font-size: 11px; line-height: 1.4; white-space: pre-wrap; border: 1px solid var(--color-red);">${errorReport}</div>
            </div>

            <div>
                <h4 style="margin: 0 0 10px 0; color: var(--text-normal);">汇总内容预览:</h4>
                <div style="font-size: 12px; color: var(--text-muted); margin-bottom: 10px;">
                    文件名：${fileName} | 生成时间：${new Date().toLocaleString('zh-CN')}
                </div>
                <div style="max-height: 400px; overflow-y: auto; background: var(--background-primary); padding: 15px; border-radius: 4px; font-family: var(--font-monospace); font-size: 12px; line-height: 1.4; white-space: pre-wrap;">${content}</div>
            </div>

            <div style="margin-top: 15px; padding: 10px; background: var(--background-primary); border-radius: 4px; font-size: 12px; color: var(--text-muted);">
                💡 <strong>解决建议</strong>：<br>
                1. 点击"📋 复制汇总"手动创建文件<br>
                2. 点击"🔍 复制错误报告"获取详细错误信息
            </div>
        </div>
    `;

    // 将内容插入到结果区域的顶部
    resultContent.innerHTML = summaryHtml + resultContent.innerHTML;

    // 滚动到顶部显示内容
    resultContent.scrollTop = 0;

    // 保存内容到全局变量
    window.currentSummaryContent = content;
    window.currentSummaryFileName = fileName;
    window.currentErrorReport = errorReport;
}

// 复制错误报告
window.copyErrorReport = async function() {
    try {
        await navigator.clipboard.writeText(window.currentErrorReport);
        showUserMessage('✅ 错误报告已复制到剪贴板', 'success');
    } catch (error) {
        showUserMessage('❌ 复制失败，请手动选择文本复制', 'error');
    }
};

// 排序函数
function sortSearchResults(results, sortBy, keywords) {
    switch (sortBy) {
        case 'relevance':
            results.sort((a, b) => b.relevanceScore - a.relevanceScore);
            break;
        case 'modified':
            results.sort((a, b) => new Date(b.mtime) - new Date(a.mtime));
            break;
        case 'created':
            results.sort((a, b) => new Date(b.ctime) - new Date(a.ctime));
            break;
        case 'size':
            results.sort((a, b) => (b.size || 0) - (a.size || 0));
            break;
        case 'name':
            results.sort((a, b) => a.name.localeCompare(b.name));
            break;
        default:
            results.sort((a, b) => b.relevanceScore - a.relevanceScore);
    }
}

// 增强的匹配函数
function isEnhancedMatch(text, keywords, mode) {
    if (!text || !keywords || keywords.length === 0) return false;

    const lowerText = text.toLowerCase();
    const lowerKeywords = keywords.map(k => k.toLowerCase());

    switch (mode) {
        case 'AND':
            return lowerKeywords.every(k => lowerText.includes(k));
        case 'OR':
            return lowerKeywords.some(k => lowerText.includes(k));
        case 'EXACT':
            return lowerText.includes(keywords.join(' ').toLowerCase());
        default:
            return lowerKeywords.some(k => lowerText.includes(k));
    }
}

// 异步搜索函数（增强版）
async function performEnhancedSearch() {
    const keyword = searchInput.value.trim();
    const mode = modeSelect.value;
    const dirFilter = dirSelect.value;
    const searchScope = scopeSelect.value;
    const fileTypeFilter = fileTypeSelect.value;
    const sortBy = sortSelect.value;

    // 保存排除设置
    saveExcludeSettings();

    if (!keyword) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                <div>请输入搜索关键词</div>
            </div>
        `;
        return;
    }

    // 检查是否已有搜索在进行
    if (searchState.isSearching) {
        cancelCurrentSearch();
    }

    // 初始化搜索状态
    searchState.isSearching = true;
    searchState.searchAbortController = new AbortController();

    // 显示进度和取消按钮
    showSearchProgress();

    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
            <div>正在智能搜索中...</div>
        </div>
    `;

    try {
        let allPages = dv.pages();

        // 排除系统目录
        allPages = allPages.where(p =>
            !CONFIG.excludeDirs.some(dir => p.file.path.includes(dir))
        );

        // 根据目录过滤
        if (dirFilter !== 'all') {
            allPages = allPages.where(p => p.file.path.includes(dirFilter + '/'));
        }

        // 根据文件类型过滤
        if (fileTypeFilter !== 'all') {
            allPages = allPages.where(p => {
                const fileType = getFileType(p.file.path);
                return fileType === fileTypeFilter;
            });
        }

        const keywords = keyword.split(/\s+/).filter(k => k.length > 0);
        const results = [];
        const totalFiles = allPages.length;
        let processedFiles = 0;

        // 分批异步处理
        const batchSize = CONFIG.performance.batchSize;
        const allPagesArray = Array.from(allPages);

        for (let i = 0; i < allPagesArray.length; i += batchSize) {
            // 检查是否被取消
            if (searchState.searchAbortController.signal.aborted) {
                hideSearchProgress();
                return;
            }

            const batch = allPagesArray.slice(i, i + batchSize);

            // 处理当前批次
            await processBatch(batch, keywords, mode, searchScope, results);

            // 更新进度
            processedFiles += batch.length;
            updateSearchProgress(processedFiles, totalFiles);

            // 让出控制权，避免阻塞UI
            await new Promise(resolve => setTimeout(resolve, 10));
        }

        // 智能排序结果
        sortSearchResults(results, sortBy, keywords);

        // 完成搜索
        searchState.isSearching = false;
        hideSearchProgress();

        addToHistory(keyword, mode, dirFilter, searchScope, fileTypeFilter, sortBy, results.length);
        displayEnhancedResults(results, keywords, dirFilter, searchScope, fileTypeFilter, sortBy);

    } catch (error) {
        searchState.isSearching = false;
        hideSearchProgress();

        if (error.name === 'AbortError') {
            resultContent.innerHTML = `
                <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                    <div style="font-size: 24px; margin-bottom: 10px;">⏹️</div>
                    <div>搜索已取消</div>
                </div>
            `;
        } else {
            resultContent.innerHTML = `
                <div style="text-align: center; color: var(--text-error); padding: 40px;">
                    <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                    <div>搜索失败，请重试</div>
                </div>
            `;
        }
    }
}

// 增强的结果显示函数
function displayEnhancedResults(results, keywords, dirFilter, searchScope, fileTypeFilter, sortBy) {
    // 保存当前搜索状态（用于汇总导出）
    searchState.currentSearch = {
        results,
        keywords,
        settings: { dirFilter, searchScope, fileTypeFilter, sortBy, mode: modeSelect.value }
    };

    // 显示/隐藏汇总导出按钮
    if (results.length > 0) {
        exportBtn.style.display = 'inline-block';
    } else {
        exportBtn.style.display = 'none';
    }

    // 搜索范围和类型的描述
    let scopeDescription = '';
    switch (searchScope) {
        case 'filename': scopeDescription = '仅文件名'; break;
        case 'content': scopeDescription = '仅文件内容'; break;
        default: scopeDescription = '全部内容';
    }

    let fileTypeDescription = '';
    switch (fileTypeFilter) {
        case 'markdown': fileTypeDescription = 'Markdown'; break;
        case 'image': fileTypeDescription = '图片'; break;
        case 'document': fileTypeDescription = '文档'; break;
        case 'media': fileTypeDescription = '媒体'; break;
        case 'code': fileTypeDescription = '代码'; break;
        default: fileTypeDescription = '所有类型';
    }

    let sortDescription = '';
    switch (sortBy) {
        case 'relevance': sortDescription = '按相关性'; break;
        case 'modified': sortDescription = '按修改时间'; break;
        case 'created': sortDescription = '按创建时间'; break;
        case 'size': sortDescription = '按文件大小'; break;
        case 'name': sortDescription = '按文件名'; break;
        default: sortDescription = '按相关性';
    }

    if (results.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">未找到匹配的笔记</div>
                <div style="font-size: 14px;">
                    搜索范围: ${scopeDescription} | 文件类型: ${fileTypeDescription}
                    ${dirFilter !== 'all' ? ` | 目录: ${dirFilter}` : ''}
                </div>
            </div>
        `;
        return;
    }

    let html = `
        <div style="margin-bottom: 15px; padding: 15px; background: var(--background-secondary); border-radius: 8px; border-left: 4px solid var(--interactive-accent);">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                <strong style="font-size: 16px;">🎯 找到 ${results.length} 个匹配结果</strong>
                <span style="color: var(--text-muted); font-size: 12px;">${sortDescription}</span>
            </div>
            <div style="color: var(--text-muted); font-size: 13px; line-height: 1.4;">
                <span style="margin-right: 15px;">🔍 关键词: ${keywords.join(', ')}</span>
                <span style="margin-right: 15px;">📄 范围: ${scopeDescription}</span>
                <span style="margin-right: 15px;">📁 类型: ${fileTypeDescription}</span>
                ${dirFilter !== 'all' ? `<span style="margin-right: 15px;">📂 目录: ${dirFilter}</span>` : ''}
                ${(() => {
                    const excludedDirs = getExcludedDirectories();
                    return excludedDirs.length > 0 ? `<span>🚫 排除: ${excludedDirs.join(', ')}</span>` : '';
                })()}<br>
                💡 点击文件名将在新标签页中打开文件
            </div>
        </div>
    `;

    results.forEach((result, index) => {
        const modifiedDate = new Date(result.mtime).toLocaleDateString('zh-CN');
        const createdDate = new Date(result.ctime).toLocaleDateString('zh-CN');
        const pathParts = result.path.split('/');
        const directory = pathParts.length > 1 ? pathParts[0] : '根目录';
        const fileSize = result.size ? formatFileSize(result.size) : '未知';
        const fileTypeIcon = getFileTypeIcon(result.fileType);

        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 10px; margin-bottom: 15px; padding: 18px; background: var(--background-secondary); transition: all 0.2s ease;"
                 onmouseover="this.style.boxShadow='0 4px 12px rgba(0,0,0,0.1)'; this.style.transform='translateY(-2px)';"
                 onmouseout="this.style.boxShadow='none'; this.style.transform='translateY(0)';">

                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 10px;">
                    <h4 style="margin: 0; color: var(--text-normal); flex: 1;">
                        <span style="color: var(--link-color); font-weight: bold; cursor: pointer; text-decoration: underline; font-size: 16px; display: inline-flex; align-items: center; gap: 4px;"
                              onclick="safeOpenFile('${result.path}')"
                              title="点击在新标签页中打开文件">
                            ${fileTypeIcon} ${result.name} <span style="font-size: 11px; color: var(--text-muted);">↗️</span>
                        </span>
                    </h4>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <span style="background: var(--interactive-accent); color: white; padding: 2px 8px; border-radius: 12px; font-size: 11px; font-weight: bold;">
                            ${Math.round(result.relevanceScore)}分
                        </span>
                        <span style="color: var(--text-muted); font-size: 11px;">#${index + 1}</span>
                    </div>
                </div>

                <div style="display: flex; flex-wrap: wrap; gap: 12px; font-size: 12px; color: var(--text-muted); margin-bottom: 12px;">
                    <span style="display: flex; align-items: center; gap: 4px;">📁 ${directory}</span>
                    <span style="display: flex; align-items: center; gap: 4px;">📅 修改: ${modifiedDate}</span>
                    <span style="display: flex; align-items: center; gap: 4px;">📊 大小: ${fileSize}</span>
                    <span style="display: flex; align-items: center; gap: 4px;">🔍 匹配: ${result.matchType}</span>
                </div>

                ${renderEnhancedSnippet(result.snippet, keywords, result.matchType)}
            </div>
        `;
    });

    resultContent.innerHTML = html;
}

// 辅助函数
function getFileTypeIcon(fileType) {
    const icons = {
        'markdown': '📝',
        'image': '🖼️',
        'document': '📄',
        'media': '🎵',
        'code': '💻',
        'other': '📄'
    };
    return icons[fileType] || '📄';
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

function renderEnhancedSnippet(snippet, keywords, matchType) {
    if (matchType === '文件名') {
        return `
            <div style="background: var(--background-primary); padding: 12px; border-radius: 6px; border-left: 3px solid var(--color-blue);">
                <div style="font-size: 13px; color: var(--text-normal); font-weight: 500;">
                    ${highlightEnhancedText(snippet, keywords)}
                </div>
            </div>
        `;
    } else if (Array.isArray(snippet)) {
        // 多段落匹配
        let html = '';
        snippet.forEach((section, index) => {
            html += `
                <div style="background: var(--background-primary); padding: 12px; border-radius: 6px; border-left: 3px solid var(--color-green); margin-bottom: ${index < snippet.length - 1 ? '8px' : '0'};">
                    <div style="font-size: 11px; color: var(--text-muted); margin-bottom: 6px;">
                        📍 第 ${section.matchLine} 行附近
                    </div>
                    <div style="font-size: 13px; color: var(--text-normal); line-height: 1.5;">
                        ${section.lines.map(line => {
                            const prefix = line.isMatch ? '➤ ' : '  ';
                            const style = line.isMatch ? 'font-weight: 500; background: var(--background-secondary); padding: 2px 4px; border-radius: 3px;' : '';
                            return `<div style="${style}">${prefix}${highlightEnhancedText(line.content, keywords)}</div>`;
                        }).join('')}
                    </div>
                </div>
            `;
        });
        return html;
    } else {
        return `
            <div style="background: var(--background-primary); padding: 12px; border-radius: 6px; border-left: 3px solid var(--color-green);">
                <div style="font-size: 13px; color: var(--text-normal); line-height: 1.4;">
                    ${highlightEnhancedText(snippet, keywords)}
                </div>
            </div>
        `;
    }
}

// 增强的高亮函数
function highlightEnhancedText(text, keywords) {
    if (!text) return '';
    let highlighted = text;
    keywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        highlighted = highlighted.replace(regex, '<mark style="background: var(--text-highlight-bg); color: var(--text-normal); padding: 1px 2px; border-radius: 2px; font-weight: 600;">$1</mark>');
    });
    return highlighted;
}

// 搜索历史功能
function showUniversalHistory() {
    if (searchHistory.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📚</div>
                <div style="font-size: 18px;">暂无搜索历史</div>
            </div>
        `;
        return;
    }

    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: var(--background-secondary); border-radius: 6px; border-left: 4px solid var(--color-blue);">
            <strong>📚 搜索历史</strong>
            <span style="margin-left: 10px; color: var(--text-muted);">最近 ${searchHistory.length} 条记录</span>
        </div>
    `;

    searchHistory.forEach((item, index) => {
        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 8px; margin-bottom: 10px; padding: 12px; background: var(--background-secondary); cursor: pointer;"
                 onclick="restoreEnhancedSearch('${item.keyword}', '${item.mode}', '${item.dirFilter}', '${item.searchScope}', '${item.fileTypeFilter}', '${item.sortBy}')"
                 onmouseover="this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'"
                 onmouseout="this.style.boxShadow='none'">
                <div style="font-weight: bold; margin-bottom: 5px; color: var(--text-normal);">
                    🔍 "${item.keyword}"
                </div>
                <div style="font-size: 12px; color: var(--text-muted);">
                    模式: ${item.mode} | 范围: ${item.searchScope} | 目录: ${item.dirFilter} | 类型: ${item.fileTypeFilter} | 排序: ${item.sortBy} | 结果: ${item.resultCount}个 | ${item.timestamp}
                </div>
            </div>
        `;
    });

    resultContent.innerHTML = html;
}

function restoreEnhancedSearch(keyword, mode, dirFilter, searchScope, fileTypeFilter, sortBy) {
    searchInput.value = keyword;
    modeSelect.value = mode;
    dirSelect.value = dirFilter;
    scopeSelect.value = searchScope;
    fileTypeSelect.value = fileTypeFilter;
    sortSelect.value = sortBy;
    performEnhancedSearch();
}

// 清空结果
function clearEnhancedResults() {
    searchInput.value = '';
    suggestionsDiv.style.display = 'none';
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 10px;">🌍</div>
            <div style="font-size: 18px;">专业级智能搜索引擎准备就绪</div>
        </div>
    `;
}

// 全局函数（确保onclick可以访问）
window.safeOpenFile = safeOpenFile;
window.restoreEnhancedSearch = restoreEnhancedSearch;

// 事件绑定
setTimeout(() => {
    searchBtn.onclick = performEnhancedSearch;
    clearBtn.onclick = clearEnhancedResults;
    historyBtn.onclick = showUniversalHistory;
    refreshBtn.onclick = initDirectorySelect;
    cancelBtn.onclick = cancelCurrentSearch;
    exportBtn.onclick = exportSearchSummary;

    // 排除设置变化时保存
    excludeDirSelect.onchange = saveExcludeSettings;
    customExcludeInput.onblur = saveExcludeSettings;

    searchInput.onkeypress = function(e) {
        if (e.key === 'Enter') {
            suggestionsDiv.style.display = 'none';
            performEnhancedSearch();
        } else if (e.key === 'Escape') {
            suggestionsDiv.style.display = 'none';
        }
    };

    // 点击外部隐藏建议
    document.addEventListener('click', function(e) {
        if (!searchInputContainer.contains(e.target)) {
            suggestionsDiv.style.display = 'none';
        }
    });
}, 100);

// 初始化
initDirectorySelect();
loadExcludeSettings();
```

## 🌍 通用搜索系统 opus版特点

### 🔧 **核心特性**
- ✅ **安全文件打开**：使用优化的文件打开机制，在新标签页中打开文件
- ✅ **多重降级方案**：getLeaf('tab') → splitActiveLeaf → openLinkText → Obsidian URI
- ✅ **高级排除功能**：支持系统目录排除和自定义目录排除，设置自动保存
- ✅ **汇总导出功能**：一键生成搜索结果汇总文档，包含完整统计信息
- ✅ **用户体验优化**：专业级界面设计，智能提示和悬浮说明

### ⚡ **性能优化**
- **异步分批处理**：每批50个文件，避免UI阻塞
- **实时进度显示**：显示搜索进度和处理状态
- **可取消搜索**：支持随时取消正在进行的搜索
- **智能缓存**：缓存搜索结果，提升重复搜索速度

### 🧠 **智能搜索**
- **相关性评分**：基于TF-IDF算法的智能排序
- **多种排序方式**：支持按相关性、时间、大小、文件名排序
- **文件类型过滤**：支持Markdown、图片、文档、媒体、代码等类型
- **搜索建议**：基于历史记录的实时搜索建议

### 📊 **增强展示**
- **智能上下文**：显示匹配行的上下文内容
- **多重匹配**：显示文件中所有匹配位置
- **文件信息**：显示文件大小、修改时间、相关性分数
- **美化界面**：现代化的卡片式布局和交互效果

### 🔧 **自适应配置**
- **自动检测目录**：扫描当前 vault 的目录结构
- **智能排除系统**：三级排除策略（不排除/基础排除/完全排除）
- **自定义排除**：支持用户自定义排除目录，逗号分隔多个目录
- **设置持久化**：排除设置自动保存到本地存储

### 📄 **汇总导出**
- **一键导出**：将搜索结果导出为结构化Markdown文档
- **完整信息**：包含搜索条件、统计信息、结果列表、内容片段
- **智能命名**：自动生成带时间戳的文件名
- **目录管理**：自动创建search-results目录存储汇总文件
- **成功提示**：导出完成后显示文件路径和成功提示
- **手动配置选项**：可在代码顶部的 CONFIG 区域自定义
- **排除系统目录**：自动排除 .obsidian、Templates 等
- **独立搜索历史**：不同 vault 的搜索历史分开保存

### ⚙️ **高级配置**
在代码顶部的 CONFIG 区域可以修改：
- `performance.batchSize`：批处理大小（默认50）
- `performance.searchDelay`：搜索防抖延迟（默认300ms）
- `search.contextLines`：上下文行数（默认2行）
- `search.maxSnippetLength`：最大片段长度（默认200字符）
- `fileTypes`：支持的文件类型配置

### 🎯 **使用技巧**
- **安全打开**：点击文件名在新标签页中打开，不会导致崩溃
- **精确控制**：使用文件类型和排序选项精确定位
- **批量处理**：大型vault自动分批处理，保持流畅
- **历史回溯**：点击搜索历史快速重复搜索

### 🛡️ **稳定性保证**
- **多重降级方案**：确保在各种环境下都能正常工作
- **完善错误处理**：失败时提供详细错误信息和解决建议
- **兼容性检查**：支持不同版本的Obsidian
- **用户友好提示**：清晰的操作指导和状态反馈

## 📖 新功能使用说明

### 🚫 高级排除功能
1. **系统目录排除**：
   - **不排除系统目录**：搜索所有文件
   - **排除基础系统目录**：排除 `.obsidian`、`Templates`、`.trash`
   - **排除所有系统目录**：额外排除 `.git`、`node_modules`、`.vscode`、`Attachments`、`Archive`

2. **自定义排除**：
   - 在"自定义排除目录"输入框中输入要排除的目录名称
   - 多个目录用逗号分隔，如：`Archive, Temp, Draft`
   - 设置会自动保存，下次使用时自动恢复
   - 排除条件会在搜索结果顶部显示

### 📄 汇总导出功能
1. **生成汇总**：
   - 执行搜索后，如有结果会显示"📄 生成汇总"按钮
   - 点击按钮自动生成包含完整信息的汇总文档
   - 文件保存在 `search-results/` 目录下，自动创建目录

2. **汇总内容包含**：
   - 搜索条件摘要（关键词、模式、范围、排除设置等）
   - 统计信息（结果总数、平均相关性、文件类型分布）
   - 详细结果列表（路径、匹配类型、相关性分数、内容片段）
   - 快速链接列表（可直接点击跳转到文件）

3. **文件命名规则**：
   - 格式：`搜索汇总-[关键词]-YYYYMMDD-HHMMSS.md`
   - 包含时间戳，便于管理和查找
   - 导出完成后显示成功提示和文件路径

### 💡 使用技巧
- 排除设置在每次搜索时自动保存，无需手动配置
- 汇总导出按钮只在有搜索结果时显示，避免误操作
- 所有新功能都与现有搜索模式完全兼容
- 界面文本已优化为专业级表述，提升用户体验

---

*🌟 这是一个专业级的智能搜索系统，现已支持高级排除和汇总导出功能！*
