# 🦐 Shrimp Task Manager MCP 配置指南

## 📋 概述

Shrimp Task Manager 是一个专为 AI Agent 设计的智能任务管理系统，具有以下核心特性：

- 🧠 **智能任务规划**：深度理解和分析复杂任务需求
- 🔄 **任务分解**：自动将大任务拆分为可管理的小任务
- 📊 **依赖管理**：精确处理任务间依赖关系
- 🔍 **研究模式**：系统化技术调研和知识收集
- 📝 **项目规则**：定义和维护项目标准
- 🌐 **Web GUI**：可选的网页管理界面

## ⚙️ 配置信息

### 当前配置状态
- ✅ **配置文件**：`.cursor/mcp.json`
- ✅ **数据目录**：`C:\Users\<USER>\Desktop\测试库\shrimp-data`
- ✅ **模板语言**：中文 (zh)
- ✅ **Web GUI**：已启用
- ✅ **超时设置**：600 秒

### MCP 服务器配置
```json
{
  "shrimp-task-manager": {
    "command": "npx",
    "args": ["-y", "mcp-shrimp-task-manager"],
    "timeout": 600,
    "env": {
      "DATA_DIR": "C:\\Users\\<USER>\\Desktop\\测试库\\shrimp-data",
      "TEMPLATES_USE": "zh",
      "ENABLE_GUI": "true"
    }
  }
}
```

## 🎯 核心功能使用

### 1. 任务规划
```
"请帮我规划一个 Python 学习任务"
"plan task 开发一个知识管理系统"
"制定一个为期一个月的编程学习计划"
```

### 2. 任务分解
```
"将这个项目分解为具体的子任务"
"split tasks 构建个人博客网站"
"分解任务：学习机器学习"
```

### 3. 研究模式
```
"进入研究模式，调研 FastAPI 框架"
"research React vs Vue 技术对比"
"研究最佳的数据库设计模式"
```

### 4. 项目规则管理
```
"初始化项目规则"
"init project rules"
"更新项目开发规范"
```

### 5. 任务执行与管理
```
"执行任务 [任务名称或ID]"
"list tasks"
"查看所有未完成的任务"
"delete task [任务ID]"
```

## 🔧 工作模式

### TaskPlanner 模式（任务规划）
专注于任务分析和规划，不执行具体代码修改：
```
您是专业的任务规划专家。分析需求，收集项目信息，使用 plan_task 创建任务。
完成后告知用户切换到 TaskExecutor 模式执行任务。
```

### TaskExecutor 模式（任务执行）
专注于任务执行和实现：
```
您是专业的任务执行专家。使用 execute_task 执行指定任务。
如无指定任务，使用 list_tasks 查找并执行未完成任务。
支持连续模式批量执行。
```

## 🌐 Web GUI 使用

启用 Web GUI 后，系统会在数据目录创建 `WebGUI.md` 文件，包含：
- 📊 任务可视化面板
- 🔍 任务搜索和筛选
- 📈 进度跟踪图表
- 🎯 优先级管理

## 🔄 验证配置

### 检查步骤
1. 重启 Cursor IDE
2. 打开 Cursor Settings → MCP
3. 确认 shrimp-task-manager 显示绿色状态
4. 测试基本功能

### 测试命令
```
"请获取系统信息"  # 测试基础功能
"plan task 测试任务规划功能"  # 测试任务规划
"list tasks"  # 测试任务列表
```

## 🛠️ 可用工具列表

配置成功后，您将获得以下工具：

| 工具名称 | 功能描述 |
|---------|----------|
| `plan_task` | 开始任务规划 |
| `analyze_task` | 深度任务分析 |
| `process_thought` | 逐步推理 |
| `reflect_task` | 任务反思改进 |
| `research_mode` | 进入研究模式 |
| `init_project_rules` | 初始化项目规则 |
| `split_tasks` | 任务分解 |
| `list_tasks` | 显示任务列表 |
| `query_task` | 搜索任务 |
| `get_task_detail` | 获取任务详情 |
| `execute_task` | 执行任务 |
| `verify_task` | 验证任务完成 |
| `delete_task` | 删除任务 |

## 🎯 最佳实践

1. **明确任务描述**：提供详细的任务需求和背景
2. **合理任务分解**：将复杂任务拆分为可执行的小步骤
3. **利用研究模式**：在开始编码前进行充分调研
4. **定期任务回顾**：使用 verify_task 检查完成质量
5. **项目规则维护**：定期更新和完善项目标准

## ⚠️ 注意事项

- 数据目录必须使用绝对路径
- 首次使用时会自动下载必要组件
- Web GUI 需要一定时间初始化
- 建议定期备份任务数据

## 🔍 故障排除

### 常见问题
1. **服务器启动失败**：检查 Node.js 版本和网络连接
2. **数据目录错误**：确保路径正确且有写入权限
3. **工具无响应**：重启 Cursor 或增加超时时间
4. **中文显示异常**：确认 TEMPLATES_USE 设置为 "zh"

### 解决方案
- 重新安装：`npm cache clean --force`
- 检查日志：查看 Cursor 开发者工具
- 更新版本：使用 `@latest` 标签
- 清理缓存：删除 node_modules 缓存

---

*配置完成时间：2025-06-20*
*基于 mcp-shrimp-task-manager 官方文档编写*
