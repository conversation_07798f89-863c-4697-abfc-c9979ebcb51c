# FLUX 图像生成 MCP 完整指南

## 📖 概述

本文档详细介绍如何配置和使用 FLUX 图像生成 MCP 服务器，实现在 AI 助手中直接生成高质量图像的功能。

## 🎯 核心知识点

### 1. MCP (Model Context Protocol) 基础
- **定义**：模型上下文协议，用于 AI 助手与外部工具的标准化通信
- **作用**：扩展 AI 助手的功能，如图像生成、文件操作、API 调用等
- **配置文件**：`claude_desktop_config.json` 或类似配置文件

### 2. FLUX 模型系列
- **FLUX.1 Schnell**：快速生成模型，适合实时应用
- **FLUX.1 Pro**：专业版本，更高质量
- **FLUX.1 Dev**：开发版本，开源权重
- **FLUX.1 Kontext**：上下文感知的图像编辑模型

### 3. 服务提供商对比

| 服务商 | 模型 | 费用 | 质量 | 推荐度 |
|--------|------|------|------|--------|
| Together AI | FLUX.1 Schnell Free | 🆓 免费 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| Replicate | FLUX 全系列 | 💰 付费 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| Black Forest Labs | FLUX 原生 | 💰 付费 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |

## 🚀 安装配置指南

### 步骤 1：环境准备
```bash
# 检查 Node.js 版本（需要 >= 16）
node --version

# 检查 npm 版本
npm --version
```

### 步骤 2：获取 API Key
1. **Together AI（推荐免费方案）**
   - 访问：https://api.together.xyz/settings/api-keys
   - 注册账户并创建 API Key
   - 复制生成的 Token

2. **Replicate（付费方案）**
   - 访问：https://replicate.com/account/api-tokens
   - 注册账户并设置付费方式
   - 创建 API Token

### 步骤 3：MCP 配置

#### Together AI 配置（免费推荐）
```json
{
  "mcpServers": {
    "together-image-gen": {
      "command": "npx",
      "args": ["together-mcp@latest"],
      "env": {
        "TOGETHER_API_KEY": "your_api_key_here"
      },
      "timeout": 600
    }
  }
}
```

#### Replicate 配置（付费方案）
```json
{
  "mcpServers": {
    "replicate-flux-mcp": {
      "command": "npx",
      "args": ["-y", "replicate-flux-mcp"],
      "env": {
        "REPLICATE_API_TOKEN": "your_token_here"
      },
      "timeout": 600
    }
  }
}
```

### 步骤 4：测试验证
```bash
# 测试包安装
npx together-mcp@latest --help

# 或测试 Replicate
npx replicate-flux-mcp --help
```

### 步骤 5：重启应用
1. 完全关闭 AI 助手应用
2. 重新启动以加载新配置

## 🎨 使用方法

### 基础图像生成
```
请生成一张美丽的风景照片
```

### 指定参数生成
```
生成一张 1024x768 的现代办公室图片，高质量
```

### 风格化生成
```
创建一个赛博朋克风格的城市夜景，霓虹灯效果
```

### 人物生成
```
生成一个金发美女，手里拿着咖啡，温暖的微笑
```

### 批量生成
```
生成 3 张不同风格的花园图片
```

## 🔧 高级参数

### Together AI 参数
- **prompt**：图像描述（必需）
- **width**：宽度（128-2048，默认1024）
- **height**：高度（128-2048，默认768）
- **steps**：推理步数（1-100，默认1）
- **n**：生成数量（最多4张）
- **image_path**：保存路径（可选）

### Replicate 参数
- **prompt**：图像描述（必需）
- **aspect_ratio**：长宽比（1:1, 16:9, 4:3等）
- **output_quality**：输出质量（0-100）
- **num_outputs**：输出数量（1-4）
- **seed**：随机种子（可复现）

## 💡 提示词优化技巧

### 1. 结构化描述
```
主体 + 环境 + 光线 + 风格 + 质量描述

示例：
一位优雅的女性（主体）
在现代咖啡厅中（环境）
柔和的自然光线（光线）
专业人像摄影风格（风格）
高分辨率，细节丰富（质量）
```

### 2. 关键词分类

**风格关键词**：
- 摄影：professional photography, portrait, landscape
- 艺术：oil painting, watercolor, digital art, sketch
- 时代：modern, vintage, futuristic, classical

**质量关键词**：
- high quality, detailed, sharp focus
- 4K, 8K, ultra-detailed
- professional, masterpiece

**光线关键词**：
- golden hour, soft lighting, dramatic lighting
- natural light, studio lighting, neon lights

### 3. 避免的内容
- 版权受保护的角色或品牌
- 真实人物的肖像
- 不当或敏感内容
- 过于复杂的场景描述

## 📁 文件管理

### 推荐目录结构
```
C:\Users\<USER>\Desktop\测试库\
├── cursor_projects\
│   └── together\
│       ├── landscapes\      # 风景图片
│       ├── portraits\       # 人物图片
│       ├── abstract\        # 抽象艺术
│       └── misc\           # 其他图片
└── notes\
    └── FLUX图像生成MCP完整指南.md
```

### 自动保存设置
```
# 在生成时指定保存路径
生成一张海滩照片，保存到 landscapes 文件夹
```

## 🔍 故障排除

### 常见问题

1. **API Key 错误**
   - 检查 API Key 是否正确复制
   - 确认账户状态和余额

2. **包安装失败**
   - 检查网络连接
   - 清理 npm 缓存：`npm cache clean --force`

3. **图片生成失败**
   - 检查提示词是否合规
   - 确认参数设置正确

4. **图片保存问题**
   - 确认保存路径存在
   - 检查文件夹写入权限

### 调试方法
```bash
# 查看详细错误信息
npx together-mcp@latest --verbose

# 检查环境变量
echo $TOGETHER_API_KEY
```

## 📊 性能优化

### 1. 参数优化
- **快速生成**：steps=1, 较小尺寸
- **高质量**：steps=4-20, 大尺寸
- **批量生成**：使用 n 参数而非多次调用

### 2. 成本控制
- 优先使用免费的 Together AI
- 合理设置图片尺寸
- 避免不必要的重复生成

## 🎯 最佳实践

1. **提示词设计**：清晰、具体、结构化
2. **参数选择**：根据需求平衡质量和速度
3. **文件管理**：建立清晰的目录结构
4. **版本控制**：记录重要的生成参数
5. **备份策略**：定期备份重要图片

## 🔮 扩展应用

### 1. 工作流集成
- 与设计软件联动
- 批量内容生成
- 自动化图片处理

### 2. 创意应用
- 概念设计
- 故事板制作
- 产品原型可视化

### 3. 商业应用
- 营销素材生成
- 社交媒体内容
- 网站配图制作

## 📚 实用示例库

### 风景摄影
```
# 山湖景色
一张专业摄影作品，展现宁静的山湖景色，黄昏时分，暖色调，倒影清晰，构图平衡

# 海滩日落
金色沙滩上的日落景色，海浪轻拍岸边，天空呈现橙红色渐变，专业风景摄影

# 森林晨雾
清晨的森林中，阳光透过薄雾照射在树林间，神秘而宁静的氛围
```

### 人物肖像
```
# 商务人士
一位专业的商务女性，穿着优雅的西装，自信的微笑，现代办公环境背景

# 艺术肖像
一位艺术家在工作室中，专注地绘画，柔和的自然光线，艺术氛围浓厚

# 生活场景
一位年轻女性在咖啡厅中享受下午茶时光，温暖的光线，休闲惬意
```

### 创意设计
```
# 科技感
未来主义的科技界面设计，蓝色霓虹灯效果，简洁的几何图形

# 抽象艺术
色彩丰富的抽象画作，流动的线条和渐变色彩，现代艺术风格

# 产品展示
简约的产品展示场景，白色背景，专业的产品摄影光线
```

## 🛠️ 工具集成

### 与其他 MCP 服务器配合
```json
{
  "mcpServers": {
    "together-image-gen": {
      "command": "npx",
      "args": ["together-mcp@latest"],
      "env": {
        "TOGETHER_API_KEY": "your_key"
      }
    },
    "file-manager": {
      "command": "npx",
      "args": ["file-manager-mcp"]
    },
    "web-search": {
      "command": "npx",
      "args": ["web-search-mcp"]
    }
  }
}
```

### 批处理脚本示例
```bash
#!/bin/bash
# 批量生成不同风格的图片

styles=("油画风格" "水彩风格" "素描风格" "数字艺术风格")
subject="美丽的花园景色"

for style in "${styles[@]}"; do
    echo "生成${style}的${subject}，保存为 garden_${style}.png"
done
```

## 📈 进阶技巧

### 1. 种子值控制
```
# 使用相同种子获得一致结果
生成一张风景照，种子值设为 12345

# 系列图片生成
使用种子值 12345-12349 生成 5 张相似风格的图片
```

### 2. 渐进式优化
```
# 第一步：基础生成
生成一张山景照片

# 第二步：添加细节
在刚才的山景基础上，添加湖泊倒影

# 第三步：优化光线
调整为黄金时刻的温暖光线
```

### 3. 风格迁移
```
# 参考风格
以梵高的《星夜》风格，生成一张现代城市夜景

# 混合风格
结合日式浮世绘和现代摄影风格，创作樱花盛开的场景
```

## 🔐 安全与合规

### 内容政策
- 避免生成真实人物肖像
- 不生成版权保护的角色
- 遵守平台使用条款
- 尊重隐私和肖像权

### 数据安全
- 定期更新 API Key
- 不在公共场所暴露配置
- 备份重要的生成结果
- 遵循数据保护法规

## 📞 技术支持

### 官方资源
- **Together AI 文档**：https://docs.together.ai/
- **Replicate 文档**：https://replicate.com/docs
- **MCP 协议规范**：https://modelcontextprotocol.io/

### 社区资源
- GitHub Issues 和讨论区
- Discord 社区频道
- Reddit r/MachineLearning
- Stack Overflow 相关标签

### 常用命令速查
```bash
# 检查配置
cat claude_desktop_config.json

# 测试连接
npx together-mcp@latest --version

# 清理缓存
npm cache clean --force

# 重新安装
npm uninstall -g together-mcp
npm install -g together-mcp@latest
```

---

*本指南涵盖了 FLUX 图像生成 MCP 的完整知识体系，从基础配置到高级应用，助您快速掌握 AI 图像生成技术。定期更新以保持内容的时效性和准确性。*
