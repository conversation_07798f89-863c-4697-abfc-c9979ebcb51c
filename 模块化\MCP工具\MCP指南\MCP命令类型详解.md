# MCP 命令类型详解

## 概述

在MCP配置中，`command` 字段指定了如何启动MCP服务器。主要有三种方式：

## 1. NPX 命令

### 语法
```json
{
  "command": "npx",
  "args": ["package-name"]
}
```

### 说明
- **npx** 是 Node.js 包管理器的执行工具
- 用于运行 **JavaScript/Node.js** 编写的MCP服务器
- 会自动下载并执行npm包

### 工作原理
1. 检查本地是否已安装包
2. 如果没有，临时下载到缓存
3. 执行包中的可执行文件
4. 执行完成后可选择保留或删除

### 示例
```json
{
  "context7": {
    "command": "npx",
    "args": ["context7"]
  },
  "sequential-thinking": {
    "command": "npx", 
    "args": ["sequential-thinking"]
  },
  "playwright": {
    "command": "npx",
    "args": ["@executeautomation/playwright-mcp-server"]
  }
}
```

### 适用场景
- JavaScript/TypeScript编写的MCP服务器
- 需要Node.js运行环境的工具
- 官方或社区提供的npm包

## 2. UVX 命令

### 语法
```json
{
  "command": "uvx",
  "args": ["package-name"]
}
```

### 说明
- **uvx** 是 uv (Python包管理器) 的执行工具
- 用于运行 **Python** 编写的MCP服务器
- 类似于npx，但针对Python生态

### 工作原理
1. 创建临时虚拟环境
2. 安装指定的Python包
3. 在隔离环境中执行
4. 自动管理依赖关系

### 示例
```json
{
  "mcp-obsidian": {
    "command": "uvx",
    "args": ["mcp-obsidian"]
  },
  "replicate-flux-mcp": {
    "command": "uvx",
    "args": ["replicate-flux-mcp"]
  },
  "together-image-gen": {
    "command": "uvx",
    "args": ["together-image-gen"]
  }
}
```

### 适用场景
- Python编写的MCP服务器
- 需要Python运行环境的工具
- 发布在PyPI上的包

## 3. Python 命令

### 语法
```json
{
  "command": "python",
  "args": ["-m", "module_name"]
}
```

### 说明
- **python** 直接调用系统Python解释器
- 用于运行已安装的Python模块
- 需要预先安装相关包

### 工作原理
1. 使用系统默认Python环境
2. 通过 `-m` 参数运行模块
3. 依赖系统已安装的包

### 示例
```json
{
  "fetch": {
    "command": "python",
    "args": ["-m", "mcp_server_fetch"]
  }
}
```

### 适用场景
- 已经通过pip安装的Python包
- 需要使用特定Python环境
- 调试或开发阶段

## 对比总结

| 特性 | NPX | UVX | Python |
|------|-----|-----|--------|
| **语言** | JavaScript/Node.js | Python | Python |
| **包管理** | npm | PyPI | pip |
| **环境隔离** | ❌ | ✅ | ❌ |
| **自动安装** | ✅ | ✅ | ❌ |
| **预安装需求** | Node.js | uv | Python + pip install |
| **缓存管理** | npm缓存 | uv缓存 | 系统环境 |

## 选择建议

### 使用NPX当：
- MCP服务器是JavaScript/Node.js编写
- 包发布在npm registry
- 希望自动管理依赖

### 使用UVX当：
- MCP服务器是Python编写
- 包发布在PyPI
- 希望环境隔离
- 推荐用于Python MCP

### 使用Python当：
- 需要使用特定Python环境
- 包已经预安装
- 调试或开发场景
- uvx不可用时的备选方案

## 环境变量

所有三种方式都支持环境变量：

```json
{
  "command": "uvx",
  "args": ["mcp-obsidian"],
  "env": {
    "OBSIDIAN_API_KEY": "your-key",
    "OBSIDIAN_HOST": "https://127.0.0.1:27124/"
  }
}
```

## 故障排除

### NPX问题
- 确保Node.js已安装
- 检查npm registry连接
- 清理npm缓存：`npm cache clean --force`

### UVX问题  
- 确保uv已安装：`uv --version`
- 检查PyPI连接
- 清理uv缓存：`uv cache clean`

### Python问题
- 确保Python已安装：`python --version`
- 确保包已安装：`pip list | grep package-name`
- 检查模块路径：`python -m package_name --help`

---

**总结**：选择合适的命令类型取决于MCP服务器的编程语言和您的环境偏好。
