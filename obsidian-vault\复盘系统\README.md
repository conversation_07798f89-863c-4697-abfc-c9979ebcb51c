# 🧠 智能复盘系统

> 基于Obsidian的智能化个人成长复盘系统，让每一天的反思都成为成长的阶梯

[![版本](https://img.shields.io/badge/版本-v2.0-blue.svg)](https://github.com/your-repo)
[![Obsidian](https://img.shields.io/badge/Obsidian-v1.4+-purple.svg)](https://obsidian.md)
[![许可证](https://img.shields.io/badge/许可证-MIT-green.svg)](LICENSE)

---

## 📋 目录

- [🎯 项目概述](#-项目概述)
- [✨ 功能特性](#-功能特性)
- [🚀 快速开始](#-快速开始)
- [🏗️ 系统架构](#️-系统架构)
- [📖 使用指南](#-使用指南)
- [⚙️ 配置说明](#️-配置说明)
- [🔧 故障排除](#-故障排除)
- [📝 更新日志](#-更新日志)
- [🤝 贡献指南](#-贡献指南)

---

## 🎯 项目概述

智能复盘系统是一个基于Obsidian构建的个人成长工具，旨在帮助用户建立系统化的复盘习惯，通过结构化的反思促进持续成长。

### 🌟 核心价值

- **🌱 渐进式成长**：从简单的3个问题开始，逐步深入到完整的复盘体系
- **🚀 高效便捷**：1-2步即可开始复盘，降低使用门槛
- **🔍 智能分析**：自动识别成长模式，发现个人发展规律
- **⚙️ 个性定制**：完全可配置的复盘维度和显示方式
- **📊 数据驱动**：基于历史数据的洞察和建议

### 🎯 适用人群

- 希望建立复盘习惯的个人成长爱好者
- 需要系统化反思的职场人士
- 追求持续改进的学习者
- 使用Obsidian进行知识管理的用户

---

## ✨ 功能特性

### 🌱 渐进式复盘引导系统
**让复盘变得简单易行**

- **三级难度体系**：入门级(3问题) → 标准级(5问题) → 深度级(完整)
- **智能推荐**：基于用户经验自动推荐合适的复盘级别
- **成就系统**：7种成就徽章，激励持续复盘
- **经验值机制**：量化复盘进展，可视化成长轨迹

**核心文件**：
- [[复盘系统/复盘引导中心|🎯 复盘引导中心]] - 系统导航和推荐
- [[Templates/复盘引导/入门级日复盘|🌱 入门级模板]] - 新手友好
- [[Templates/复盘引导/标准级日复盘|🌿 标准级模板]] - 进阶体验

### 🚀 复盘快速入口系统
**2步内开始复盘**

- **多种启动方式**：快速启动器页面 + QuickAdd脚本 + 直接模板
- **智能文件命名**：自动生成标准化的文件名和路径
- **模板变量处理**：自动填充日期、时间等动态内容
- **跨平台支持**：桌面端和移动端都能良好工作

**核心文件**：
- [[复盘系统/复盘快速启动器|🚀 快速启动器]] - 一键创建复盘
- [[Attachment/scripts/复盘快速入口.js|🔧 快速入口脚本]] - QuickAdd集成
- [[Attachment/scripts/QuickAdd配置指南|📋 配置指南]] - 详细设置说明

### 🔍 复盘数据关联和搜索功能
**让历史复盘数据发挥最大价值**

- **多维度搜索**：关键词、时间范围、标签、复盘级别
- **智能关联推荐**：基于内容相似性推荐相关复盘
- **模式识别分析**：自动识别成就模式、挑战模式、成长轨迹
- **可视化展示**：时间线、趋势图、统计图表

**核心文件**：
- [[复盘系统/复盘搜索系统|🔍 搜索系统]] - 智能搜索和关联
- [[复盘系统/复盘模式分析器|🧠 模式分析器]] - 深度数据挖掘
- [[复盘系统/复盘标签管理器|🏷️ 标签管理器]] - 标签体系管理

### ⚙️ 复盘配置管理系统
**真正可用的个性化配置**

- **动态配置编辑**：通过YAML front matter实时调整配置
- **配置验证系统**：自动检查配置完整性和正确性
- **个性化模板生成**：基于用户配置动态生成复盘模板
- **配置备份恢复**：完整的配置导入导出功能

**核心文件**：
- [[复盘系统/配置管理中心|⚙️ 配置管理中心]] - 主配置界面
- [[Attachment/scripts/配置应用器.js|🔧 配置应用器]] - 配置应用脚本
- [[复盘系统/配置导入导出工具|📦 导入导出工具]] - 配置备份管理

---

## 🚀 快速开始

### 📋 前置要求

**必需软件**：
- [Obsidian](https://obsidian.md) v1.4.0+
- [Dataview](https://github.com/blacksmithgu/obsidian-dataview) 插件

**推荐插件**：
- [Templater](https://github.com/SilentVoid13/Templater) - 高级模板功能
- [QuickAdd](https://github.com/chhoumann/quickadd) - 快速入口功能
- [Calendar](https://github.com/liamcain/obsidian-calendar-plugin) - 日历集成

### 📥 安装步骤

1. **下载系统文件**
   ```
   将整个复盘系统文件夹复制到你的Obsidian库中
   ```

2. **安装必需插件**
   - 打开Obsidian设置 → 社区插件
   - 搜索并安装"Dataview"插件
   - 启用插件并重启Obsidian

3. **配置文件夹结构**
   ```
   your-vault/
   ├── 复盘系统/           # 系统核心文件
   ├── Templates/          # 复盘模板
   ├── 0_Bullet Journal/   # 复盘笔记存储
   └── Attachment/scripts/ # 脚本文件
   ```

4. **验证安装**
   - 打开 [[复盘系统/复盘引导中心|🎯 复盘引导中心]]
   - 检查是否能正常显示数据统计
   - 尝试创建第一篇复盘笔记

### 🎯 首次使用

1. **选择复盘级别**
   - 新用户建议从 [[Templates/复盘引导/入门级日复盘|🌱 入门级]] 开始
   - 有经验用户可直接使用 [[Templates/复盘引导/标准级日复盘|🌿 标准级]]

2. **创建第一篇复盘**
   - 访问 [[复盘系统/复盘快速启动器|🚀 快速启动器]]
   - 点击对应的创建按钮
   - 按照模板提示填写内容

3. **个性化配置**（可选）
   - 打开 [[复盘系统/配置管理中心|⚙️ 配置管理中心]]
   - 根据个人需求调整复盘维度
   - 保存配置后即可生效

---

## 🏗️ 系统架构

### 📁 文件结构

```
复盘系统/
├── README.md                    # 系统说明文档
├── 复盘引导中心.md              # 系统导航中心
├── 复盘快速启动器.md            # 快速创建入口
├── 复盘搜索系统.md              # 搜索和关联功能
├── 复盘模式分析器.md            # 数据分析工具
├── 复盘标签管理器.md            # 标签管理系统
├── 复盘数据仪表盘.md            # 数据统计面板
├── 配置管理中心.md              # 配置管理界面
├── 配置导入导出工具.md          # 配置备份工具
├── 快速入口使用指南.md          # 使用说明
└── 配置/
    ├── 复盘引导配置.md          # 引导系统配置
    └── 复盘系统配置.md          # 系统基础配置

Templates/
├── 复盘引导/
│   ├── 入门级日复盘.md          # 新手模板
│   └── 标准级日复盘.md          # 进阶模板
├── 5_BuJo - Daily Log.md       # 深度复盘模板
├── 5_BuJo - Weekly Log.md      # 周复盘模板
└── 5_BuJo - Monthly Log.md     # 月复盘模板

Attachment/scripts/
├── 复盘快速入口.js              # QuickAdd脚本
├── 配置应用器.js                # 配置应用脚本
└── QuickAdd配置指南.md          # 脚本配置说明

0_Bullet Journal/
├── Daily Notes/                 # 日复盘存储
├── Weekly Notes/                # 周复盘存储
└── Monthly Notes/               # 月复盘存储
```

### 🔄 系统流程

```mermaid
graph TD
    A[用户启动] --> B{选择入口方式}
    B -->|快速启动器| C[选择复盘类型]
    B -->|QuickAdd脚本| C
    B -->|直接模板| D[创建复盘笔记]
    
    C --> E{智能推荐}
    E --> F[生成个性化模板]
    F --> D
    
    D --> G[填写复盘内容]
    G --> H[保存复盘记录]
    H --> I[数据分析处理]
    
    I --> J[搜索索引更新]
    I --> K[模式识别分析]
    I --> L[统计数据更新]
```

### 🧩 组件关系

- **复盘引导中心**：系统的总控制台，提供导航和状态概览
- **快速启动器**：用户的主要入口，连接所有创建功能
- **配置管理中心**：系统的个性化核心，影响所有模板生成
- **搜索分析系统**：数据的智能处理中心，提供洞察和关联

---

## 📖 使用指南

### 🌱 新手用户指南

**第一周：建立习惯**
1. 使用 [[Templates/复盘引导/入门级日复盘|🌱 入门级复盘]]
2. 每天花2-3分钟回答3个简单问题
3. 关注 [[复盘系统/复盘引导中心|🎯 引导中心]] 的进度追踪

**第二周：提升质量**
1. 尝试更详细地回答问题
2. 开始关注复盘中的模式和规律
3. 查看 [[复盘系统/复盘数据仪表盘|📊 数据仪表盘]] 了解进展

**第三周：升级体验**
1. 完成7次入门级复盘后，升级到标准级
2. 使用 [[Templates/复盘引导/标准级日复盘|🌿 标准级复盘]]
3. 开始使用标签系统分类复盘内容

### 🌿 进阶用户指南

**深度分析**
1. 使用 [[复盘系统/复盘模式分析器|🧠 模式分析器]] 发现成长规律
2. 通过 [[复盘系统/复盘搜索系统|🔍 搜索系统]] 查找相关复盘
3. 建立个人的复盘标签体系

**系统优化**
1. 在 [[复盘系统/配置管理中心|⚙️ 配置中心]] 个性化设置
2. 尝试不同的复盘维度组合
3. 使用周复盘和月复盘进行更大周期的反思

### 🌳 专家用户指南

**高级功能**
1. 使用 [[Templates/5_BuJo - Daily Log|🌳 深度复盘模式]]
2. 配置 [[Attachment/scripts/复盘快速入口.js|🔧 QuickAdd脚本]] 提升效率
3. 创建自定义的复盘维度和问题

**数据洞察**
1. 定期使用模式分析器进行深度分析
2. 建立复盘数据的可视化仪表盘
3. 将复盘洞察应用到目标设定和计划制定中

---

## ⚙️ 配置说明

### 🎯 基础配置

**复盘维度配置**
```yaml
user_config:
  daily_dimensions:
    - name: "今日成就"
      description: "今天做了什么？"
      required: true
      enabled: true
```

**显示设置配置**
```yaml
display_settings:
  theme: "default"          # 主题：default/dark/light
  language: "zh-CN"         # 语言：zh-CN/en-US
  date_format: "YYYY-MM-DD" # 日期格式
  time_format: "24h"        # 时间格式：24h/12h
```

### 🔧 高级配置

**复盘风格配置**
```yaml
review_style:
  detailed: true           # 详细模式
  include_prompts: true    # 包含提示
  auto_fill_data: true     # 自动填充数据
  template_style: "standard" # 模板风格
```

**提醒设置配置**
```yaml
reminder_settings:
  daily_enabled: true      # 启用日提醒
  daily_time: "21:00"      # 提醒时间
  weekly_enabled: true     # 启用周提醒
  weekly_day: "Sunday"     # 提醒日期
```

### 📋 配置管理

1. **编辑配置**：修改 [[复盘系统/配置管理中心|⚙️ 配置中心]] 的YAML front matter
2. **验证配置**：系统会自动验证配置的正确性
3. **应用配置**：新创建的复盘笔记会使用最新配置
4. **备份配置**：使用 [[复盘系统/配置导入导出工具|📦 导入导出工具]] 备份配置

---

## 🔧 故障排除

### ❓ 常见问题

**Q: Dataview查询不显示结果？**
A: 
1. 确认Dataview插件已正确安装并启用
2. 检查文件路径和标签是否正确
3. 尝试刷新页面或重启Obsidian

**Q: 快速入口按钮不工作？**
A: 
1. 确认Templater插件已安装
2. 检查模板文件是否存在于正确路径
3. 验证文件夹权限设置

**Q: 配置更改不生效？**
A: 
1. 确认YAML格式正确
2. 保存文件后重新创建复盘笔记
3. 检查配置验证结果

**Q: QuickAdd脚本报错？**
A: 
1. 检查脚本文件路径是否正确
2. 确认QuickAdd插件版本兼容
3. 查看控制台错误信息（F12）

### 🛠️ 故障诊断

**系统健康检查**
1. 打开 [[复盘系统/复盘引导中心|🎯 引导中心]] 检查数据显示
2. 尝试创建一篇测试复盘笔记
3. 验证搜索和分析功能是否正常

**性能优化**
1. 定期清理不需要的复盘笔记
2. 合理使用标签，避免过度复杂化
3. 在大型库中限制Dataview查询范围

### 📞 获取帮助

- 查看 [[复盘系统/快速入口使用指南|📖 使用指南]] 获取详细说明
- 检查各个组件的内置帮助文档
- 在GitHub Issues中报告问题

---

---

## 📝 更新日志

### 🚀 v2.0 (2025-08-07) - 完整版本
**重大功能更新**

**🌱 渐进式复盘引导系统**
- ✨ 新增三级难度体系（入门/标准/深度）
- ✨ 智能推荐算法，基于用户经验自动推荐级别
- ✨ 成就系统，包含7种成就徽章
- ✨ 经验值机制，量化复盘进展
- 🔧 优化新手引导流程，降低使用门槛

**🚀 复盘快速入口系统**
- ✨ 多种启动方式：快速启动器 + QuickAdd脚本 + 直接模板
- ✨ 智能文件命名和路径管理
- ✨ 跨平台支持（桌面端和移动端）
- 🔧 优化模板变量处理，支持自动填充

**🔍 复盘数据关联和搜索功能**
- ✨ 多维度搜索：关键词、时间、标签、级别
- ✨ 智能关联推荐系统
- ✨ 复盘模式分析器，识别成长规律
- ✨ 复盘标签管理器，标准化标签体系
- 🔧 优化搜索性能，避免全文搜索

**⚙️ 复盘配置管理系统**
- ✨ 动态配置编辑，支持实时调整
- ✨ 配置验证系统，自动检查配置正确性
- ✨ 个性化模板生成，基于用户配置
- ✨ 配置导入导出功能，支持备份和分享
- ✨ 预设配置模板（简约/标准/深度）

### 📊 v1.0 (2025-07-01) - MVP版本
**基础功能实现**

- ✅ 基础复盘模板（日/周/月/年）
- ✅ 简单的数据统计功能
- ✅ 基本的文件组织结构
- ✅ Dataview集成支持

### 🔄 升级指南

**从v1.0升级到v2.0**
1. 备份现有复盘数据
2. 下载新版本系统文件
3. 按照安装步骤重新配置
4. 使用配置导入工具迁移个人设置
5. 验证所有功能正常工作

---

## 🤝 贡献指南

### 🎯 如何贡献

我们欢迎各种形式的贡献，包括但不限于：

**📝 内容贡献**
- 改进文档和使用指南
- 分享优秀的复盘模板
- 提供配置预设方案
- 翻译文档到其他语言

**🔧 技术贡献**
- 报告和修复Bug
- 提出新功能建议
- 优化系统性能
- 开发插件集成

**🌟 社区贡献**
- 分享使用经验和最佳实践
- 帮助新用户解决问题
- 组织社区活动和讨论
- 推广复盘文化

### 📋 贡献流程

1. **Fork项目**：创建项目的分支
2. **创建特性分支**：`git checkout -b feature/amazing-feature`
3. **提交更改**：`git commit -m 'Add some amazing feature'`
4. **推送分支**：`git push origin feature/amazing-feature`
5. **创建Pull Request**：描述你的更改和改进

### 🏷️ 配置分享

**分享你的配置**
1. 使用 [[复盘系统/配置导入导出工具|📦 导入导出工具]] 导出配置
2. 在社区中分享你的配置JSON文件
3. 描述配置的特点和适用场景
4. 提供使用建议和注意事项

**优秀配置示例**
- **学生专用配置**：专注学习和成长的复盘维度
- **职场人士配置**：平衡工作和生活的复盘框架
- **创业者配置**：关注目标达成和决策复盘
- **健康生活配置**：重视身心健康的全面复盘

### 📊 开发规范

**代码规范**
- 使用清晰的变量和函数命名
- 添加必要的注释和文档
- 遵循JavaScript和Markdown最佳实践
- 确保跨平台兼容性

**文档规范**
- 使用Markdown格式编写
- 包含清晰的标题层次结构
- 提供实际的使用示例
- 保持内容的准确性和时效性

**测试要求**
- 在多个Obsidian版本中测试
- 验证不同操作系统的兼容性
- 确保功能的稳定性和性能
- 提供测试用例和预期结果

### 🎖️ 贡献者

感谢所有为智能复盘系统做出贡献的朋友们！

**核心开发者**
- [@YourName](https://github.com/yourname) - 项目创始人和主要开发者

**社区贡献者**
- 欢迎第一位贡献者！

**特别感谢**
- Obsidian社区提供的优秀插件生态
- Dataview插件开发者的技术支持
- 所有提供反馈和建议的用户

---

## 📄 许可证

本项目采用 [MIT许可证](LICENSE) 开源。

```
MIT License

Copyright (c) 2025 智能复盘系统

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

## 🔗 相关链接

### 📚 官方资源
- [项目主页](https://github.com/your-repo/intelligent-review-system)
- [在线文档](https://your-docs-site.com)
- [更新日志](https://github.com/your-repo/releases)
- [问题反馈](https://github.com/your-repo/issues)

### 🛠️ 依赖项目
- [Obsidian](https://obsidian.md) - 知识管理平台
- [Dataview](https://github.com/blacksmithgu/obsidian-dataview) - 数据查询插件
- [Templater](https://github.com/SilentVoid13/Templater) - 模板引擎
- [QuickAdd](https://github.com/chhoumann/quickadd) - 快速添加插件

### 🌟 推荐资源
- [复盘方法论](https://example.com/review-methodology) - 复盘理论基础
- [个人成长指南](https://example.com/personal-growth) - 成长方法论
- [Obsidian使用技巧](https://example.com/obsidian-tips) - 工具使用指南

---

## 📞 联系我们

### 💬 社区交流
- **GitHub Discussions**: [项目讨论区](https://github.com/your-repo/discussions)
- **QQ群**: 123456789（智能复盘系统交流群）
- **微信群**: 扫描二维码加入（群满时请联系管理员）
- **邮件**: <EMAIL>

### 🐛 问题反馈
- **Bug报告**: [GitHub Issues](https://github.com/your-repo/issues)
- **功能建议**: [Feature Requests](https://github.com/your-repo/issues/new?template=feature_request.md)
- **使用问题**: [Q&A Discussion](https://github.com/your-repo/discussions/categories/q-a)

### 📈 项目统计
- ⭐ GitHub Stars: ![GitHub stars](https://img.shields.io/github/stars/your-repo/intelligent-review-system)
- 🍴 Forks: ![GitHub forks](https://img.shields.io/github/forks/your-repo/intelligent-review-system)
- 📥 Downloads: ![GitHub downloads](https://img.shields.io/github/downloads/your-repo/intelligent-review-system/total)
- 🐛 Issues: ![GitHub issues](https://img.shields.io/github/issues/your-repo/intelligent-review-system)

---

## 🎉 致谢

感谢每一位使用智能复盘系统的朋友，是你们的支持和反馈让这个项目不断完善。

**特别致谢**：
- 感谢Obsidian团队创造了如此优秀的知识管理平台
- 感谢开源社区提供的各种优秀插件和工具
- 感谢所有提供反馈和建议的用户
- 感谢每一位为项目做出贡献的开发者

让我们一起，通过系统化的复盘，成为更好的自己！ 🌟

---

*📝 文档版本：v2.0 | 最后更新：2025-08-07 | 📖 [查看完整更新历史](CHANGELOG.md)*
