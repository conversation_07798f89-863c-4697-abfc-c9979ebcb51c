---
tags:
  - type/dashboard
  - dashboard/notes-search
  - system/knowledge-management
created: 2025-07-11T10:00
updated: 2025-07-11T10:00
---

# 🔍 交互式笔记搜索检索系统

## 📝 智能全局搜索

```dataviewjs
// ===== 交互式笔记搜索检索系统 =====
const container = this.container;

// 创建主容器
const mainContainer = document.createElement('div');
mainContainer.style.cssText = `
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
`;

// ===== 快速搜索面板 =====
const quickContainer = document.createElement('div');
quickContainer.style.cssText = `
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 20px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
`;

const quickTitle = document.createElement('h3');
quickTitle.textContent = '⚡ 快速搜索';
quickTitle.style.cssText = 'margin: 0 0 15px 0; font-size: 18px;';

const quickSearchSection = document.createElement('div');
quickSearchSection.innerHTML = `
    <div style="margin-bottom: 15px;">
        <span style="font-weight: bold; margin-right: 10px;">🔥 热门关键词：</span>
        <button data-quick-search="MCP" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 6px 12px; border-radius: 15px; cursor: pointer; margin: 2px;">MCP</button>
        <button data-quick-search="Obsidian" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 6px 12px; border-radius: 15px; cursor: pointer; margin: 2px;">Obsidian</button>
        <button data-quick-search="配置" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 6px 12px; border-radius: 15px; cursor: pointer; margin: 2px;">配置</button>
        <button data-quick-search="复盘" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 6px 12px; border-radius: 15px; cursor: pointer; margin: 2px;">复盘</button>
        <button data-quick-search="AI" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 6px 12px; border-radius: 15px; cursor: pointer; margin: 2px;">AI</button>
        <button data-quick-search="技术" style="background: rgba(255,255,255,0.2); color: white; border: none; padding: 6px 12px; border-radius: 15px; cursor: pointer; margin: 2px;">技术</button>
    </div>
`;

quickContainer.appendChild(quickTitle);
quickContainer.appendChild(quickSearchSection);
mainContainer.appendChild(quickContainer);

// ===== 搜索控制面板 =====
const searchSection = document.createElement('div');
searchSection.style.cssText = `
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #e9ecef;
`;

const searchTitle = document.createElement('h3');
searchTitle.textContent = '🔍 高级搜索';
searchTitle.style.cssText = 'margin: 0 0 15px 0; color: #495057;';

// 搜索输入框
const searchContainer = document.createElement('div');
searchContainer.style.cssText = 'margin-bottom: 15px; display: flex; align-items: center; gap: 10px; flex-wrap: wrap;';

const searchLabel = document.createElement('label');
searchLabel.textContent = '🔎 关键词: ';
searchLabel.style.cssText = 'font-weight: bold; min-width: 80px;';

const searchInput = document.createElement('input');
searchInput.id = 'notes-search-input';
searchInput.type = 'text';
searchInput.placeholder = '输入关键词，支持多个关键词用空格分隔...';
searchInput.style.cssText = 'flex: 1; min-width: 300px; padding: 10px; border-radius: 6px; border: 1px solid #ddd; font-size: 14px;';

// 搜索模式选择
const searchModeLabel = document.createElement('label');
searchModeLabel.textContent = '模式: ';
searchModeLabel.style.cssText = 'font-weight: bold; margin-left: 10px;';

const searchModeSelector = document.createElement('select');
searchModeSelector.id = 'search-mode';
searchModeSelector.style.cssText = 'padding: 8px; border-radius: 4px; border: 1px solid #ddd;';

const searchModes = [
    { value: 'AND', text: 'AND (所有)' },
    { value: 'OR', text: 'OR (任一)' },
    { value: 'EXACT', text: '精确匹配' }
];

searchModes.forEach(mode => {
    const option = document.createElement('option');
    option.value = mode.value;
    option.text = mode.text;
    searchModeSelector.appendChild(option);
});

searchContainer.appendChild(searchLabel);
searchContainer.appendChild(searchInput);
searchContainer.appendChild(searchModeLabel);
searchContainer.appendChild(searchModeSelector);

// 搜索范围选择
const scopeContainer = document.createElement('div');
scopeContainer.style.cssText = 'margin-bottom: 15px; display: flex; align-items: center; gap: 10px; flex-wrap: wrap;';

const scopeLabel = document.createElement('label');
scopeLabel.textContent = '📂 搜索范围: ';
scopeLabel.style.cssText = 'font-weight: bold; min-width: 80px;';

const scopeSelector = document.createElement('select');
scopeSelector.id = 'search-scope';
scopeSelector.style.cssText = 'padding: 8px; border-radius: 4px; border: 1px solid #ddd; min-width: 150px;';

const searchScopes = [
    { value: 'all', text: '全部内容' },
    { value: 'filename', text: '仅文件名' },
    { value: 'content', text: '仅文件内容' }
];

searchScopes.forEach(scope => {
    const option = document.createElement('option');
    option.value = scope.value;
    option.text = scope.text;
    scopeSelector.appendChild(option);
});

scopeContainer.appendChild(scopeLabel);
scopeContainer.appendChild(scopeSelector);

// 搜索按钮
const buttonContainer = document.createElement('div');
buttonContainer.style.cssText = 'text-align: center; margin-top: 15px;';

const searchButton = document.createElement('button');
searchButton.textContent = '🔍 开始搜索';
searchButton.style.cssText = `
    background: #007bff;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    margin-right: 10px;
`;

const clearButton = document.createElement('button');
clearButton.textContent = '🗑️ 清空结果';
clearButton.style.cssText = `
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
    margin-right: 10px;
`;

const historyButton = document.createElement('button');
historyButton.textContent = '📚 搜索历史';
historyButton.style.cssText = `
    background: #28a745;
    color: white;
    border: none;
    padding: 12px 24px;
    border-radius: 6px;
    font-size: 16px;
    cursor: pointer;
`;

buttonContainer.appendChild(searchButton);
buttonContainer.appendChild(clearButton);
buttonContainer.appendChild(historyButton);

searchSection.appendChild(searchTitle);
searchSection.appendChild(searchContainer);
searchSection.appendChild(scopeContainer);
searchSection.appendChild(buttonContainer);
mainContainer.appendChild(searchSection);

// ===== 搜索结果区域 =====
const resultSection = document.createElement('div');
resultSection.style.cssText = 'margin-bottom: 20px;';

const resultHeader = document.createElement('h3');
resultHeader.textContent = '📋 搜索结果';
resultHeader.style.cssText = 'margin: 0 0 15px 0; color: #495057;';

const resultContainer = document.createElement('div');
resultContainer.id = 'notes-search-results';
resultContainer.style.cssText = `
    min-height: 200px;
    max-height: 500px;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 20px;
    overflow-y: auto;
    background: white;
`;

resultSection.appendChild(resultHeader);
resultSection.appendChild(resultContainer);
mainContainer.appendChild(resultSection);

container.appendChild(mainContainer);

// ===== 搜索历史管理 =====
const SEARCH_HISTORY_KEY = 'notes-search-history';
const MAX_HISTORY_ITEMS = 10;

function saveSearchHistory(keywords, mode, scope, resultCount) {
    try {
        const history = getSearchHistory();
        const newItem = {
            keywords: keywords.join(' '),
            mode: mode,
            scope: scope,
            resultCount: resultCount,
            timestamp: new Date().toISOString()
        };

        // 避免重复项
        const existingIndex = history.findIndex(item =>
            item.keywords === newItem.keywords &&
            item.mode === newItem.mode &&
            item.scope === newItem.scope
        );

        if (existingIndex !== -1) {
            history.splice(existingIndex, 1);
        }

        history.unshift(newItem);

        // 限制历史记录数量
        if (history.length > MAX_HISTORY_ITEMS) {
            history.splice(MAX_HISTORY_ITEMS);
        }

        localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(history));
    } catch (error) {
        console.error('保存搜索历史失败:', error);
    }
}

function getSearchHistory() {
    try {
        const history = localStorage.getItem(SEARCH_HISTORY_KEY);
        return history ? JSON.parse(history) : [];
    } catch (error) {
        console.error('获取搜索历史失败:', error);
        return [];
    }
}

function showSearchHistory() {
    const history = getSearchHistory();
    const resultContainer = document.getElementById('notes-search-results');

    if (history.length === 0) {
        resultContainer.innerHTML = `
            <div style="text-align: center; color: #6c757d; padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📚</div>
                <div style="font-size: 18px; margin-bottom: 5px;">暂无搜索历史</div>
                <div style="font-size: 14px;">开始搜索后会自动记录历史</div>
            </div>
        `;
        return;
    }

    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: #e8f5e8; border-radius: 6px; border-left: 4px solid #28a745;">
            <strong>📚 搜索历史记录</strong>
            <span style="margin-left: 10px; color: #666;">最近 ${history.length} 次搜索</span>
        </div>
    `;

    history.forEach((item, index) => {
        const date = new Date(item.timestamp).toLocaleString('zh-CN');
        const modeText = item.mode === 'AND' ? 'AND(所有)' : item.mode === 'OR' ? 'OR(任一)' : '精确匹配';
        const scopeText = item.scope === 'all' ? '全部内容' : item.scope === 'filename' ? '仅文件名' : '仅内容';

        html += `
            <div style="border: 1px solid #e9ecef; border-radius: 8px; margin-bottom: 10px; padding: 15px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="font-weight: bold; color: #2c3e50; font-size: 14px;">${item.keywords}</span>
                        <span style="margin-left: 10px; font-size: 12px; color: #6c757d;">
                            ${modeText} | ${scopeText} | ${item.resultCount} 个结果
                        </span>
                    </div>
                    <button onclick="replaySearch('${item.keywords}', '${item.mode}', '${item.scope}')"
                            style="background: #007bff; color: white; border: none; padding: 4px 8px; border-radius: 4px; cursor: pointer; font-size: 12px;">
                        🔄 重新搜索
                    </button>
                </div>
                <div style="font-size: 11px; color: #6c757d;">
                    📅 ${date}
                </div>
            </div>
        `;
    });

    html += `
        <div style="text-align: center; margin-top: 15px;">
            <button onclick="clearSearchHistory()" style="background: #dc3545; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
                🗑️ 清空历史记录
            </button>
        </div>
    `;

    resultContainer.innerHTML = html;
}

// 全局函数，供HTML调用
window.replaySearch = function(keywords, mode, scope) {
    document.getElementById('notes-search-input').value = keywords;
    document.getElementById('search-mode').value = mode;
    document.getElementById('search-scope').value = scope;
    performSearchEnhanced();
};

window.clearSearchHistory = function() {
    if (confirm('确定要清空所有搜索历史记录吗？')) {
        localStorage.removeItem(SEARCH_HISTORY_KEY);
        showSearchHistory();
    }
};

// ===== 核心搜索函数 =====
async function getAllNotesFiles() {
    try {
        console.log('开始获取笔记文件列表...');

        // 方法1：直接查询 notes 文件夹
        let notesPages;
        try {
            notesPages = dv.pages('"notes"');
            console.log('方法1 - 直接查询，找到页面数量:', notesPages.length);
        } catch (e1) {
            console.log('方法1失败，尝试方法2:', e1);
            // 方法2：查询所有页面然后过滤
            try {
                const allPages = dv.pages();
                notesPages = allPages.where(p => p.file.path.includes('notes/'));
                console.log('方法2 - 过滤查询，找到页面数量:', notesPages.length);
            } catch (e2) {
                console.log('方法2失败，尝试方法3:', e2);
                // 方法3：获取所有页面，手动过滤
                const allPages = dv.pages();
                notesPages = allPages.filter(p => p.file.path.includes('notes/'));
                console.log('方法3 - 手动过滤，找到页面数量:', notesPages.length);
            }
        }

        // 打印前几个文件用于调试
        if (notesPages.length > 0) {
            console.log('前3个文件:');
            notesPages.slice(0, 3).forEach((page, index) => {
                console.log(`${index + 1}. ${page.file.path} - ${page.file.name}`);
            });
        }

        return notesPages.map(page => ({
            name: page.file.name,
            path: page.file.path,
            link: page.file.link,
            size: page.file.size || 0,
            ctime: page.file.ctime,
            mtime: page.file.mtime
        }));
    } catch (error) {
        console.error('获取笔记文件完全失败:', error);
        return [];
    }
}

async function searchInNotes(keywords, searchMode, searchScope) {
    const allFiles = await getAllNotesFiles();
    const results = [];
    
    for (const file of allFiles) {
        try {
            const content = await dv.io.load(file.path);
            const fileName = file.name.replace('.md', '');
            
            let matchResult = false;
            let matchedContent = '';
            let matchType = '';
            
            // 根据搜索范围进行匹配
            if (searchScope === 'all' || searchScope === 'filename') {
                const fileNameMatch = checkKeywordMatch(fileName, keywords, searchMode);
                if (fileNameMatch.matched) {
                    matchResult = true;
                    matchType = '文件名';
                    matchedContent = fileName;
                }
            }
            
            if (!matchResult && (searchScope === 'all' || searchScope === 'content')) {
                const contentMatch = checkKeywordMatch(content, keywords, searchMode);
                if (contentMatch.matched) {
                    matchResult = true;
                    matchType = '文件内容';
                    matchedContent = extractMatchedSnippet(content, keywords);
                }
            }
            
            if (matchResult) {
                results.push({
                    file: file,
                    matchType: matchType,
                    snippet: matchedContent,
                    keywords: keywords
                });
            }
        } catch (error) {
            console.error(`读取文件 ${file.path} 失败:`, error);
        }
    }
    
    return results;
}

function checkKeywordMatch(text, keywords, mode) {
    const lowerText = text.toLowerCase();
    const lowerKeywords = keywords.map(k => k.toLowerCase());
    
    switch (mode) {
        case 'AND':
            return { matched: lowerKeywords.every(keyword => lowerText.includes(keyword)) };
        case 'OR':
            return { matched: lowerKeywords.some(keyword => lowerText.includes(keyword)) };
        case 'EXACT':
            const exactPhrase = keywords.join(' ').toLowerCase();
            return { matched: lowerText.includes(exactPhrase) };
        default:
            return { matched: false };
    }
}

function extractMatchedSnippet(content, keywords, maxLength = 200) {
    const lines = content.split('\n');
    const lowerKeywords = keywords.map(k => k.toLowerCase());
    
    for (const line of lines) {
        const lowerLine = line.toLowerCase();
        if (lowerKeywords.some(keyword => lowerLine.includes(keyword))) {
            if (line.length <= maxLength) {
                return line.trim();
            } else {
                return line.substring(0, maxLength).trim() + '...';
            }
        }
    }
    
    return content.substring(0, maxLength).trim() + '...';
}

// ===== 结果显示函数 =====
function displaySearchResults(results, keywords) {
    const resultContainer = document.getElementById('notes-search-results');
    
    if (results.length === 0) {
        resultContainer.innerHTML = `
            <div style="text-align: center; color: #6c757d; padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">未找到匹配的笔记</div>
                <div style="font-size: 14px;">尝试使用不同的关键词或搜索模式</div>
            </div>
        `;
        return;
    }
    
    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: #e3f2fd; border-radius: 6px; border-left: 4px solid #2196f3;">
            <strong>🎯 找到 ${results.length} 个匹配结果</strong>
            <span style="margin-left: 10px; color: #666;">关键词: ${keywords.join(', ')}</span>
        </div>
    `;
    
    results.forEach((result, index) => {
        const file = result.file;
        const modifiedDate = new Date(file.mtime).toLocaleDateString('zh-CN');
        
        html += `
            <div style="border: 1px solid #e9ecef; border-radius: 8px; margin-bottom: 15px; padding: 15px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 10px;">
                    <div style="flex: 1;">
                        <h4 style="margin: 0 0 5px 0; color: #2c3e50;">
                            <a href="${file.link}" style="text-decoration: none; color: #007bff; font-weight: bold;">
                                📄 ${file.name.replace('.md', '')}
                            </a>
                        </h4>
                        <div style="font-size: 12px; color: #6c757d; margin-bottom: 8px;">
                            <span style="margin-right: 15px;">📅 ${modifiedDate}</span>
                            <span style="margin-right: 15px;">🔍 匹配: ${result.matchType}</span>
                            <span>📏 ${Math.round(file.size / 1024)}KB</span>
                        </div>
                    </div>
                </div>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; border-left: 3px solid #28a745;">
                    <div style="font-size: 13px; color: #495057; line-height: 1.4;">
                        ${highlightKeywords(result.snippet, keywords)}
                    </div>
                </div>
            </div>
        `;
    });
    
    resultContainer.innerHTML = html;
}

function highlightKeywords(text, keywords) {
    let highlightedText = text;
    keywords.forEach(keyword => {
        const regex = new RegExp(`(${keyword})`, 'gi');
        highlightedText = highlightedText.replace(regex, '<mark style="background: #fff3cd; padding: 1px 2px;">$1</mark>');
    });
    return highlightedText;
}



// ===== 相关内容推荐 =====
async function findRelatedNotes(currentFile, allFiles) {
    const relatedNotes = [];

    try {
        const currentContent = await dv.io.load(currentFile.path);
        const currentWords = extractKeywords(currentContent);

        for (const file of allFiles) {
            if (file.path === currentFile.path) continue;

            try {
                const content = await dv.io.load(file.path);
                const fileWords = extractKeywords(content);
                const similarity = calculateSimilarity(currentWords, fileWords);

                if (similarity > 0.1) {
                    relatedNotes.push({
                        file: file,
                        similarity: similarity
                    });
                }
            } catch (error) {
                console.error(`处理相关文件 ${file.path} 失败:`, error);
            }
        }

        return relatedNotes
            .sort((a, b) => b.similarity - a.similarity)
            .slice(0, 5);
    } catch (error) {
        console.error('查找相关笔记失败:', error);
        return [];
    }
}

function extractKeywords(content) {
    // 简单的关键词提取，去除常见停用词
    const stopWords = ['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'];
    const words = content
        .toLowerCase()
        .replace(/[^\u4e00-\u9fa5a-zA-Z0-9\s]/g, ' ')
        .split(/\s+/)
        .filter(word => word.length > 1 && !stopWords.includes(word));

    const wordCount = {};
    words.forEach(word => {
        wordCount[word] = (wordCount[word] || 0) + 1;
    });

    return Object.keys(wordCount)
        .sort((a, b) => wordCount[b] - wordCount[a])
        .slice(0, 20);
}

function calculateSimilarity(words1, words2) {
    const set1 = new Set(words1);
    const set2 = new Set(words2);
    const intersection = new Set([...set1].filter(x => set2.has(x)));
    const union = new Set([...set1, ...set2]);

    return intersection.size / union.size;
}

// ===== 增强的结果显示函数 =====
async function displaySearchResultsWithRelated(results, keywords) {
    const resultContainer = document.getElementById('notes-search-results');

    if (results.length === 0) {
        resultContainer.innerHTML = `
            <div style="text-align: center; color: #6c757d; padding: 40px;">
                <div style="font-size: 48px; margin-bottom: 10px;">📭</div>
                <div style="font-size: 18px; margin-bottom: 5px;">未找到匹配的笔记</div>
                <div style="font-size: 14px;">尝试使用不同的关键词或搜索模式</div>
            </div>
        `;
        return;
    }

    const allFiles = await getAllNotesFiles();

    let html = `
        <div style="margin-bottom: 15px; padding: 10px; background: #e3f2fd; border-radius: 6px; border-left: 4px solid #2196f3;">
            <strong>🎯 找到 ${results.length} 个匹配结果</strong>
            <span style="margin-left: 10px; color: #666;">关键词: ${keywords.join(', ')}</span>
        </div>
    `;

    for (let i = 0; i < results.length; i++) {
        const result = results[i];
        const file = result.file;
        const modifiedDate = new Date(file.mtime).toLocaleDateString('zh-CN');

        // 获取相关笔记
        const relatedNotes = await findRelatedNotes(file, allFiles);

        html += `
            <div style="border: 1px solid #e9ecef; border-radius: 8px; margin-bottom: 15px; background: white; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                <div style="padding: 15px; border-bottom: 1px solid #f1f3f4;">
                    <div style="display: flex; justify-content: between; align-items: flex-start; margin-bottom: 10px;">
                        <div style="flex: 1;">
                            <h4 style="margin: 0 0 5px 0; color: #2c3e50;">
                                <a href="${file.link}" style="text-decoration: none; color: #007bff; font-weight: bold;">
                                    📄 ${file.name.replace('.md', '')}
                                </a>
                            </h4>
                            <div style="font-size: 12px; color: #6c757d; margin-bottom: 8px;">
                                <span style="margin-right: 15px;">📅 ${modifiedDate}</span>
                                <span style="margin-right: 15px;">🔍 匹配: ${result.matchType}</span>
                                <span>📏 ${Math.round(file.size / 1024)}KB</span>
                            </div>
                        </div>
                    </div>
                    <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; border-left: 3px solid #28a745;">
                        <div style="font-size: 13px; color: #495057; line-height: 1.4;">
                            ${highlightKeywords(result.snippet, keywords)}
                        </div>
                    </div>
                </div>
        `;

        // 添加相关内容区域
        if (relatedNotes.length > 0) {
            html += `
                <div style="padding: 10px 15px; background: #f8f9fa;">
                    <div style="font-size: 12px; font-weight: bold; color: #6c757d; margin-bottom: 8px;">🔗 相关笔记</div>
                    <div style="display: flex; flex-wrap: wrap; gap: 8px;">
            `;

            relatedNotes.forEach(related => {
                const similarityPercent = Math.round(related.similarity * 100);
                html += `
                    <a href="${related.file.link}" style="
                        text-decoration: none;
                        background: white;
                        border: 1px solid #dee2e6;
                        border-radius: 4px;
                        padding: 4px 8px;
                        font-size: 11px;
                        color: #495057;
                        display: inline-block;
                        transition: all 0.2s;
                    " onmouseover="this.style.background='#e9ecef'" onmouseout="this.style.background='white'">
                        📎 ${related.file.name.replace('.md', '')} (${similarityPercent}%)
                    </a>
                `;
            });

            html += `
                    </div>
                </div>
            `;
        }

        html += `</div>`;
    }

    resultContainer.innerHTML = html;
}

// 更新搜索函数以使用增强的显示功能
async function performSearchEnhanced() {
    console.log('开始搜索...');

    const searchInput = document.getElementById('notes-search-input');
    const searchMode = document.getElementById('search-mode');
    const searchScope = document.getElementById('search-scope');

    console.log('搜索输入框:', searchInput);
    console.log('搜索模式:', searchMode);
    console.log('搜索范围:', searchScope);

    if (!searchInput || !searchMode || !searchScope) {
        console.error('找不到搜索控件');
        alert('搜索控件初始化失败，请刷新页面重试');
        return;
    }

    const keywords = searchInput.value.trim().split(/\s+/).filter(k => k);
    console.log('搜索关键词:', keywords);

    if (keywords.length === 0) {
        alert('请输入搜索关键词');
        return;
    }

    // 显示加载状态
    const resultContainer = document.getElementById('notes-search-results');
    resultContainer.innerHTML = `
        <div style="text-align: center; padding: 40px; color: #6c757d;">
            <div style="font-size: 24px; margin-bottom: 10px;">🔄</div>
            <div>正在搜索中...</div>
        </div>
    `;

    try {
        const results = await searchInNotes(keywords, searchMode.value, searchScope.value);
        await displaySearchResultsWithRelated(results, keywords);

        // 保存搜索历史
        saveSearchHistory(keywords, searchMode.value, searchScope.value, results.length);
    } catch (error) {
        console.error('搜索失败:', error);
        resultContainer.innerHTML = `
            <div style="text-align: center; padding: 40px; color: #dc3545;">
                <div style="font-size: 24px; margin-bottom: 10px;">❌</div>
                <div>搜索失败，请重试</div>
            </div>
        `;
    }
}

// 更新事件监听器以使用增强的搜索功能
function addEventListeners() {
    // 搜索按钮事件 - 修复选择器
    const searchButton = document.querySelector('button');
    const buttons = document.querySelectorAll('button');
    let searchBtn = null;
    let clearBtn = null;
    let historyBtn = null;

    buttons.forEach(btn => {
        if (btn.textContent.includes('🔍 开始搜索')) {
            searchBtn = btn;
        } else if (btn.textContent.includes('🗑️ 清空结果')) {
            clearBtn = btn;
        } else if (btn.textContent.includes('📚 搜索历史')) {
            historyBtn = btn;
        }
    });

    if (searchBtn) {
        searchBtn.addEventListener('click', performSearchEnhanced);
    }

    if (clearBtn) {
        clearBtn.addEventListener('click', clearResults);
    }

    if (historyBtn) {
        historyBtn.addEventListener('click', showSearchHistory);
    }

    // 快速搜索按钮事件
    document.querySelectorAll('[data-quick-search]').forEach(button => {
        button.addEventListener('click', (e) => {
            const keyword = e.target.getAttribute('data-quick-search');
            document.getElementById('notes-search-input').value = keyword;
            performSearchEnhanced();
        });
    });

    // 回车键搜索
    const searchInput = document.getElementById('notes-search-input');
    if (searchInput) {
        searchInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                performSearchEnhanced();
            }
        });
    }
}

function clearResults() {
    document.getElementById('notes-search-input').value = '';
    document.getElementById('notes-search-results').innerHTML = `
        <div style="text-align: center; color: #6c757d; padding: 40px;">
            <div style="font-size: 48px; margin-bottom: 10px;">🔍</div>
            <div style="font-size: 18px; margin-bottom: 5px;">准备开始搜索</div>
            <div style="font-size: 14px;">输入关键词并点击搜索按钮</div>
        </div>
    `;
}

// 初始化
setTimeout(() => {
    addEventListeners();
    clearResults();
}, 100);
```

## 📖 使用说明

### 🎯 功能特点
1. **快速搜索**：顶部提供热门关键词一键搜索
2. **全局检索**：同时搜索文件名和文件内容
3. **智能匹配**：支持AND/OR/精确匹配三种模式
4. **灵活范围**：可选择搜索全部内容、仅文件名或仅内容
5. **可点击跳转**：搜索结果直接链接到原始笔记
6. **内容预览**：显示匹配的内容片段，关键词高亮显示
7. **相关内容推荐**：基于内容相似度自动推荐相关笔记
8. **智能关联**：显示相关笔记的相似度百分比

### 🔍 搜索技巧
- **多关键词**：用空格分隔多个关键词
- **逻辑运算**：
  - AND(所有)：必须包含所有关键词
  - OR(任一)：包含任意一个关键词即可
  - 精确匹配：完全匹配输入的短语
- **搜索范围**：
  - 全部内容：同时搜索文件名和内容
  - 仅文件名：只在文件名中搜索
  - 仅文件内容：只在文件内容中搜索

### ⚡ 快速操作
- **热门关键词**：MCP、Obsidian、配置、复盘、AI、技术等一键搜索
- **回车搜索**：在搜索框中按回车键快速搜索
- **清空结果**：一键清空搜索结果和输入框
- **搜索历史**：自动记录最近10次搜索，支持一键重新搜索
- **历史管理**：查看搜索历史，支持清空历史记录

### 📋 结果展示
- **匹配统计**：显示找到的结果数量和搜索关键词
- **文件信息**：文件名、修改日期、匹配类型、文件大小
- **内容预览**：显示匹配的内容片段，关键词高亮
- **直接跳转**：点击文件名直接跳转到原始笔记
- **相关推荐**：每个搜索结果下方显示最多5个相关笔记
- **相似度指标**：显示相关笔记与当前笔记的相似度百分比
- **快速导航**：相关笔记支持一键跳转，增强发现性

### 🎨 界面设计
- **紧凑布局**：参考现有系统的卡片式设计
- **视觉层次**：清晰的信息层次和视觉引导
- **响应式**：适配不同屏幕尺寸
- **一致性**：与现有知识管理系统保持一致的设计风格
