<!-- Generated by SpecStory -->

# 翻译任务命令文件内容 (2025-07-04 07:57Z)

_**User**_

请翻译文件 `任务命令.txt` 中的英文内容为中文，遵循以下规则：
1. **翻译范围**：仅翻译英文文本内容，保持以下内容不变：
   - 代码片段（包括变量名、函数名、类名等）
   - 技术概念的专有名词（如API、JSON、HTTP等）
   - 文件路径和URL
   - 命令行指令
2. **翻译质量**：确保翻译准确、自然，符合中文表达习惯
3. **格式保持**：保持原文档的格式结构（如标题层级、列表、代码块等）
4. **输出要求**：将翻译后的完整内容保存为新文件，文件名格式为 `任务命令_中文版` 或 `任务命令_翻译`，用Markdown格式输出
5. **存储位置**：保存在当前工作目录或用户指定的位置

请先读取原文件内容，然后按照上述要求进行翻译和保存。

---

_**Assistant**_



---

<details>
            <summary>Searched codebase "任务命令.txt" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---



---

Read file: 任务命令.txt

---

<think><details><summary>Thought Process</summary>**Translating file content**

The file contains 32 lines, and the next step is to translate the English content into Chinese while preserving the formatting. I need to keep code snippets, keywords, and certain technical nouns intact, like API, JSON, and HTTP, although they aren't in this file. Commands like `/project:ultrathink-task <TASK_DESCRIPTION>` and titles like Architect Agent should be preserved as well. I'll create a new file named `任务命令_中文版.md` to save the translated text. Let's begin the translation!</details></think>

---

```diff
- 
+  
```

---

<think><details><summary>Thought Process</summary>**Verifying file changes**

I'm noticing that the differences might not be correct. It looks like the changes in the file weren't inserted properly, and the model might have replaced the content incorrectly. The diff only shows the addition of whitespace changes, which is odd. I think I need to reapply the changes to fix this issue. So, I'll open the new file content to verify what's actually going on. Let's check it!</details></think>

---

Read file: 任务命令_中文版.md

---



---

```diff
-  
+ Ultrathink任务（命令）
+ 
+ ## 用法
+ 
+ `/project:ultrathink-task <TASK_DESCRIPTION>`
+ 
+ ## 上下文
+ 
+ - 任务描述：$ARGUMENTS
+ - 相关代码或文件将通过 `@file` 语法临时引用。
+ 
+ ## 你的角色
+ 
+ 你是 **Coordinator Agent**，负责协调四个专业子代理：
+ 1. **Architect Agent** – 设计高级方案。
+ 2. **Research Agent** – 收集外部知识与先例。
+ 3. **Coder Agent** – 编写或修改代码。
+ 4. **Tester Agent** – 提出测试与验证策略。
+ 
+ ## 流程
+ 
+ 1. 按 **step-by-step** 思考，列出假设与未知。
+ 2. 针对每个子代理，清晰分派任务，记录其输出，并总结洞见。
+ 3. 执行一次 “ultrathink” 反思阶段，将所有洞见整合成一致的解决方案。
+ 4. 如仍存在空缺，继续迭代（再次启动子代理）直至确信无疑。
+ 
+ ## 输出格式
+ 
+ 1. **推理记录**（可选但推荐） – 展示主要决策点。
+ 2. **最终答案** – 以Markdown呈现可执行步骤、代码修改或命令。
+ 3. **后续行动** – 团队的后续事项 bullet 列表（如有）。
+ 
```

---



---

Read file: 任务命令_中文版.md

---



---



---

[模式：快速]  
✅ 已完成 English → 中文 翻译，并保存为 `任务命令_中文版.md`（Markdown 格式，位于当前目录）。  
如需进一步修改或移动文件位置，请告知！

---

_**User**_

基于以上，可以把翻译做成记忆约定吗

---

_**Assistant**_



---

