---
tags:
  - type/dashboard
  - dashboard/mvp-search
  - obsidian/search
created: 2025-07-11T15:30
updated: 2025-07-11T15:30
---

# 🔧 MVP搜索系统 v1.3 - 高级版

```dataviewjs
// MVP搜索系统 - 专门解决Obsidian崩溃问题
const container = this.container;

// 清理容器
container.innerHTML = '';

// 创建主容器
const mainDiv = document.createElement('div');
mainDiv.style.cssText = `
    font-family: var(--font-interface);
    max-width: 700px;
    margin: 0 auto;
    padding: 15px;
`;

// 搜索区域
const searchDiv = document.createElement('div');
searchDiv.style.cssText = `
    background: var(--background-secondary);
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 15px;
    border: 1px solid var(--background-modifier-border);
`;

// 标题
const title = document.createElement('h3');
title.textContent = '🔧 MVP搜索系统 v1.3 (高级版)';
title.style.cssText = 'margin: 0 0 10px 0; color: var(--text-normal); font-size: 16px;';

// 搜索输入容器（支持建议下拉）
const searchContainer = document.createElement('div');
searchContainer.style.cssText = 'position: relative; margin-bottom: 10px;';

// 搜索输入
const searchInput = document.createElement('input');
searchInput.type = 'text';
searchInput.placeholder = '输入关键词搜索... (支持智能建议)';
searchInput.style.cssText = `
    width: 100%;
    padding: 8px 10px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
    box-sizing: border-box;
`;

// 搜索建议下拉框
const suggestionsDiv = document.createElement('div');
suggestionsDiv.style.cssText = `
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--background-primary);
    border: 1px solid var(--background-modifier-border);
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 150px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
`;

searchContainer.appendChild(searchInput);
searchContainer.appendChild(suggestionsDiv);

// 选项区域
const optionsDiv = document.createElement('div');
optionsDiv.style.cssText = 'display: flex; gap: 8px; margin-bottom: 10px; flex-wrap: wrap;';

// 搜索模式选择
const modeSelect = document.createElement('select');
modeSelect.style.cssText = `
    padding: 6px 8px;
    border: 1px solid var(--background-modifier-border);
    border-radius: 4px;
    background: var(--background-primary);
    color: var(--text-normal);
    font-size: 12px;
`;

const modes = [
    { value: 'OR', text: 'OR (任一)' },
    { value: 'AND', text: 'AND (所有)' },
    { value: 'EXACT', text: '精确匹配' }
];

modes.forEach(mode => {
    const option = document.createElement('option');
    option.value = mode.value;
    option.textContent = mode.text;
    modeSelect.appendChild(option);
});

// 搜索范围选择
const scopeSelect = document.createElement('select');
scopeSelect.style.cssText = modeSelect.style.cssText;

const scopes = [
    { value: 'filename', text: '仅文件名' },
    { value: 'content', text: '仅内容' },
    { value: 'all', text: '全部' }
];

scopes.forEach(scope => {
    const option = document.createElement('option');
    option.value = scope.value;
    option.textContent = scope.text;
    scopeSelect.appendChild(option);
});

// 文件类型过滤
const typeSelect = document.createElement('select');
typeSelect.style.cssText = modeSelect.style.cssText;

const types = [
    { value: 'all', text: '所有类型' },
    { value: 'md', text: 'Markdown' },
    { value: 'image', text: '图片' },
    { value: 'pdf', text: 'PDF' },
    { value: 'other', text: '其他' }
];

types.forEach(type => {
    const option = document.createElement('option');
    option.value = type.value;
    option.textContent = type.text;
    typeSelect.appendChild(option);
});

// 排序方式选择
const sortSelect = document.createElement('select');
sortSelect.style.cssText = modeSelect.style.cssText;

const sorts = [
    { value: 'name', text: '按名称' },
    { value: 'modified', text: '按修改时间' },
    { value: 'size', text: '按大小' },
    { value: 'relevance', text: '按相关性' }
];

sorts.forEach(sort => {
    const option = document.createElement('option');
    option.value = sort.value;
    option.textContent = sort.text;
    sortSelect.appendChild(option);
});

optionsDiv.appendChild(modeSelect);
optionsDiv.appendChild(scopeSelect);
optionsDiv.appendChild(typeSelect);
optionsDiv.appendChild(sortSelect);

// 搜索按钮
const searchBtn = document.createElement('button');
searchBtn.textContent = '🔍 搜索';
searchBtn.style.cssText = `
    background: var(--interactive-accent);
    color: var(--text-on-accent);
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    margin-right: 8px;
    font-size: 13px;
`;

// 清空按钮
const clearBtn = document.createElement('button');
clearBtn.textContent = '🗑️ 清空';
clearBtn.style.cssText = `
    background: var(--background-modifier-border);
    color: var(--text-normal);
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    margin-right: 8px;
`;

// 搜索历史按钮
const historyBtn = document.createElement('button');
historyBtn.textContent = '📚 历史';
historyBtn.style.cssText = `
    background: var(--color-blue);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
    margin-right: 8px;
`;

// 统计按钮
const statsBtn = document.createElement('button');
statsBtn.textContent = '📊 统计';
statsBtn.style.cssText = `
    background: var(--color-green);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 13px;
`;

// 状态显示
const statusDiv = document.createElement('div');
statusDiv.style.cssText = 'font-size: 11px; color: var(--text-muted); margin-top: 8px;';
statusDiv.textContent = 'MVP搜索系统 v1.3 准备就绪 - 支持智能建议和搜索历史';

// 组装搜索区域
searchDiv.appendChild(title);
searchDiv.appendChild(searchContainer);
searchDiv.appendChild(optionsDiv);
const buttonContainer = document.createElement('div');
buttonContainer.appendChild(searchBtn);
buttonContainer.appendChild(clearBtn);
buttonContainer.appendChild(historyBtn);
buttonContainer.appendChild(statsBtn);
searchDiv.appendChild(buttonContainer);
searchDiv.appendChild(statusDiv);

// 结果区域
const resultDiv = document.createElement('div');
resultDiv.style.cssText = `
    border: 1px solid var(--background-modifier-border);
    border-radius: 6px;
    padding: 15px;
    background: var(--background-primary);
    min-height: 150px;
`;

const resultContent = document.createElement('div');
resultContent.innerHTML = `
    <div style="text-align: center; color: var(--text-muted); padding: 30px;">
        <div style="font-size: 36px; margin-bottom: 8px;">🔧</div>
        <div style="font-size: 14px;">MVP搜索系统 v1.3 准备就绪</div>
        <div style="font-size: 11px; margin-top: 5px;">支持智能建议和搜索历史</div>
    </div>
`;

resultDiv.appendChild(resultContent);

// 组装主界面
mainDiv.appendChild(searchDiv);
mainDiv.appendChild(resultDiv);
container.appendChild(mainDiv);

// ===== 搜索历史和智能建议功能 =====

// 搜索历史管理
const SEARCH_HISTORY_KEY = 'mvp-search-history-v13';
let searchHistory = JSON.parse(localStorage.getItem(SEARCH_HISTORY_KEY) || '[]');
let searchStats = JSON.parse(localStorage.getItem('mvp-search-stats-v13') || '{}');

// 添加搜索历史
function addToHistory(keyword, mode, scope, fileType, sortBy, resultCount) {
    const historyItem = {
        keyword,
        mode,
        scope,
        fileType,
        sortBy,
        resultCount,
        timestamp: new Date().toISOString(),
        date: new Date().toLocaleDateString('zh-CN')
    };

    // 避免重复
    searchHistory = searchHistory.filter(item =>
        !(item.keyword === keyword && item.mode === mode && item.scope === scope)
    );

    searchHistory.unshift(historyItem);
    searchHistory = searchHistory.slice(0, 20); // 保留最近20条
    localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(searchHistory));

    // 更新统计
    updateSearchStats(keyword);
}

// 更新搜索统计
function updateSearchStats(keyword) {
    const words = keyword.toLowerCase().split(/\s+/);
    words.forEach(word => {
        if (word.length > 1) {
            searchStats[word] = (searchStats[word] || 0) + 1;
        }
    });
    localStorage.setItem('mvp-search-stats-v13', JSON.stringify(searchStats));
}

// 获取搜索建议
function getSearchSuggestions(input) {
    if (!input || input.length < 2) return [];

    const suggestions = [];
    const lowerInput = input.toLowerCase();

    // 1. 历史搜索匹配
    const historyMatches = searchHistory
        .filter(item => item.keyword.toLowerCase().includes(lowerInput))
        .slice(0, 5)
        .map(item => ({
            text: item.keyword,
            type: 'history',
            count: item.resultCount
        }));

    // 2. 热门关键词匹配
    const statsMatches = Object.keys(searchStats)
        .filter(word => word.includes(lowerInput))
        .sort((a, b) => searchStats[b] - searchStats[a])
        .slice(0, 3)
        .map(word => ({
            text: word,
            type: 'popular',
            count: searchStats[word]
        }));

    suggestions.push(...historyMatches);
    suggestions.push(...statsMatches);

    // 去重
    const uniqueSuggestions = suggestions.filter((item, index, self) =>
        index === self.findIndex(t => t.text === item.text)
    );

    return uniqueSuggestions.slice(0, 8);
}

// 显示搜索建议
function showSearchSuggestions(input) {
    const suggestions = getSearchSuggestions(input);

    if (suggestions.length === 0) {
        suggestionsDiv.style.display = 'none';
        return;
    }

    let html = '';
    suggestions.forEach(suggestion => {
        const icon = suggestion.type === 'history' ? '🕒' : '🔥';
        const typeText = suggestion.type === 'history' ? '历史' : '热门';

        html += `
            <div style="padding: 6px 10px; cursor: pointer; border-bottom: 1px solid var(--background-modifier-border); display: flex; justify-content: space-between; align-items: center;"
                 onmouseover="this.style.background='var(--background-modifier-hover)'"
                 onmouseout="this.style.background=''"
                 onclick="selectSuggestion('${suggestion.text}')">
                <span style="font-size: 12px;">
                    ${icon} ${suggestion.text}
                </span>
                <span style="font-size: 10px; color: var(--text-muted);">
                    ${typeText} (${suggestion.count})
                </span>
            </div>
        `;
    });

    suggestionsDiv.innerHTML = html;
    suggestionsDiv.style.display = 'block';
}

// 选择建议
function selectSuggestion(text) {
    searchInput.value = text;
    suggestionsDiv.style.display = 'none';
    performMVPSearch();
}

// 安全的文件打开函数
function safeOpenFile(filePath) {
    try {
        // 方法1：使用Obsidian的内部API
        if (app && app.workspace && app.workspace.openLinkText) {
            app.workspace.openLinkText(filePath, '', false);
            return;
        }
        
        // 方法2：使用文件路径
        if (app && app.vault && app.workspace) {
            const file = app.vault.getAbstractFileByPath(filePath);
            if (file) {
                app.workspace.getLeaf().openFile(file);
                return;
            }
        }
        
        // 方法3：降级处理
        console.log('无法打开文件:', filePath);
        
    } catch (error) {
        console.error('打开文件时出错:', error);
    }
}

// 增强的搜索函数
async function performMVPSearch() {
    const keyword = searchInput.value.trim();
    const mode = modeSelect.value;
    const scope = scopeSelect.value;
    const fileType = typeSelect.value;
    const sortBy = sortSelect.value;
    
    if (!keyword) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 30px;">
                <div style="font-size: 24px; margin-bottom: 8px;">⚠️</div>
                <div style="font-size: 14px;">请输入搜索关键词</div>
            </div>
        `;
        return;
    }
    
    statusDiv.textContent = '正在搜索...';
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 30px;">
            <div style="font-size: 24px; margin-bottom: 8px;">🔄</div>
            <div style="font-size: 14px;">正在搜索中...</div>
        </div>
    `;
    
    try {
        // 获取所有页面并应用文件类型过滤
        let allPages = dv.pages().where(p =>
            !p.file.path.includes('.obsidian') &&
            !p.file.path.includes('Templates')
        );

        // 根据文件类型过滤
        if (fileType !== 'all') {
            allPages = allPages.where(p => {
                const ext = p.file.path.split('.').pop().toLowerCase();
                switch (fileType) {
                    case 'md':
                        return ext === 'md' || ext === 'markdown';
                    case 'image':
                        return ['jpg', 'jpeg', 'png', 'gif', 'svg', 'webp'].includes(ext);
                    case 'pdf':
                        return ext === 'pdf';
                    case 'other':
                        return !['md', 'markdown', 'jpg', 'jpeg', 'png', 'gif', 'svg', 'webp', 'pdf'].includes(ext);
                    default:
                        return true;
                }
            });
        }
        
        const results = [];
        const keywords = keyword.toLowerCase().split(/\s+/);
        let processed = 0;

        // 增强的搜索逻辑
        for (const page of allPages) {
            if (results.length >= 50) break; // 限制结果数量

            try {
                const fileName = page.file.name.replace('.md', '');
                const filePath = page.file.path;
                let content = '';
                let matched = false;
                let matchType = '';

                // 根据搜索范围决定是否读取文件内容
                if (scope === 'content' || scope === 'all') {
                    try {
                        content = await dv.io.load(filePath);
                    } catch (error) {
                        // 如果无法读取内容，跳过内容搜索
                        content = '';
                    }
                }

                // 检查文件名匹配
                if (scope === 'filename' || scope === 'all') {
                    if (isMatch(fileName.toLowerCase(), keywords, mode)) {
                        matched = true;
                        matchType = '文件名';
                    }
                }

                // 检查内容匹配
                if (!matched && (scope === 'content' || scope === 'all')) {
                    if (content && isMatch(content.toLowerCase(), keywords, mode)) {
                        matched = true;
                        matchType = '文件内容';
                    }
                }

                if (matched) {
                    // 计算相关性分数（简单版本）
                    let relevanceScore = 0;
                    if (matchType === '文件名') {
                        relevanceScore += 100; // 文件名匹配权重更高
                    }
                    keywords.forEach(k => {
                        const fileNameMatches = (fileName.toLowerCase().match(new RegExp(k, 'g')) || []).length;
                        const contentMatches = content ? (content.toLowerCase().match(new RegExp(k, 'g')) || []).length : 0;
                        relevanceScore += fileNameMatches * 10 + contentMatches;
                    });

                    results.push({
                        name: fileName,
                        path: filePath,
                        mtime: page.file.mtime,
                        ctime: page.file.ctime,
                        size: page.file.size,
                        matchType: matchType,
                        relevanceScore: relevanceScore,
                        fileExt: filePath.split('.').pop().toLowerCase()
                    });
                }

                processed++;

                // 每处理10个文件更新一次状态
                if (processed % 10 === 0) {
                    statusDiv.textContent = `正在搜索... ${processed} 个文件`;
                    // 让出控制权，保持界面响应
                    await new Promise(resolve => setTimeout(resolve, 1));
                }

            } catch (error) {
                // 跳过有问题的文件
                continue;
            }
        }
        
        // 对结果进行排序
        sortResults(results, sortBy);

        // 添加到搜索历史
        addToHistory(keyword, mode, scope, fileType, sortBy, results.length);

        statusDiv.textContent = `搜索完成，处理了 ${processed} 个文件，找到 ${results.length} 个结果 (${mode}模式, ${scope}范围, ${sortBy}排序)`;
        displayMVPResults(results, keywords, mode, scope, fileType, sortBy);
        
    } catch (error) {
        statusDiv.textContent = '搜索出错';
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-error); padding: 30px;">
                <div style="font-size: 24px; margin-bottom: 8px;">❌</div>
                <div style="font-size: 14px;">搜索失败，请重试</div>
            </div>
        `;
    }
}

// 增强的匹配函数
function isMatch(text, keywords, mode) {
    if (!text || !keywords || keywords.length === 0) return false;

    switch (mode) {
        case 'AND':
            // 所有关键词都必须存在
            return keywords.every(k => text.includes(k));
        case 'OR':
            // 任意一个关键词存在即可
            return keywords.some(k => text.includes(k));
        case 'EXACT':
            // 精确匹配整个短语
            return text.includes(keywords.join(' '));
        default:
            return keywords.some(k => text.includes(k));
    }
}

// 排序函数
function sortResults(results, sortBy) {
    switch (sortBy) {
        case 'name':
            results.sort((a, b) => a.name.localeCompare(b.name));
            break;
        case 'modified':
            results.sort((a, b) => new Date(b.mtime) - new Date(a.mtime));
            break;
        case 'size':
            results.sort((a, b) => (b.size || 0) - (a.size || 0));
            break;
        case 'relevance':
            results.sort((a, b) => b.relevanceScore - a.relevanceScore);
            break;
        default:
            // 默认按名称排序
            results.sort((a, b) => a.name.localeCompare(b.name));
    }
}

// 获取文件类型图标
function getFileIcon(fileExt) {
    const icons = {
        'md': '📝', 'markdown': '📝',
        'jpg': '🖼️', 'jpeg': '🖼️', 'png': '🖼️', 'gif': '🖼️', 'svg': '🖼️', 'webp': '🖼️',
        'pdf': '📄',
        'mp3': '🎵', 'wav': '🎵', 'flac': '🎵',
        'mp4': '🎬', 'avi': '🎬', 'mov': '🎬',
        'js': '💻', 'py': '💻', 'html': '💻', 'css': '💻',
        'txt': '📄', 'doc': '📄', 'docx': '📄'
    };
    return icons[fileExt] || '📄';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (!bytes) return '未知';
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// 专业版结果显示函数
function displayMVPResults(results, keywords, mode, scope, fileType, sortBy) {
    if (results.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 30px;">
                <div style="font-size: 36px; margin-bottom: 8px;">📭</div>
                <div style="font-size: 14px;">未找到匹配的文件</div>
                <div style="font-size: 11px; margin-top: 5px;">尝试其他关键词</div>
            </div>
        `;
        return;
    }
    
    // 搜索模式和范围的描述
    const modeDesc = {
        'OR': 'OR (任一)',
        'AND': 'AND (所有)',
        'EXACT': '精确匹配'
    }[mode] || mode;

    const scopeDesc = {
        'filename': '仅文件名',
        'content': '仅内容',
        'all': '全部'
    }[scope] || scope;

    const typeDesc = {
        'all': '所有类型',
        'md': 'Markdown',
        'image': '图片',
        'pdf': 'PDF',
        'other': '其他'
    }[fileType] || fileType;

    const sortDesc = {
        'name': '按名称',
        'modified': '按修改时间',
        'size': '按大小',
        'relevance': '按相关性'
    }[sortBy] || sortBy;

    let html = `
        <div style="margin-bottom: 12px; padding: 10px; background: var(--background-secondary); border-radius: 4px; border-left: 3px solid var(--interactive-accent);">
            <strong style="font-size: 14px;">🎯 找到 ${results.length} 个匹配文件</strong><br>
            <span style="color: var(--text-muted); font-size: 11px;">
                关键词: ${keywords.join(', ')} | 模式: ${modeDesc} | 范围: ${scopeDesc}<br>
                类型: ${typeDesc} | 排序: ${sortDesc}
            </span>
        </div>
    `;
    
    results.forEach((result, index) => {
        const modifiedDate = new Date(result.mtime).toLocaleDateString('zh-CN');
        const pathParts = result.path.split('/');
        const directory = pathParts.length > 1 ? pathParts[0] : '根目录';
        const fileSize = formatFileSize(result.size);
        const fileIcon = getFileIcon(result.fileExt);

        // 关键修复：使用onclick事件而不是href链接
        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 6px; margin-bottom: 10px; padding: 12px; background: var(--background-secondary); transition: all 0.2s ease;"
                 onmouseover="this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'; this.style.transform='translateY(-1px)';"
                 onmouseout="this.style.boxShadow='none'; this.style.transform='translateY(0)';">

                <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 8px;">
                    <div style="flex: 1;">
                        <span style="color: var(--link-color); font-weight: bold; cursor: pointer; text-decoration: underline; font-size: 14px;"
                              onclick="safeOpenFile('${result.path}')">
                            ${fileIcon} ${result.name}
                        </span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 6px;">
                        ${sortBy === 'relevance' ? `<span style="background: var(--interactive-accent); color: white; padding: 1px 6px; border-radius: 8px; font-size: 10px; font-weight: bold;">${result.relevanceScore}分</span>` : ''}
                        <span style="color: var(--text-muted); font-size: 10px;">#${index + 1}</span>
                    </div>
                </div>

                <div style="display: flex; flex-wrap: wrap; gap: 8px; font-size: 11px; color: var(--text-muted);">
                    <span>📁 ${directory}</span>
                    <span>📅 ${modifiedDate}</span>
                    <span>📊 ${fileSize}</span>
                    <span>🔍 ${result.matchType || '文件名'}</span>
                </div>
            </div>
        `;
    });
    
    resultContent.innerHTML = html;
}

// 显示搜索历史
function showSearchHistory() {
    if (searchHistory.length === 0) {
        resultContent.innerHTML = `
            <div style="text-align: center; color: var(--text-muted); padding: 30px;">
                <div style="font-size: 36px; margin-bottom: 8px;">📚</div>
                <div style="font-size: 14px;">暂无搜索历史</div>
                <div style="font-size: 11px; margin-top: 5px;">开始搜索后会显示历史记录</div>
            </div>
        `;
        return;
    }

    let html = `
        <div style="margin-bottom: 12px; padding: 10px; background: var(--background-secondary); border-radius: 4px; border-left: 3px solid var(--color-blue);">
            <strong style="font-size: 14px;">📚 搜索历史</strong>
            <span style="margin-left: 10px; color: var(--text-muted); font-size: 11px;">最近 ${searchHistory.length} 条记录</span>
        </div>
    `;

    searchHistory.forEach((item, index) => {
        html += `
            <div style="border: 1px solid var(--background-modifier-border); border-radius: 6px; margin-bottom: 8px; padding: 10px; background: var(--background-secondary); cursor: pointer;"
                 onclick="restoreSearch('${item.keyword}', '${item.mode}', '${item.scope}', '${item.fileType}', '${item.sortBy}')"
                 onmouseover="this.style.boxShadow='0 2px 6px rgba(0,0,0,0.1)'"
                 onmouseout="this.style.boxShadow='none'">
                <div style="font-weight: bold; margin-bottom: 4px; color: var(--text-normal); font-size: 13px;">
                    🔍 "${item.keyword}"
                </div>
                <div style="font-size: 10px; color: var(--text-muted); line-height: 1.3;">
                    ${item.date} | ${item.mode}模式 | ${item.scope}范围 | ${item.fileType}类型 | ${item.sortBy}排序 | ${item.resultCount}个结果
                </div>
            </div>
        `;
    });

    resultContent.innerHTML = html;
}

// 显示搜索统计
function showSearchStats() {
    const totalSearches = searchHistory.length;
    const uniqueKeywords = new Set(searchHistory.map(item => item.keyword)).size;
    const avgResults = totalSearches > 0 ? Math.round(searchHistory.reduce((sum, item) => sum + item.resultCount, 0) / totalSearches) : 0;

    // 热门关键词 Top 10
    const topKeywords = Object.entries(searchStats)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 10);

    // 最近搜索趋势
    const recentSearches = searchHistory.slice(0, 5);

    let html = `
        <div style="margin-bottom: 15px; padding: 12px; background: var(--background-secondary); border-radius: 6px; border-left: 3px solid var(--color-green);">
            <strong style="font-size: 14px;">📊 搜索统计</strong>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin-bottom: 15px;">
            <div style="background: var(--background-secondary); padding: 10px; border-radius: 4px; text-align: center;">
                <div style="font-size: 20px; font-weight: bold; color: var(--interactive-accent);">${totalSearches}</div>
                <div style="font-size: 11px; color: var(--text-muted);">总搜索次数</div>
            </div>
            <div style="background: var(--background-secondary); padding: 10px; border-radius: 4px; text-align: center;">
                <div style="font-size: 20px; font-weight: bold; color: var(--color-blue);">${uniqueKeywords}</div>
                <div style="font-size: 11px; color: var(--text-muted);">不同关键词</div>
            </div>
            <div style="background: var(--background-secondary); padding: 10px; border-radius: 4px; text-align: center;">
                <div style="font-size: 20px; font-weight: bold; color: var(--color-green);">${avgResults}</div>
                <div style="font-size: 11px; color: var(--text-muted);">平均结果数</div>
            </div>
        </div>
    `;

    if (topKeywords.length > 0) {
        html += `
            <div style="margin-bottom: 15px; padding: 10px; background: var(--background-secondary); border-radius: 4px;">
                <h4 style="margin: 0 0 8px 0; font-size: 13px; color: var(--text-normal);">🔥 热门关键词</h4>
        `;

        topKeywords.forEach(([keyword, count], index) => {
            const percentage = Math.round((count / Math.max(...Object.values(searchStats))) * 100);
            html += `
                <div style="margin-bottom: 6px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; font-size: 11px;">
                        <span style="cursor: pointer; color: var(--link-color);" onclick="selectSuggestion('${keyword}')">${index + 1}. ${keyword}</span>
                        <span style="color: var(--text-muted);">${count}次</span>
                    </div>
                    <div style="background: var(--background-modifier-border); height: 3px; border-radius: 2px; overflow: hidden;">
                        <div style="background: var(--interactive-accent); height: 100%; width: ${percentage}%; transition: width 0.3s ease;"></div>
                    </div>
                </div>
            `;
        });

        html += `</div>`;
    }

    if (recentSearches.length > 0) {
        html += `
            <div style="padding: 10px; background: var(--background-secondary); border-radius: 4px;">
                <h4 style="margin: 0 0 8px 0; font-size: 13px; color: var(--text-normal);">⏰ 最近搜索</h4>
        `;

        recentSearches.forEach(item => {
            html += `
                <div style="margin-bottom: 4px; padding: 4px 6px; background: var(--background-primary); border-radius: 3px; cursor: pointer;"
                     onclick="restoreSearch('${item.keyword}', '${item.mode}', '${item.scope}', '${item.fileType}', '${item.sortBy}')">
                    <span style="font-size: 11px; color: var(--text-normal);">${item.keyword}</span>
                    <span style="font-size: 10px; color: var(--text-muted); margin-left: 8px;">${item.date}</span>
                </div>
            `;
        });

        html += `</div>`;
    }

    resultContent.innerHTML = html;
}

// 恢复搜索设置
function restoreSearch(keyword, mode, scope, fileType, sortBy) {
    searchInput.value = keyword;
    modeSelect.value = mode;
    scopeSelect.value = scope;
    typeSelect.value = fileType;
    sortSelect.value = sortBy;
    performMVPSearch();
}

// 清空结果
function clearMVPResults() {
    searchInput.value = '';
    modeSelect.value = 'OR';
    scopeSelect.value = 'filename';
    typeSelect.value = 'all';
    sortSelect.value = 'name';
    suggestionsDiv.style.display = 'none';
    statusDiv.textContent = 'MVP搜索系统 v1.3 准备就绪 - 支持智能建议和搜索历史';
    resultContent.innerHTML = `
        <div style="text-align: center; color: var(--text-muted); padding: 30px;">
            <div style="font-size: 36px; margin-bottom: 8px;">🔧</div>
            <div style="font-size: 14px;">MVP搜索系统 v1.3 准备就绪</div>
            <div style="font-size: 11px; margin-top: 5px;">支持智能建议和搜索历史</div>
        </div>
    `;
}

// 全局函数（确保onclick可以访问）
window.safeOpenFile = safeOpenFile;
window.selectSuggestion = selectSuggestion;
window.restoreSearch = restoreSearch;

// 搜索建议防抖
let suggestionTimeout;
function handleSearchInput() {
    clearTimeout(suggestionTimeout);
    suggestionTimeout = setTimeout(() => {
        const value = searchInput.value.trim();
        if (value.length >= 2) {
            showSearchSuggestions(value);
        } else {
            suggestionsDiv.style.display = 'none';
        }
    }, 300);
}

// 事件绑定
setTimeout(() => {
    if (searchBtn) searchBtn.onclick = performMVPSearch;
    if (clearBtn) clearBtn.onclick = clearMVPResults;
    if (historyBtn) historyBtn.onclick = showSearchHistory;
    if (statsBtn) statsBtn.onclick = showSearchStats;

    if (searchInput) {
        searchInput.oninput = handleSearchInput;
        searchInput.onkeypress = function(e) {
            if (e.key === 'Enter') {
                suggestionsDiv.style.display = 'none';
                performMVPSearch();
            } else if (e.key === 'Escape') {
                suggestionsDiv.style.display = 'none';
            }
        };

        searchInput.onfocus = function() {
            if (searchInput.value.length >= 2) {
                showSearchSuggestions(searchInput.value);
            }
        };
    }

    // 点击外部隐藏建议
    document.addEventListener('click', function(e) {
        if (!searchContainer.contains(e.target)) {
            suggestionsDiv.style.display = 'none';
        }
    });
}, 100);
```

## 🔧 问题诊断与修复

### **根本原因分析**
1. **HTML链接冲突**：使用`<a href="${result.link}">`与Obsidian内部路由冲突
2. **API调用错误**：`page.file.link`生成的链接格式不适合HTML直接使用
3. **事件处理冲突**：dataviewjs环境中的链接点击与Obsidian核心功能冲突

### **关键修复措施**
1. ✅ **移除HTML链接**：完全避免使用`<a href="">`
2. ✅ **使用onclick事件**：通过JavaScript安全打开文件
3. ✅ **Obsidian API调用**：使用`app.workspace.openLinkText()`
4. ✅ **简化DOM结构**：减少内存泄漏风险
5. ✅ **限制结果数量**：避免过载导致的性能问题

### **测试验证步骤**
1. 打开 `notes/MVP搜索系统.md`
2. 输入关键词（如"任务"）
3. 点击搜索按钮
4. **关键测试**：点击搜索结果中的文件名
5. 验证文件是否正常打开，Obsidian是否保持稳定

### **备选方案**
如果MVP版本仍有问题，可以：
1. 使用纯文本显示文件路径，用户手动复制
2. 集成到Obsidian的命令面板中
3. 使用第三方搜索插件

---

*🔧 这个MVP版本专门解决崩溃问题，确保基本功能稳定工作！*
