@echo off
chcp 65001 >nul
echo ========================================
echo 照片替换工具 v1.1 使用示例
echo ========================================
echo.

echo 请将此批处理文件放在包含多个客户文件夹的父目录中
echo 或者修改下面的路径为您的实际路径
echo.

echo 当前目录: %CD%
echo.

echo 开始执行照片替换...
echo.

REM 使用当前目录作为父文件夹路径
python "%~dp0batch_photo_replacer.py" --parent "%CD%"

echo.
echo ========================================
echo 处理完成！
echo ========================================
pause
