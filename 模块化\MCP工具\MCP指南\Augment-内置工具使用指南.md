# Augment 内置工具使用指南

## 网页内容获取替代方案

由于fetch MCP在Augment中存在schema兼容性问题，以下是几个有效的替代方案：

### 方案1：使用内置web-fetch工具

Augment IDE内置了web-fetch功能，无需额外配置：

**使用方法：**
```
请帮我获取 https://example.com 的内容
```

**功能特点：**
- 无需MCP配置
- 直接可用
- 自动转换为markdown格式

### 方案2：使用Playwright MCP（推荐）

您已经配置了Playwright MCP，它也可以获取网页内容：

**使用方法：**
```
请使用playwright访问 https://example.com 并获取页面内容
```

**优势：**
- 支持JavaScript渲染
- 可以处理动态内容
- 功能更强大

### 方案3：使用web-search + web-fetch组合

**步骤：**
1. 先搜索：`请搜索关于某个主题的信息`
2. 再获取：`请获取搜索结果中第一个链接的详细内容`

## 实际测试示例

### 测试1：基础网页获取
```
请获取 https://httpbin.org/html 的内容
```

### 测试2：新闻网站
```
请获取 https://example-news.com 的最新文章内容
```

### 测试3：技术文档
```
请获取 https://docs.python.org 首页的内容
```

## 功能对比

| 功能 | 内置web-fetch | Playwright MCP | fetch MCP |
|------|---------------|----------------|-----------|
| 基础网页获取 | ✅ | ✅ | ❌(兼容性问题) |
| JavaScript支持 | ❌ | ✅ | ❌ |
| 配置复杂度 | 无需配置 | 已配置 | 配置失败 |
| 稳定性 | 高 | 高 | 低(Augment中) |

## 推荐使用策略

1. **日常网页获取**：使用内置web-fetch
2. **复杂网页**：使用Playwright MCP
3. **搜索+获取**：使用web-search + web-fetch组合

## 注意事项

- 内置工具可能有访问限制
- Playwright功能更强但消耗更多资源
- 建议根据具体需求选择合适的工具

---

**结论**：虽然fetch MCP在Augment中有兼容性问题，但您有多个有效的替代方案可以实现相同功能。
